// Script to test organization membership webhook events
const fetch = require('node-fetch');
const crypto = require('crypto');

const WEBHOOK_URL = 'https://efficient-toucan-547.convex.cloud/http/workos-webhook';
const WEBHOOK_SECRET = 'test_secret_for_development';

// Create a test webhook payload for organization membership
const createMembershipPayload = (eventType, membershipData) => {
  const timestamp = Date.now();
  const payload = {
    event: eventType,
    data: membershipData,
    created_at: new Date().toISOString()
  };
  
  const payloadString = JSON.stringify(payload);
  
  // Create signature according to WorkOS format
  const signatureString = timestamp + '.' + payloadString;
  const signature = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(signatureString, 'utf8')
    .digest('hex');
  
  const workosSignature = `t=${timestamp},v1=${signature}`;
  
  return {
    payload: payloadString,
    signature: workosSignature,
    timestamp
  };
};

async function testMembershipEvent(eventType, membershipData) {
  try {
    console.log(`\n=== Testing ${eventType} ===`);
    
    const testData = createMembershipPayload(eventType, membershipData);
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'WorkOS-Signature': testData.signature,
        'User-Agent': 'WorkOS-Webhook/1.0'
      },
      body: testData.payload
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      console.log(`✅ ${eventType}: SUCCESS`);
    } else {
      console.log(`❌ ${eventType}: FAILED (${response.status})`);
      console.log(`Response: ${responseText}`);
    }
    
    return response.ok;
    
  } catch (error) {
    console.error(`❌ ${eventType}: ERROR -`, error.message);
    return false;
  }
}

async function testMembershipWebhooks() {
  console.log('Testing organization membership webhook events...');
  
  // Test data based on the real membership we found
  const membershipData = {
    id: 'om_01JYPKN99FB1TXKGHFQZJ7Z6Q5',
    object: 'organization_membership',
    organization_id: 'org_01JM086NJQP34JDKMG0Z3K2ATR',
    organization_name: 'Test Organization',
    user_id: 'user_01JYPKN98F8DRBXB1STS5KFSMT',
    status: 'active',
    role: {
      slug: 'accounts'
    },
    created_at: '2025-06-26T17:13:11.158Z',
    updated_at: new Date().toISOString()
  };
  
  const testCases = [
    {
      event: 'organization_membership.created',
      data: membershipData
    },
    {
      event: 'organization_membership.updated',
      data: {
        ...membershipData,
        role: { slug: 'shop_support' }, // Test role change
        updated_at: new Date().toISOString()
      }
    },
    {
      event: 'organization_membership.updated',
      data: {
        ...membershipData,
        role: { slug: 'accounts' }, // Change back to accounts
        updated_at: new Date().toISOString()
      }
    }
  ];
  
  let successCount = 0;
  
  for (const testCase of testCases) {
    const success = await testMembershipEvent(testCase.event, testCase.data);
    if (success) successCount++;
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log(`\n=== Test Summary ===`);
  console.log(`✅ ${successCount}/${testCases.length} membership events processed successfully`);
  
  if (successCount === testCases.length) {
    console.log('🎉 All organization membership webhooks are working correctly!');
  } else {
    console.log('⚠️ Some membership webhook events failed. Check the logs for details.');
  }
}

testMembershipWebhooks();