#!/bin/bash

# Run Convex dev once to ensure deployment is up-to-date
echo "Initializing Convex deployment..."
npx convex dev --once

# Check if Convex deployment was successful
if [ $? -ne 0 ]; then
  echo "❌ Convex deployment failed. Please check your network connection and try again."
  exit 1
fi

echo "✅ Convex deployment successful!"

# Start Vite development server
echo "Starting Vite development server..."
npx vite

echo "Development servers stopped."