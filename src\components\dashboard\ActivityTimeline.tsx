/**
 * ActivityTimeline Component
 *
 * This component displays activities grouped by request, showing the complete lifecycle
 * of each request with activities in chronological order within each group.
 *
 * Layout:
 * - Each request is displayed as a group with its title, amount, shop, and area
 * - Activities within each request are shown chronologically (oldest to newest)
 * - Request groups are ordered by most recent activity (newest first)
 * - Dividers separate different request groups
 *
 * Activity Flow per Request:
 * 1. Request Created - When a shop manager creates a request
 * 2. Request Approved/Rejected - When admin takes action
 * 3. Request Resubmitted - When shop manager resubmits after rejection
 * 4. Request Approved (again) - When admin approves the resubmitted request
 *
 * Features:
 * - Grouped by request with clear visual separation
 * - Pagination for performance with large datasets
 * - Payment method labels (mobile money/bank transfer)
 * - Actor information (who performed each action)
 * - Smooth scrolling on page changes
 * - Responsive design
 */

import React, { useState, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  ClipboardPen,
  Smartphone,
  Building2,
  MapPin,
  Store
} from 'lucide-react';
import { format } from 'date-fns';
import { Request } from '@/lib/types';
import { Skeleton } from '@/components/ui/skeleton';
import { formatAmount } from '@/lib/currency';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from '@/components/ui/pagination';

interface ActivityTimelineProps {
  requests: Request[];
  isLoading?: boolean;
  itemsPerPage?: number;
  showPagination?: boolean;
  dateFilter?: { from?: Date; to?: Date };
}

interface Activity {
  id: string;
  type: 'created' | 'approved' | 'rejected' | 'resubmitted';
  timestamp: Date;
  actor: string; // Who performed the action
  actorRole: 'shop_manager' | 'admin';
  reason?: string; // Approval/rejection reason
  changes?: Record<string, { from: any; to: any }>; // For resubmissions
  notes?: string; // Resubmission notes
}

interface RequestActivityGroup {
  request: Request;
  activities: Activity[];
  lastActivityDate: Date;
}

const ActivityTimeline: React.FC<ActivityTimelineProps> = React.memo(({
  requests,
  isLoading = false,
  itemsPerPage = 10,
  showPagination = true,
  dateFilter
}) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);

  // Date filtering helper function
  const isDateInRange = (requestDate: Date, fromDate?: Date, toDate?: Date): boolean => {
    if (!fromDate || !toDate) return true;

    // Normalize dates to midnight local time for accurate day-based comparison
    const normalizedRequestDate = new Date(requestDate.getFullYear(), requestDate.getMonth(), requestDate.getDate());
    const normalizedFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
    const normalizedToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate());

    return normalizedRequestDate >= normalizedFromDate && normalizedRequestDate <= normalizedToDate;
  };

  // Generate grouped activities by request
  const requestGroups: RequestActivityGroup[] = useMemo(() => {
    const groups: RequestActivityGroup[] = [];

    // Filter requests by date if dateFilter is provided
    const filteredRequests = dateFilter?.from && dateFilter?.to
      ? requests.filter(request => {
          const requestDate = new Date(request.createdAt);
          return isDateInRange(requestDate, dateFilter.from, dateFilter.to);
        })
      : requests;

    filteredRequests.forEach(request => {
      const activities: Activity[] = [];

      // 1. Request Created Activity
      activities.push({
        id: `${request._id}-created`,
        type: 'created',
        timestamp: new Date(request.createdAt),
        actor: request.createdByName,
        actorRole: 'shop_manager'
      });

      // 2. Resubmission Activities (if any)
      if (request.resubmissionHistory && request.resubmissionHistory.length > 0) {
        request.resubmissionHistory.forEach((resubmission, index) => {
          activities.push({
            id: `${request._id}-resubmitted-${index}`,
            type: 'resubmitted',
            timestamp: new Date(resubmission.timestamp),
            actor: request.createdByName,
            actorRole: 'shop_manager',
            changes: resubmission.changes,
            notes: resubmission.notes
          });
        });
      }

      // 3. Final Status Activities (Approved or Rejected)
      if (request.status === 'approved' && request.approvedAt && request.approvedByName) {
        activities.push({
          id: `${request._id}-approved`,
          type: 'approved',
          timestamp: new Date(request.approvedAt),
          actor: request.approvedByName,
          actorRole: 'admin',
          reason: request.approvalReason
        });
      } else if (request.status === 'rejected' && request.rejectedAt && request.rejectedByName) {
        activities.push({
          id: `${request._id}-rejected`,
          type: 'rejected',
          timestamp: new Date(request.rejectedAt),
          actor: request.rejectedByName,
          actorRole: 'admin',
          reason: request.rejectionReason
        });
      }

      // Sort activities within this request by timestamp (chronological order)
      activities.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

      // Find the most recent activity date for sorting groups
      const lastActivityDate = activities.length > 0
        ? activities[activities.length - 1].timestamp
        : new Date(request.createdAt);

      groups.push({
        request,
        activities,
        lastActivityDate
      });
    });

    // Sort groups by most recent activity (newest first)
    return groups.sort((a, b) => b.lastActivityDate.getTime() - a.lastActivityDate.getTime());
  }, [requests, dateFilter]);

  // Pagination logic for request groups
  const totalPages = Math.ceil(requestGroups.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentGroups = requestGroups.slice(startIndex, endIndex);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'created':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'rejected':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'resubmitted':
        return <ClipboardPen className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  const getActivityTitle = (activity: Activity) => {
    switch (activity.type) {
      case 'created':
        return 'Request Created';
      case 'approved':
        return 'Request Approved';
      case 'rejected':
        return 'Request Rejected';
      case 'resubmitted':
        return 'Request Resubmitted';
      default:
        return 'Activity';
    }
  };

  const getActivityDescription = (activity: Activity) => {
    switch (activity.type) {
      case 'created':
        return `${activity.actor} created this request`;
      case 'approved':
        return `${activity.actor} approved the request${activity.reason ? `: ${activity.reason}` : ''}`;
      case 'rejected':
        return `${activity.actor} rejected the request${activity.reason ? `: ${activity.reason}` : ''}`;
      case 'resubmitted':
        return `${activity.actor} resubmitted with changes${activity.notes ? `: ${activity.notes}` : ''}`;
      default:
        return '';
    }
  };

  const getStatusBadge = (type: string) => {
    switch (type) {
      case 'created':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Created</Badge>;
      case 'approved':
        return <Badge variant="secondary" className="bg-green-100 text-green-800">Approved</Badge>;
      case 'rejected':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Rejected</Badge>;
      case 'resubmitted':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Resubmitted</Badge>;
      default:
        return <Badge variant="secondary">Unknown</Badge>;
    }
  };



  const getPaymentMethodBadge = (paymentMethod: 'mobile_money' | 'bank_transfer') => {
    if (paymentMethod === 'mobile_money') {
      return (
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Smartphone className="h-3 w-3 mr-1" />
          Mobile Money
        </Badge>
      );
    }
    return (
      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
        <Building2 className="h-3 w-3 mr-1" />
        Bank Transfer
      </Badge>
    );
  };

  const getActivityIconBg = (type: string) => {
    switch (type) {
      case 'created':
        return 'bg-blue-500';
      case 'approved':
        return 'bg-green-500';
      case 'rejected':
        return 'bg-red-500';
      case 'resubmitted':
        return 'bg-amber-500';
      default:
        return 'bg-gray-400';
    }
  };

  const getReasonBgColor = (type: string) => {
    switch (type) {
      case 'approved':
        return 'bg-green-50 border-green-200';
      case 'rejected':
        return 'bg-red-50 border-red-200';
      case 'resubmitted':
        return 'bg-amber-50 border-amber-200';
      default:
        return 'bg-gray-50 border-gray-200';
    }
  };

  const getActivityTagColor = (type: string) => {
    switch (type) {
      case 'created':
        return 'bg-blue-100 text-blue-700';
      case 'approved':
        return 'bg-green-100 text-green-700';
      case 'rejected':
        return 'bg-red-100 text-red-700';
      case 'resubmitted':
        return 'bg-amber-100 text-amber-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    // Scroll to top of timeline when page changes
    const timelineElement = document.querySelector('[data-timeline-container]');
    if (timelineElement) {
      timelineElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Activity Timeline</CardTitle>
          <p className="text-sm text-muted-foreground">Recent system activities</p>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(6)].map((_, i) => (
              <div key={i} className="flex items-start space-x-4 animate-pulse">
                <Skeleton className="w-8 h-8 rounded-full" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-1/2" />
                  <div className="flex gap-2">
                    <Skeleton className="h-5 w-16" />
                    <Skeleton className="h-5 w-20" />
                  </div>
                </div>
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div data-timeline-container>
      {currentGroups.length > 0 ? (
        <>
          <div className="space-y-8">
            {currentGroups.map((group, groupIndex) => (
              <div key={group.request._id} className="relative">
                {/* Request Header */}
                <div className="mb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {group.request.title}
                      </h3>
                      <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4 text-blue-600" />
                          <span className="text-blue-600">{group.request.areaName}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Store className="h-4 w-4 text-purple-600" />
                          <span className="text-purple-600">{group.request.shopName}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span className="font-medium">{formatAmount(group.request.amount)}</span>
                        </div>
                        {getPaymentMethodBadge(group.request.paymentMethod)}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Activities for this request */}
                <div className="relative ml-4">
                  {/* Continuous timeline line */}
                  <div className="absolute left-[6px] top-0 bottom-0 w-0.5 bg-gray-200"></div>

                  <div className="space-y-0">
                    {group.activities.map((activity, activityIndex) => (
                      <div key={activity.id} className="relative flex items-start gap-4 pb-4">
                        <div className={`relative z-10 w-3 h-3 rounded-full ${getActivityIconBg(activity.type)}`}></div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <span className="text-sm font-medium text-gray-900">{getActivityTitle(activity)}</span>
                            <span className="text-xs text-gray-400">{format(activity.timestamp, 'MMM dd')}</span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">{activity.actor}</p>
                          {activity.reason && (
                            <p className="text-xs text-gray-600 mt-2 italic">"{activity.reason}"</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Divider between request groups */}
                {groupIndex < currentGroups.length - 1 && (
                  <div className="mt-8 border-t border-gray-200"></div>
                )}
              </div>
            ))}
          </div>

          {/* Pagination */}
          {showPagination && totalPages > 1 && (
            <div className="mt-6 flex justify-center">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                      className={currentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>

                  {[...Array(totalPages)].map((_, i) => {
                    const page = i + 1;
                    if (
                      page === 1 ||
                      page === totalPages ||
                      (page >= currentPage - 1 && page <= currentPage + 1)
                    ) {
                      return (
                        <PaginationItem key={page}>
                          <PaginationLink
                            onClick={() => handlePageChange(page)}
                            isActive={currentPage === page}
                            className="cursor-pointer"
                          >
                            {page}
                          </PaginationLink>
                        </PaginationItem>
                      );
                    }
                    return null;
                  })}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                      className={currentPage === totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </>
      ) : (
        <div className="flex flex-col items-center justify-center h-[300px] text-center">
          <div className="rounded-full bg-gray-100 p-3 mb-3">
            <Clock className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900">No activities yet</h3>
          <p className="text-sm text-gray-500 mt-1">
            System activities will appear here as they happen
          </p>
        </div>
      )}
    </div>
  );
});

export default ActivityTimeline;
