import { convexAuth } from "@convex-dev/auth/server";
import Work<PERSON> from "@auth/core/providers/workos";
import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { getCurrentUserWithPermissions } from "./permissions";

const workosConfigured =
  process.env.WORKOS_API_KEY &&
  process.env.WORKOS_CLIENT_ID &&
  process.env.WORKOS_DOMAIN;

// Log all environment variables for debugging
console.log("WorkOS Environment Variables:");
console.log("- API_KEY:", process.env.WORKOS_API_KEY ? "Set (hidden)" : "Not set");
console.log("- CLIENT_ID:", process.env.WORKOS_CLIENT_ID);
console.log("- DOMAIN:", process.env.WORKOS_DOMAIN);
console.log("- CONNECTION_ID:", process.env.WORKOS_CONNECTION_ID);
console.log("- REDIRECT_URI:", process.env.WORKOS_REDIRECT_URI);

const providers = [];
if (workosConfigured) {
  console.log("WorkOS Auth provider configured.");
  providers.push(
    WorkOS({
      connection: process.env.WORKOS_CONNECTION_ID,
      domain: process.env.WORKOS_DOMAIN!,
      clientId: process.env.WORKOS_CLIENT_ID!,
      clientSecret: process.env.WORKOS_API_KEY!,
      redirectUri: process.env.WORKOS_REDIRECT_URI,
    })
  );
} else {
  console.warn(
    "WorkOS environment variables not set. Skipping WorkOS auth provider. Authentication will not work."
  );
  console.warn("Required: WORKOS_API_KEY, WORKOS_CLIENT_ID, WORKOS_DOMAIN");
}

export const { auth, signIn, signOut, store } = convexAuth({
  providers,
  callbacks: {
    async createOrUpdateUser(ctx, args) {
      // This callback is called when a user signs in
      const { existingUserId, ...profile } = args;
      
      if (existingUserId) {
        // Update existing user
        await ctx.db.patch(existingUserId, {
          email: profile.email,
          firstName: profile.name?.split(' ')[0],
          lastName: profile.name?.split(' ').slice(1).join(' '),
          profilePicture: profile.pictureUrl,
          updatedAt: Date.now(),
        });
        return existingUserId;
      } else {
        // Create new user
        const now = Date.now();
        const userId = await ctx.db.insert("users", {
          workosId: profile.id,
          email: profile.email!,
          firstName: profile.name?.split(' ')[0] || "User",
          lastName: profile.name?.split(' ').slice(1).join(' ') || "",
          profilePicture: profile.pictureUrl,
          isActive: true,
          createdAt: now,
          updatedAt: now,
        });
        
        // Check for pending invitation
        const invitation = await ctx.db
          .query("invitations")
          .withIndex("by_email", (q) => q.eq("email", profile.email!))
          .filter((q) => q.eq(q.field("status"), "pending"))
          .first();
          
        if (invitation) {
          // Accept invitation and assign role
          await ctx.db.patch(invitation._id, {
            status: "accepted",
            acceptedAt: now,
            acceptedBy: userId,
          });
          
          await ctx.db.insert("user_roles", {
            userId,
            roleId: invitation.roleId,
            assignedBy: invitation.invitedBy,
            assignedAt: now,
            isActive: true,
          });
          
          if (invitation.areaIds) {
            for (const areaId of invitation.areaIds) {
              await ctx.db.insert("user_area_assignments", {
                userId,
                areaId,
                assignedBy: invitation.invitedBy,
                assignedAt: now,
                isActive: true,
              });
            }
          }
        }
        
        return userId;
      }
    },
  },
});

export const getSessionInfo = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    return {
      userId: identity.subject,
      email: identity.email,
      name: identity.name,
      picture: identity.pictureUrl,
    };
  },
});

export const getCurrentUser = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    const user = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", identity.subject))
      .unique();
    return user;
  },
});

export const getCurrentUserWithRoles = query({
  args: {
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    return await getCurrentUserWithPermissions(ctx, args.workosUserId);
  },
});

export const syncUser = mutation({
  args: {
    workosId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    profilePicture: v.optional(v.string()),
    roleNames: v.optional(v.array(v.string())),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", args.workosId))
      .unique();

    let userId: any;

    if (existingUser) {
      await ctx.db.patch(existingUser._id, {
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        profilePicture: args.profilePicture,
        updatedAt: now,
      });
      userId = existingUser._id;
    } else {
      userId = await ctx.db.insert("users", {
        workosId: args.workosId,
        email: args.email,
        firstName: args.firstName,
        lastName: args.lastName,
        profilePicture: args.profilePicture,
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      
      // Check for pending invitation
      const invitation = await ctx.db
        .query("invitations")
        .withIndex("by_email", (q) => q.eq("email", args.email))
        .filter((q) => q.eq(q.field("status"), "pending"))
        .first();
      if (invitation) {
        await ctx.db.patch(invitation._id, {
          status: "accepted",
          acceptedAt: now,
          acceptedBy: userId,
        });
        await ctx.db.insert("user_roles", {
          userId,
          roleId: invitation.roleId,
          assignedBy: invitation.invitedBy,
          assignedAt: now,
          isActive: true,
        });
        if (invitation.areaIds) {
          for (const areaId of invitation.areaIds) {
            await ctx.db.insert("user_area_assignments", {
              userId,
              areaId,
              assignedBy: invitation.invitedBy,
              assignedAt: now,
              isActive: true,
            });
          }
        }
      }
    }

    // Sync roles from WorkOS if provided
    if (args.roleNames && args.roleNames.length > 0) {
      // Get all roles that match the role names
      const roles = await ctx.db.query("roles").collect();
      const matchingRoles = roles.filter(role => 
        args.roleNames!.includes(role.name) && role.isActive
      );
      
      if (matchingRoles.length > 0) {
        // Deactivate existing role assignments for this user
        const existingUserRoles = await ctx.db
          .query("user_roles")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", userId).eq("isActive", true)
          )
          .collect();
        
        for (const userRole of existingUserRoles) {
          await ctx.db.patch(userRole._id, { isActive: false });
        }
        
        // Create new role assignments
        for (const role of matchingRoles) {
          await ctx.db.insert("user_roles", {
            userId,
            roleId: role._id,
            assignedBy: userId, // Self-assignment from WorkOS sync
            assignedAt: now,
            isActive: true,
          });
        }
      }
    }

    return userId;
  },
});

export const isAuthenticated = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    return !!identity;
  },
});

export const getAuthConfiguredProviders = query({
  args: {},
  handler: async () => {
    const configuredProviders = [];
    if (workosConfigured) {
      configuredProviders.push("workos");
    }
    return configuredProviders;
  },
});
