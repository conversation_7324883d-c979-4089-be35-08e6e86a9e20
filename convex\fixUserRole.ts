import { mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Manual role assignment fix for users
 */
export const assignRoleToUser = mutation({
  args: {
    userEmail: v.string(),
    roleName: v.string(),
  },
  handler: async (ctx, args) => {
    console.log(`Assigning role ${args.roleName} to user ${args.userEmail}`);
    
    // Find user by email
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.userEmail))
      .unique();
    
    if (!user) {
      throw new Error(`User not found: ${args.userEmail}`);
    }
    
    // Find role by name
    const role = await ctx.db
      .query("roles")
      .withIndex("by_name", (q) => q.eq("name", args.roleName))
      .unique();
    
    if (!role) {
      throw new Error(`Role not found: ${args.roleName}`);
    }
    
    // Check if role assignment already exists
    const existingAssignment = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", user._id).eq("isActive", true)
      )
      .filter((q) => q.eq(q.field("roleId"), role._id))
      .unique();
    
    if (existingAssignment) {
      console.log("Role assignment already exists");
      return {
        success: true,
        message: "Role assignment already exists",
        userRoleId: existingAssignment._id
      };
    }
    
    // Create role assignment
    const now = Date.now();
    const userRoleId = await ctx.db.insert("user_roles", {
      userId: user._id,
      roleId: role._id,
      assignedBy: user._id, // Self-assigned for now
      assignedAt: now,
      isActive: true,
    });
    
    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "role_assigned_manual_fix",
      entityType: "user_role",
      entityId: userRoleId,
      userId: user._id,
      metadata: {
        userEmail: args.userEmail,
        roleName: args.roleName,
        source: "manual_fix",
        reason: "workos_sync_missing"
      },
      timestamp: now,
    });
    
    console.log(`Successfully assigned role ${args.roleName} to user ${args.userEmail}`);
    
    return {
      success: true,
      message: `Successfully assigned role ${args.roleName} to user ${args.userEmail}`,
      userRoleId,
      userId: user._id,
      roleId: role._id
    };
  },
});

/**
 * Sync role from WorkOS organization membership
 */
export const syncRoleFromWorkOS = mutation({
  args: {
    userEmail: v.string(),
    workosRoleSlug: v.string(),
    organizationMembershipId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log(`Syncing WorkOS role ${args.workosRoleSlug} for user ${args.userEmail}`);
    
    // Find user by email
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.userEmail))
      .unique();
    
    if (!user) {
      throw new Error(`User not found: ${args.userEmail}`);
    }
    
    // Find role by name (WorkOS role slug should match our role name)
    const role = await ctx.db
      .query("roles")
      .withIndex("by_name", (q) => q.eq("name", args.workosRoleSlug))
      .unique();
    
    if (!role) {
      throw new Error(`Role not found: ${args.workosRoleSlug}`);
    }
    
    // Check if role assignment already exists
    const existingAssignment = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", user._id).eq("isActive", true)
      )
      .filter((q) => q.eq(q.field("roleId"), role._id))
      .unique();
    
    if (existingAssignment) {
      console.log("Role assignment already exists");
      return {
        success: true,
        message: "Role assignment already exists",
        userRoleId: existingAssignment._id
      };
    }
    
    // Create role assignment
    const now = Date.now();
    const userRoleId = await ctx.db.insert("user_roles", {
      userId: user._id,
      roleId: role._id,
      assignedBy: user._id, // System assigned
      assignedAt: now,
      isActive: true,
    });
    
    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "role_synced_from_workos",
      entityType: "user_role",
      entityId: userRoleId,
      userId: user._id,
      metadata: {
        userEmail: args.userEmail,
        workosRoleSlug: args.workosRoleSlug,
        organizationMembershipId: args.organizationMembershipId,
        source: "workos_sync"
      },
      timestamp: now,
    });
    
    console.log(`Successfully synced role ${args.workosRoleSlug} from WorkOS for user ${args.userEmail}`);
    
    return {
      success: true,
      message: `Successfully synced role ${args.workosRoleSlug} from WorkOS`,
      userRoleId,
      userId: user._id,
      roleId: role._id
    };
  },
});