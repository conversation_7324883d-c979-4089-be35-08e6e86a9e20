import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  requireAuth,
  requirePermission,
  requirePermissionWithWorkOSUser,
  requireAnyPermission,
  requireAnyPermissionWithWorkOSUser,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  ROLES,
  canAccessArea,
  getAccessibleAreaIds,
} from "./permissions";

// Get areas with role-based filtering
export const getAreas = query({
  args: {
    isActive: v.optional(v.boolean()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    // Use WorkOS authentication if workosUserId is provided
    const userWithPermissions = args.workosUserId 
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.AREA_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.AREA_VIEW);

    let query = ctx.db.query("areas");

    // Apply role-based filtering
    if (!userWithPermissions.permissions.includes(PERMISSIONS.AREA_VIEW)) {
      // If user doesn't have general area view permission, they can only see their assigned areas
      const accessibleAreaIds = await getAccessibleAreaIds(ctx);
      if (accessibleAreaIds.length === 0) {
        return [];
      }
      query = query.filter((q) => 
        q.or(...accessibleAreaIds.map(areaId => q.eq(q.field("_id"), areaId)))
      );
    }

    // Apply additional filters
    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    const areas = await query
      .order("asc")
      .take(args.limit || 50);

    // Enrich with related data
    const enrichedAreas = await Promise.all(
      areas.map(async (area) => {
        const createdBy = await ctx.db.get(area.createdBy);

        // Get shop count for this area
        const shops = await ctx.db
          .query("shops")
          .withIndex("by_area_active", (q) => 
            q.eq("areaId", area._id).eq("isActive", true)
          )
          .collect();

        // Get assigned users count
        const userAssignments = await ctx.db
          .query("user_area_assignments")
          .withIndex("by_area_active", (q) => 
            q.eq("areaId", area._id).eq("isActive", true)
          )
          .collect();

        return {
          ...area,
          createdBy,
          shopCount: shops.length,
          assignedUserCount: userAssignments.length,
        };
      })
    );

    return enrichedAreas;
  },
});

// Get single area by ID
export const getArea = query({
  args: { 
    areaId: v.id("areas"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    // Use WorkOS authentication if workosUserId is provided
    if (args.workosUserId) {
      await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.AREA_VIEW, args.workosUserId);
    } else {
      await requirePermission(ctx, PERMISSIONS.AREA_VIEW);
    }

    const area = await ctx.db.get(args.areaId);
    if (!area) {
      return null;
    }

    // Check if user can access this area (accounts role has access to all areas)
    if (args.workosUserId) {
      // For WorkOS users, check if they have accounts role
      const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
      if (!userWithPermissions?.roles.some(role => role.name === 'accounts')) {
        const canAccess = await canAccessArea(ctx, args.areaId);
        if (!canAccess) {
          throw new Error("Access denied");
        }
      }
    } else {
      // For Convex Auth users, use the old method
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    // Enrich with related data
    const createdBy = await ctx.db.get(area.createdBy);

    // Get shops in this area
    const shops = await ctx.db
      .query("shops")
      .withIndex("by_area", (q) => q.eq("areaId", args.areaId))
      .collect();

    // Get assigned users
    const userAssignments = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_area_active", (q) => 
        q.eq("areaId", args.areaId).eq("isActive", true)
      )
      .collect();

    const assignedUsers = await Promise.all(
      userAssignments.map(async (assignment) => {
        const user = await ctx.db.get(assignment.userId);
        const assignedBy = await ctx.db.get(assignment.assignedBy);
        return {
          ...assignment,
          user,
          assignedBy,
        };
      })
    );

    return {
      ...area,
      createdBy,
      shops,
      assignedUsers,
    };
  },
});

// Create new area
export const createArea = mutation({
  args: {
    name: v.string(),
    description: v.optional(v.string()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermissionWithWorkOSUser(
      ctx, 
      PERMISSIONS.AREA_CREATE, 
      args.workosUserId
    );

    // Check for duplicate area name
    const existingArea = await ctx.db
      .query("areas")
      .withIndex("by_name", (q) => q.eq("name", args.name))
      .first();

    if (existingArea) {
      throw new Error("Area with this name already exists");
    }

    const now = Date.now();
    const areaId = await ctx.db.insert("areas", {
      name: args.name,
      description: args.description,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      createdBy: userWithPermissions.user._id,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "area_created",
      entityType: "area",
      entityId: areaId,
      userId: userWithPermissions.user._id,
      newValues: args,
      timestamp: now,
    });

    return areaId;
  },
});

// Update area
export const updateArea = mutation({
  args: {
    areaId: v.id("areas"),
    name: v.optional(v.string()),
    description: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.AREA_UPDATE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.AREA_UPDATE);

    const { areaId, workosUserId, ...updates } = args;
    const area = await ctx.db.get(areaId);
    if (!area) {
      throw new Error("Area not found");
    }

    // Check if user can access this area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccess = await canAccessArea(ctx, areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    // Check for duplicate area name (excluding current area)
    if (updates.name && updates.name !== area.name) {
      const existingArea = await ctx.db
        .query("areas")
        .withIndex("by_name", (q) => q.eq("name", updates.name))
        .filter((q) => q.neq(q.field("_id"), areaId))
        .first();

      if (existingArea) {
        throw new Error("Area with this name already exists");
      }
    }

    // If deactivating area, check for active shops
    if (updates.isActive === false && area.isActive) {
      const activeShops = await ctx.db
        .query("shops")
        .withIndex("by_area_active", (q) => 
          q.eq("areaId", areaId).eq("isActive", true)
        )
        .first();

      if (activeShops) {
        throw new Error("Cannot deactivate area with active shops");
      }

      // Check for pending requests
      const pendingRequests = await ctx.db
        .query("requests")
        .withIndex("by_area_status", (q) => 
          q.eq("areaId", areaId).eq("status", "pending")
        )
        .first();

      if (pendingRequests) {
        throw new Error("Cannot deactivate area with pending requests");
      }
    }

    const now = Date.now();
    await ctx.db.patch(areaId, {
      ...updates,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "area_updated",
      entityType: "area",
      entityId: areaId,
      userId: userWithPermissions.user._id,
      oldValues: area,
      newValues: updates,
      timestamp: now,
    });

    return areaId;
  },
});

// Delete area (soft delete)
export const deleteArea = mutation({
  args: { 
    areaId: v.id("areas"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.AREA_DELETE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.AREA_DELETE);

    const area = await ctx.db.get(args.areaId);
    if (!area) {
      throw new Error("Area not found");
    }

    // Check if user can access this area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    // Check for shops in this area
    const shops = await ctx.db
      .query("shops")
      .withIndex("by_area", (q) => q.eq("areaId", args.areaId))
      .first();

    if (shops) {
      throw new Error("Cannot delete area with shops");
    }

    // Check for pending requests
    const pendingRequests = await ctx.db
      .query("requests")
      .withIndex("by_area_status", (q) => 
        q.eq("areaId", args.areaId).eq("status", "pending")
      )
      .first();

    if (pendingRequests) {
      throw new Error("Cannot delete area with pending requests");
    }

    const now = Date.now();
    await ctx.db.patch(args.areaId, {
      isActive: false,
      updatedAt: now,
    });

    // Deactivate all user assignments for this area
    const userAssignments = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_area_active", (q) => 
        q.eq("areaId", args.areaId).eq("isActive", true)
      )
      .collect();

    for (const assignment of userAssignments) {
      await ctx.db.patch(assignment._id, {
        isActive: false,
      });
    }

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "area_deleted",
      entityType: "area",
      entityId: args.areaId,
      userId: userWithPermissions.user._id,
      oldValues: area,
      timestamp: now,
    });

    return args.areaId;
  },
});

// Get users assigned to area
export const getAreaUsers = query({
  args: { 
    areaId: v.id("areas"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.AREA_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.AREA_VIEW);

    // Check if user can access this area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    const userAssignments = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_area_active", (q) => 
        q.eq("areaId", args.areaId).eq("isActive", true)
      )
      .collect();

    const assignedUsers = await Promise.all(
      userAssignments.map(async (assignment) => {
        const [user, assignedBy] = await Promise.all([
          ctx.db.get(assignment.userId),
          ctx.db.get(assignment.assignedBy),
        ]);

        // Get user roles
        const userRoles = await ctx.db
          .query("user_roles")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", assignment.userId).eq("isActive", true)
          )
          .collect();

        const roles = await Promise.all(
          userRoles.map(async (userRole) => {
            const role = await ctx.db.get(userRole.roleId);
            return role;
          })
        );

        return {
          ...assignment,
          user,
          assignedBy,
          roles: roles.filter(Boolean),
        };
      })
    );

    return assignedUsers;
  },
});

// Get shops in area
export const getAreaShops = query({
  args: { 
    areaId: v.id("areas"),
    isActive: v.optional(v.boolean()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_VIEW);

    // Check if user can access this area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    let query = ctx.db
      .query("shops")
      .withIndex("by_area", (q) => q.eq("areaId", args.areaId));

    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    const shops = await query.collect();

    // Enrich with related data
    const enrichedShops = await Promise.all(
      shops.map(async (shop) => {
        const [area, manager, createdBy] = await Promise.all([
          ctx.db.get(shop.areaId),
          shop.managerId ? ctx.db.get(shop.managerId) : null,
          ctx.db.get(shop.createdBy),
        ]);

        return {
          ...shop,
          area,
          manager,
          createdBy,
        };
      })
    );

    return enrichedShops;
  },
});

// Get area statistics
export const getAreaStats = query({
  args: {
    areaId: v.optional(v.id("areas")),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requireAnyPermissionWithWorkOSUser(ctx, [
          PERMISSIONS.REPORTS_VIEW_AREA,
          PERMISSIONS.REPORTS_VIEW_ALL,
        ], args.workosUserId)
      : await requireAnyPermission(ctx, [
          PERMISSIONS.REPORTS_VIEW_AREA,
          PERMISSIONS.REPORTS_VIEW_ALL,
        ]);

    let query = ctx.db.query("areas");

    // Apply role-based filtering
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REPORTS_VIEW_ALL)) {
      const accessibleAreaIds = await getAccessibleAreaIds(ctx);
      if (accessibleAreaIds.length === 0) {
        return { total: 0, active: 0, inactive: 0, totalShops: 0, totalUsers: 0 };
      }
      query = query.filter((q) => 
        q.or(...accessibleAreaIds.map(areaId => q.eq(q.field("_id"), areaId)))
      );
    }

    // Apply additional filters
    if (args.areaId) {
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied to this area");
      }
      query = query.filter((q) => q.eq(q.field("_id"), args.areaId));
    }

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("createdAt"), args.startDate));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("createdAt"), args.endDate));
    }

    const areas = await query.collect();

    let totalShops = 0;
    let totalUsers = 0;

    for (const area of areas) {
      // Count shops in this area
      const shops = await ctx.db
        .query("shops")
        .withIndex("by_area", (q) => q.eq("areaId", area._id))
        .collect();
      totalShops += shops.length;

      // Count users assigned to this area
      const userAssignments = await ctx.db
        .query("user_area_assignments")
        .withIndex("by_area_active", (q) => 
          q.eq("areaId", area._id).eq("isActive", true)
        )
        .collect();
      totalUsers += userAssignments.length;
    }

    const stats = areas.reduce(
      (acc, area) => {
        acc.total++;
        if (area.isActive) {
          acc.active++;
        } else {
          acc.inactive++;
        }
        return acc;
      },
      { total: 0, active: 0, inactive: 0, totalShops, totalUsers }
    );

    return stats;
  },
});

// Get areas accessible to current user
export const getAccessibleAreas = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await requireAuth(ctx);
    
    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    const accessibleAreaIds = await getAccessibleAreaIds(ctx);
    
    if (accessibleAreaIds.length === 0) {
      return [];
    }

    const areas = await Promise.all(
      accessibleAreaIds.map(async (areaId) => {
        const area = await ctx.db.get(areaId);
        return area;
      })
    );

    return areas.filter(Boolean);
  },
});

// Get areas for current user (shop manager's areas through their managed shops)
export const getMyAreas = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    console.log('[getMyAreas] Query called with:', { workosUserId: args.workosUserId });
    
    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      console.log('[getMyAreas] No user found, authentication required');
      throw new Error("Authentication required");
    }

    console.log('[getMyAreas] User found:', {
      userId: userWithPermissions.user._id,
      roles: userWithPermissions.roles.map(r => r.name),
      hasAccountsRole: userWithPermissions.roles.some(role => role.name === ROLES.ACCOUNTS),
      hasShopManagerRole: userWithPermissions.roles.some(role => role.name === ROLES.SHOP_MANAGER)
    });

    // If user has accounts role, they can see all areas
    if (userWithPermissions.roles.some(role => role.name === ROLES.ACCOUNTS)) {
      console.log('[getMyAreas] User has accounts role, returning all areas');
      
      const allAreas = await ctx.db
        .query("areas")
        .filter((q) => q.eq(q.field("isActive"), true))
        .collect();

      console.log('[getMyAreas] Found areas for accounts user:', allAreas.length);

      // Enrich with related data
      const enrichedAreas = await Promise.all(
        allAreas.map(async (area) => {
          const createdBy = await ctx.db.get(area.createdBy);

          // Get shop count for this area
          const shops = await ctx.db
            .query("shops")
            .withIndex("by_area_active", (q) => 
              q.eq("areaId", area._id).eq("isActive", true)
            )
            .collect();

          // Get assigned users count
          const userAssignments = await ctx.db
            .query("user_area_assignments")
            .withIndex("by_area_active", (q) => 
              q.eq("areaId", area._id).eq("isActive", true)
            )
            .collect();

          return {
            ...area,
            createdBy,
            shopCount: shops.length,
            assignedUserCount: userAssignments.length,
          };
        })
      );

      console.log('[getMyAreas] Returning enriched areas for accounts user:', enrichedAreas.length);
      return enrichedAreas;
    }

    // For shop managers, get areas through their managed shops
    console.log('[getMyAreas] User is shop manager, querying areas through managed shops');
    
    const managedShops = await ctx.db
      .query("shops")
      .withIndex("by_manager", (q) => q.eq("managerId", userWithPermissions.user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    console.log('[getMyAreas] Found managed shops:', {
      count: managedShops.length,
      shopIds: managedShops.map(s => s._id),
      areaIds: managedShops.map(s => s.areaId)
    });

    // Get unique area IDs from managed shops
    const areaIds = [...new Set(managedShops.map(shop => shop.areaId))];

    console.log('[getMyAreas] Unique area IDs from managed shops:', areaIds);

    if (areaIds.length === 0) {
      console.log('[getMyAreas] No areas found for shop manager, returning empty array');
      return [];
    }

    // Get areas
    const areas = await Promise.all(
      areaIds.map(async (areaId) => {
        const area = await ctx.db.get(areaId);
        if (!area || area.isActive === false) {
          console.log('[getMyAreas] Area not found or inactive:', areaId);
          return null;
        }

        const createdBy = await ctx.db.get(area.createdBy);

        // Get shop count for this area
        const shops = await ctx.db
          .query("shops")
          .withIndex("by_area_active", (q) => 
            q.eq("areaId", area._id).eq("isActive", true)
          )
          .collect();

        // Get assigned users count
        const userAssignments = await ctx.db
          .query("user_area_assignments")
          .withIndex("by_area_active", (q) => 
            q.eq("areaId", area._id).eq("isActive", true)
          )
          .collect();

        return {
          ...area,
          createdBy,
          shopCount: shops.length,
          assignedUserCount: userAssignments.length,
        };
      })
    );

    const validAreas = areas.filter(Boolean);
    
    console.log('[getMyAreas] Returning areas for shop manager:', {
      count: validAreas.length,
      areas: validAreas.map(a => ({ id: a._id, name: a.name }))
    });

    return validAreas;
  },
});
