import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { testFrontendAuth } from '@/lib/test-auth';
import { useAuth } from '@/lib/auth-context';

const AuthTest: React.FC = () => {
  const [testResult, setTestResult] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user, session, userWithRoles } = useAuth();

  const runAuthTest = async () => {
    setIsLoading(true);
    try {
      const result = await testFrontendAuth();
      setTestResult(result);
    } catch (error) {
      setTestResult({
        success: false,
        error: error.message,
        message: 'Test execution failed'
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Authentication Test</CardTitle>
          <CardDescription>
            Test the WorkOS authentication integration with Convex
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Current Auth State */}
          <div>
            <h3 className="text-lg font-semibold mb-2">Current Auth State</h3>
            <div className="bg-gray-100 p-3 rounded text-sm">
              <div><strong>User:</strong> {user ? `${user.email} (${user.id})` : 'Not logged in'}</div>
              <div><strong>Session:</strong> {session ? 'Active' : 'None'}</div>
              <div><strong>User with Roles:</strong> {userWithRoles ? `${userWithRoles.roles?.length || 0} roles` : 'Not loaded'}</div>
            </div>
          </div>

          {/* Test Button */}
          <Button 
            onClick={runAuthTest} 
            disabled={isLoading || !user}
            className="w-full"
          >
            {isLoading ? 'Testing...' : 'Test Authentication & Area Creation'}
          </Button>

          {!user && (
            <div className="text-red-600 text-sm">
              Please log in first to run the authentication test.
              <br />
              <a href="/login" className="text-blue-600 underline">Go to Login Page</a>
            </div>
          )}

          {/* Test Results */}
          {testResult && (
            <div>
              <h3 className="text-lg font-semibold mb-2">Test Results</h3>
              <div className={`p-3 rounded text-sm ${testResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}`}>
                <div><strong>Status:</strong> {testResult.success ? '✅ Success' : '❌ Failed'}</div>
                <div><strong>Message:</strong> {testResult.message}</div>
                {testResult.error && <div><strong>Error:</strong> {testResult.error}</div>}
                {testResult.session && (
                  <div><strong>Session:</strong> {testResult.session.email} ({testResult.session.userId})</div>
                )}
                {testResult.areaCreated && (
                  <div><strong>Area Created:</strong> {testResult.areaCreated}</div>
                )}
              </div>
              
              {/* Raw Result */}
              <details className="mt-2">
                <summary className="cursor-pointer text-sm text-gray-600">Show Raw Result</summary>
                <pre className="mt-2 p-2 bg-gray-100 rounded text-xs overflow-auto">
                  {JSON.stringify(testResult, null, 2)}
                </pre>
              </details>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthTest;