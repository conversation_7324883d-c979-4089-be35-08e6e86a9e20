
import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import { useAuth } from '@/lib/auth-context';
import PageTransition from '@/components/common/PageTransition';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Check, X, AlertCircle, Edit, ClipboardPen, Image as ImageIcon, Loader2, User } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { RequestStatus } from '@/lib/types';
import { Id } from '../../convex/_generated/dataModel';
import StatusBadge from '@/components/ui/StatusBadge';
import { formatDistanceToNow, format } from 'date-fns';
import { canApproveRequest } from '@/lib/auth';
import { toast } from '@/components/ui/use-toast';
import ResubmissionHistory from '@/components/requests/ResubmissionHistory';
import AdminActionHistory from '@/components/requests/AdminActionHistory';

const PRODUCTS = [
  { id: '1', name: 'Sportbook' },
  { id: '2', name: 'Golden Race' },
  { id: '3', name: 'IBet' },
];

const RequestDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { appUser, user: workosUser, userWithRoles } = useAuth(); // Use appUser from useAuth instead of user from useRequireAuth
  const navigate = useNavigate();
  
  // Primary query - use public query that doesn't require authentication
  const convexRequests = useQuery(
    api.publicQueries.getRequestsForUser,
    workosUser?.id ? { workosUserId: workosUser.id } : "skip"
  );
  
  // Enhanced query - now properly handles WorkOS authentication
  const requestWithHistory = useQuery(
    api.requests.getRequestWithHistory,
    // Only call if we have authentication AND a valid request ID AND the user is authenticated
    (workosUser?.id && id && convexRequests?.some(r => r._id === id)) 
      ? { 
          requestId: id as Id<"requests">,
          workosUserId: workosUser.id 
        } 
      : "skip"
  );
  
  // Use Convex mutations for approve/reject
  const convexApproveRequest = useMutation(api.requests.approveRequest);
  const convexRejectRequest = useMutation(api.requests.rejectRequest);

  // Smart request selection: prefer enhanced data if available, fallback to basic data
  const basicRequest = convexRequests?.find(r => r._id === id);
  const request = requestWithHistory || basicRequest;
  
  // Debug: Log status history data
  useEffect(() => {
    if (request) {
      console.log('=== REQUEST DATA DEBUG ===');
      console.log('Request ID:', request._id);
      console.log('Request source:', requestWithHistory ? 'Enhanced (with history)' : 'Basic (fallback)');
      console.log('Status History exists:', !!request.statusHistory);
      console.log('Status History length:', request.statusHistory?.length || 0);
      console.log('Status History data:', request.statusHistory);
      console.log('========================');
    }
  }, [request, requestWithHistory]);
  
  // Get image URL if ticketImageId exists
  const imageUrl = useQuery(
    api.files.getImageUrl,
    request?.ticketImageId ? { storageId: request.ticketImageId } : "skip"
  );
  const [requestState, setRequestState] = useState(request);
  const [isApproveDialogOpen, setIsApproveDialogOpen] = useState(false);
  const [isRejectDialogOpen, setIsRejectDialogOpen] = useState(false);
  const [approvalReason, setApprovalReason] = useState('');
  const [rejectionReason, setRejectionReason] = useState('');
  const [isImagePreviewOpen, setIsImagePreviewOpen] = useState(false);
  const [verificationItems, setVerificationItems] = useState({
    ticketStatusValid: false,
    managerVerificationCompleted: false,
    documentationVerified: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (request) {
      setRequestState(request);
    } else if (convexRequests !== undefined && convexRequests.length === 0 && id) {
      // No requests found for this user, or request doesn't exist
      toast({
        title: "Request not found",
        description: "The requested cash request could not be found or you don't have permission to view it.",
        variant: "destructive",
      });
      navigate('/requests');
    } else if (convexRequests && convexRequests.length > 0 && !basicRequest && id) {
      // User has requests but this specific ID wasn't found
      toast({
        title: "Request not found",
        description: "The requested cash request could not be found or you don't have permission to view it.",
        variant: "destructive",
      });
      navigate('/requests');
    }
  }, [request, convexRequests, basicRequest, id, navigate]);

  // Show loading state while queries are loading
  if (!workosUser?.id) {
    return (
      <Layout>
        <PageTransition>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-500">Authenticating...</p>
            </div>
          </div>
        </PageTransition>
      </Layout>
    );
  }

  if (convexRequests === undefined) {
    return (
      <Layout>
        <PageTransition>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
              <p className="text-gray-500">Loading request details...</p>
            </div>
          </div>
        </PageTransition>
      </Layout>
    );
  }

  if (!request) {
    return (
      <Layout>
        <PageTransition>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Request not found or loading...</p>
            </div>
          </div>
        </PageTransition>
      </Layout>
    );
  }

  // Get user roles from userWithRoles instead of appUser.role
  const userRoles = userWithRoles?.roles?.map(role => role.name) || [];
  
  const canCurrentUserApprove = userRoles.length > 0 ? canApproveRequest(
    userRoles,
    request.paymentMethod,
    request.amount
  ) : false;

  const isCreatedByCurrentUser = workosUser?.id === request?.workosUserId;

  const canResubmit = isCreatedByCurrentUser && request.status === 'rejected';


  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 2,
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const handleApprove = async () => {
    if (!workosUser) {
      toast({
        title: "Authentication error",
        description: "User authentication required",
        variant: "destructive",
      });
      return;
    }

    // Check if all verification items are checked
    if (!Object.values(verificationItems).every(Boolean)) {
      toast({
        title: "Verification incomplete",
        description: "Please complete all verification checks before approving",
        variant: "destructive",
      });
      return;
    }

    // Check if approval reason is provided
    if (!approvalReason) {
      toast({
        title: "Reason required",
        description: "Please provide a reason for approval",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Attempting to approve request:', request._id);
      console.log('With reason:', approvalReason);

      // Use Convex mutation to approve the request
      await convexApproveRequest({ 
        requestId: request._id, 
        notes: approvalReason,
        workosUserId: workosUser.id
      });

      console.log('Request approved successfully');

      // Convex will automatically refresh the query, so we don't need to manually update state
      toast({
        title: "Request approved",
        description: "The cash request has been approved successfully",
      });
      setIsApproveDialogOpen(false);
    } catch (error) {
      console.error('Error in handleApprove:', error);
      toast({
        title: "Approval failed",
        description: "Failed to approve the request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReject = async () => {
    if (!workosUser) {
      toast({
        title: "Authentication error",
        description: "User authentication required",
        variant: "destructive",
      });
      return;
    }

    if (!rejectionReason) {
      toast({
        title: "Reason required",
        description: "Please provide a reason for rejection",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Use Convex mutation to reject the request
      await convexRejectRequest({ 
        requestId: request._id, 
        reason: rejectionReason,
        workosUserId: workosUser.id
      });
      
      // Convex will automatically refresh the query, so we don't need to manually update state

      toast({
        title: "Request rejected",
        description: "The cash request has been rejected",
      });
      setIsRejectDialogOpen(false);
    } catch (error) {
      toast({
        title: "Rejection failed",
        description: "Failed to reject the request. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };



  const showActionButtons =
    canCurrentUserApprove &&
    (request?.status === 'pending' || request?.status === 'resubmitted') &&
    !isSubmitting;


  return (
    <Layout>
      <div className="container max-w-7xl py-6">
        <div className="flex items-center gap-2 mb-6">
          <Button variant="ghost" size="sm" asChild>
            <Link to="/requests" className="flex items-center gap-1">
              <ArrowLeft className="h-4 w-4" />
              Back to Requests
            </Link>
          </Button>
        </div>

        {/* Request header section */}
        <div className="mb-6 bg-white dark:bg-slate-900 shadow-sm border rounded-lg p-6">
          <h1 className="text-2xl font-bold text-foreground">Request #{request.ticketNumber || 'Unknown'}</h1>
          <div className="text-sm text-muted-foreground space-y-1 mt-2">
            <p>from {request.shopName}</p>
            <p>Request ID: #{request._id}</p>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-2 space-y-6">
            <Card className="glass-panel">
              <CardHeader className="pb-3">
                <CardTitle>Request Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Shop</h3>
                    <p>{request.shopName}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Ticket ID</h3>
                    <p className="font-semibold">{request.ticketNumber || 'N/A'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Product</h3>
                    <p>{PRODUCTS.find(p => p.id === request.productId)?.name || 'Sportbook'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Amount</h3>
                    <p className="font-semibold">{formatAmount(request.amount)}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Payment Method</h3>
                    <p className="capitalize">{request.paymentMethod.replace('_', ' ')}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Date Requested</h3>
                    <p>{format(new Date(request.createdAt), 'MMM d, yyyy h:mm a')}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Customer Name</h3>
                    <p>{(request.customerName && request.customerName.trim()) || 'Not provided'}</p>
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-muted-foreground mb-1">Customer Contact</h3>
                    <p>{(request.customerPhone && request.customerPhone.trim()) || (request.mobileMoneyNumber && request.mobileMoneyNumber.trim()) || 'Not provided'}</p>
                  </div>
                </div>

                {request.notes && (
                  <>
                    <Separator />
                    <div>
                      <h3 className="text-sm font-medium text-muted-foreground mb-1">Comments</h3>
                      <p className="text-sm">{request.notes}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {request.ticketImageId && (
              <Card className="glass-panel">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center justify-between">
                    <span>Ticket Image</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setIsImagePreviewOpen(true)}
                      className="flex items-center gap-1"
                    >
                      <ImageIcon className="h-4 w-4" />
                      <span>Preview</span>
                    </Button>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="rounded-md overflow-hidden border">
                    {imageUrl ? (
                      <img
                        src={imageUrl}
                        alt="Ticket"
                        className="w-full object-contain max-h-[400px]"
                      />
                    ) : (
                      <div className="flex items-center justify-center h-[200px] bg-muted">
                        <div className="text-center">
                          <ImageIcon className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
                          <p className="text-sm text-muted-foreground">Loading image...</p>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Image Preview Dialog */}
            <Dialog open={isImagePreviewOpen} onOpenChange={setIsImagePreviewOpen}>
              <DialogContent className="max-w-4xl p-0 overflow-hidden">
                <div className="p-6 bg-black flex justify-center items-center">
                  {imageUrl ? (
                    <img
                      src={imageUrl}
                      alt="Ticket Preview"
                      className="max-w-full max-h-[80vh] object-contain"
                    />
                  ) : (
                    <div className="text-center text-white">
                      <ImageIcon className="h-16 w-16 mx-auto mb-4" />
                      <p>Loading image...</p>
                    </div>
                  )}
                </div>
                <DialogFooter className="p-4 bg-background">
                  <Button onClick={() => setIsImagePreviewOpen(false)}>
                    Close
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>

            {request.rejectionReason && (
              <Card className="border-destructive/20 bg-destructive/5">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <AlertCircle className="h-5 w-5 text-destructive" />
                    <CardTitle>Rejection Reason</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{request.rejectionReason}</p>
                </CardContent>
              </Card>
            )}

            {request.approvalReason && (
              <Card className="border-green-500/20 bg-green-500/5">
                <CardHeader className="pb-3">
                  <div className="flex items-center gap-2">
                    <Check className="h-5 w-5 text-green-500" />
                    <CardTitle>Approval Reason</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <p>{request.approvalReason}</p>
                </CardContent>
              </Card>
            )}
          </div>

          <div className="space-y-6">
            <Card className="glass-panel">
              <CardHeader className="pb-3">
                <div className="flex justify-between items-center">
                  <CardTitle>Status</CardTitle>
                  <StatusBadge status={request.status} />
                </div>
              </CardHeader>
              <CardContent className="p-6">
                <div className="relative">
                  {/* Continuous timeline line */}
                  <div className="absolute left-[6px] top-0 bottom-0 w-0.5 bg-gray-200"></div>

                  {/* Status History Timeline */}
                  <div className="space-y-0">
                    {request.statusHistory && request.statusHistory.length > 0 ? (
                      // New status history format
                      request.statusHistory.map((statusEntry, index) => {
                        const user = statusEntry.changedByUser;
                        const userName = user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email : 'Unknown User';
                        
                        const getStatusColor = (status: string) => {
                          switch (status) {
                            case 'pending': return 'bg-blue-500';
                            case 'approved': return 'bg-green-500';
                            case 'rejected': return 'bg-red-500';
                            case 'resubmitted': return 'bg-amber-500';
                            case 'paid': return 'bg-purple-500';
                            case 'cancelled': return 'bg-gray-500';
                            default: return 'bg-gray-400';
                          }
                        };

                        const getStatusTitle = (fromStatus: string | undefined, toStatus: string) => {
                          if (!fromStatus) {
                            return 'Request Created';
                          }
                          switch (toStatus) {
                            case 'pending': return 'Set to Pending';
                            case 'approved': return 'Approved';
                            case 'rejected': return 'Rejected';
                            case 'resubmitted': return 'Resubmitted';
                            case 'paid': return 'Marked as Paid';
                            case 'cancelled': return 'Cancelled';
                            default: return `Status Changed to ${toStatus}`;
                          }
                        };

                        return (
                          <div key={statusEntry._id} className="relative flex items-start gap-4 pb-4">
                            <div className={`relative z-10 w-3 h-3 rounded-full ${getStatusColor(statusEntry.toStatus)}`}></div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-900">
                                  {getStatusTitle(statusEntry.fromStatus, statusEntry.toStatus)}
                                </span>
                                <span className="text-xs text-gray-400">
                                  {format(new Date(statusEntry.changedAt), 'MMM dd, HH:mm')}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">{userName}</p>
                              {statusEntry.reason && (
                                <p className="text-xs text-gray-600 mt-2 italic">"{statusEntry.reason}"</p>
                              )}
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      // Fallback to old timeline format
                      (() => {
                        const timelineEvents = [];

                        // Add creation event
                        timelineEvents.push({
                          type: 'created',
                          timestamp: new Date(request.createdAt),
                          actor: request.createdByName || 'User',
                          title: 'Request Created',
                          comment: null
                        });

                        // Add resubmission events
                        if (request.resubmissionHistory) {
                          request.resubmissionHistory.forEach(resubmission => {
                            timelineEvents.push({
                              type: 'resubmitted',
                              timestamp: new Date(resubmission.timestamp),
                              actor: request.createdByName || 'User',
                              title: 'Resubmitted',
                              comment: resubmission.notes
                            });
                          });
                        }

                        // Add final status events
                        if (request.status === 'approved' && request.approvedAt) {
                          timelineEvents.push({
                            type: 'approve',
                            timestamp: new Date(request.approvedAt),
                            actor: request.approvedByName || 'Admin',
                            title: 'Approved',
                            comment: request.approvalReason
                          });
                        }

                        if (request.status === 'rejected' && request.rejectedAt) {
                          timelineEvents.push({
                            type: 'reject',
                            timestamp: new Date(request.rejectedAt),
                            actor: request.rejectedByName || 'Admin',
                            title: 'Rejected',
                            comment: request.rejectionReason
                          });
                        }

                        // Sort chronologically
                        timelineEvents.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

                        const getEventColor = (type: string) => {
                          switch (type) {
                            case 'created': return 'bg-blue-500';
                            case 'approve': return 'bg-green-500';
                            case 'reject': return 'bg-red-500';
                            case 'resubmitted': return 'bg-amber-500';
                            default: return 'bg-gray-400';
                          }
                        };

                        return timelineEvents.map((event, index) => (
                          <div key={index} className="relative flex items-start gap-4 pb-4">
                            <div className={`relative z-10 w-3 h-3 rounded-full ${getEventColor(event.type)}`}></div>
                            <div className="flex-1">
                              <div className="flex items-center justify-between">
                                <span className="text-sm font-medium text-gray-900">{event.title}</span>
                                <span className="text-xs text-gray-400">{format(event.timestamp, 'MMM dd, HH:mm')}</span>
                              </div>
                              <p className="text-xs text-gray-500 mt-1">{event.actor}</p>
                              {event.comment && (
                                <p className="text-xs text-gray-600 mt-2 italic">"{event.comment}"</p>
                              )}
                            </div>
                          </div>
                        ));
                      })()
                    )}
                  </div>
                </div>
              </CardContent>

              {/* Action buttons for admin/support */}
              {showActionButtons && (
                <CardFooter className="flex gap-3 pt-2">
                  <Dialog open={isApproveDialogOpen} onOpenChange={setIsApproveDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        className="flex-1"
                        disabled={!showActionButtons || isSubmitting}
                      >
                        <Check className="mr-2 h-4 w-4" />
                        Approve
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[425px] max-h-[90vh] overflow-y-auto z-[9999] bg-white border shadow-lg">
                      <DialogHeader className="pb-2">
                        <DialogTitle>Approve Request</DialogTitle>
                        <DialogDescription className="text-sm">
                          Please verify the following before approving this request
                        </DialogDescription>
                      </DialogHeader>

                      <div className="space-y-4 py-4">
                        {/* Verification Checklist */}
                        <div className="space-y-3">
                          <div className="flex items-start space-x-3 p-2 rounded-lg bg-muted/50 hover:bg-muted/80 transition-colors">
                            <Checkbox
                              id="ticketStatus"
                              checked={verificationItems.ticketStatusValid}
                              onCheckedChange={(checked) =>
                                setVerificationItems({
                                  ...verificationItems,
                                  ticketStatusValid: checked as boolean
                                })
                              }
                              className="mt-1"
                            />
                            <div className="space-y-0.5">
                              <Label
                                htmlFor="ticketStatus"
                                className="text-sm font-medium cursor-pointer"
                              >
                                Ticket status is valid for payout
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                Verify that the ticket is either Won for Payout.
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start space-x-3 p-2 rounded-lg bg-muted/50 hover:bg-muted/80 transition-colors">
                            <Checkbox
                              id="managerVerification"
                              checked={verificationItems.managerVerificationCompleted}
                              onCheckedChange={(checked) =>
                                setVerificationItems({
                                  ...verificationItems,
                                  managerVerificationCompleted: checked as boolean
                                })
                              }
                              className="mt-1"
                            />
                            <div className="space-y-0.5">
                              <Label
                                htmlFor="managerVerification"
                                className="text-sm font-medium cursor-pointer"
                              >
                                Manager verification completed
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                Confirm that the shop manager has verified this request
                              </p>
                            </div>
                          </div>

                          <div className="flex items-start space-x-3 p-2 rounded-lg bg-muted/50 hover:bg-muted/80 transition-colors">
                            <Checkbox
                              id="documentation"
                              checked={verificationItems.documentationVerified}
                              onCheckedChange={(checked) =>
                                setVerificationItems({
                                  ...verificationItems,
                                  documentationVerified: checked as boolean
                                })
                              }
                              className="mt-1"
                            />
                            <div className="space-y-0.5">
                              <Label
                                htmlFor="documentation"
                                className="text-sm font-medium cursor-pointer"
                              >
                                Supporting documentation reviewed
                              </Label>
                              <p className="text-xs text-muted-foreground">
                                Confirm that all required documentation has been reviewed
                              </p>
                            </div>
                          </div>
                        </div>

                        {/* Approval Reason */}
                        <div className="space-y-1.5">
                          <Label htmlFor="approvalReason" className="text-sm font-medium">
                            Approval Reason
                            <span className="text-destructive ml-1">*</span>
                          </Label>
                          <Textarea
                            id="approvalReason"
                            placeholder="Provide the reason for approving this request..."
                            value={approvalReason}
                            onChange={(e) => setApprovalReason(e.target.value)}
                            className="h-[80px] resize-none"
                            disabled={isSubmitting}
                          />
                        </div>
                      </div>

                      <DialogFooter className="gap-2 sm:gap-0">
                        <Button
                          variant="outline"
                          onClick={() => setIsApproveDialogOpen(false)}
                          disabled={isSubmitting}
                          className="flex-1 sm:flex-none"
                        >
                          Cancel
                        </Button>
                        <Button
                          onClick={handleApprove}
                          disabled={isSubmitting || !approvalReason || !Object.values(verificationItems).every(Boolean)}
                          className="flex-1 sm:flex-none bg-green-600 hover:bg-green-700"
                        >
                          {isSubmitting ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Processing...
                            </>
                          ) : (
                            <>
                              <Check className="mr-2 h-4 w-4" />
                              Confirm Approval
                            </>
                          )}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Dialog open={isRejectDialogOpen} onOpenChange={setIsRejectDialogOpen}>
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        className="flex-1"
                        disabled={!showActionButtons || isSubmitting}
                      >
                        <X className="mr-2 h-4 w-4" />
                        Reject
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="z-[9999] bg-white border shadow-lg">
                      <DialogHeader>
                        <DialogTitle>Reject Request</DialogTitle>
                        <DialogDescription>
                          Please provide a reason for rejecting this request
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="reason">Rejection Reason (required)</Label>
                          <Textarea
                            id="reason"
                            placeholder="Explain why this request is being rejected..."
                            value={rejectionReason}
                            onChange={(e) => setRejectionReason(e.target.value)}
                            className="h-[96px]"
                            disabled={isSubmitting}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setIsRejectDialogOpen(false)}
                          disabled={isSubmitting}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={handleReject}
                          disabled={isSubmitting || !rejectionReason}
                        >
                          {isSubmitting ? 'Processing...' : 'Confirm Rejection'}
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardFooter>
              )}

              {/* Resubmit button for shop manager */}
              {canResubmit && (
                <CardFooter className="pt-2">
                  <Button
                    className="w-full"
                    onClick={() => navigate(`/resubmit-request/${request._id}`)}
                  >
                    <ClipboardPen className="mr-2 h-4 w-4" />
                    Resubmit Request
                  </Button>
                </CardFooter>
              )}

              {/* Edit button for pending requests */}
              {isCreatedByCurrentUser && request.status === 'pending' && (
                <CardFooter className="pt-2">
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => navigate(`/requests/${request._id}/edit`)}
                  >
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Request
                  </Button>
                </CardFooter>
              )}
            </Card>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default RequestDetails;
