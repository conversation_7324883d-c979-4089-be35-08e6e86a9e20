import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  requireAuth,
  requirePermission,
  requireAnyPermission,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  ROLES,
} from "./permissions";

// Get all settings as a key-value object (public access for basic settings)
export const getPublicSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("settings").collect();
    
    // Convert to key-value object for easy access
    const settingsObj: Record<string, any> = {};
    for (const setting of settings) {
      settingsObj[setting.key] = setting.value;
    }
    
    return settingsObj;
  },
});

// Get all settings as a key-value object (authenticated access)
export const getSettings = query({
  args: { workosUserId: v.optional(v.string()) },
  handler: async (ctx, { workosUserId }) => {
    // Use getCurrentUserWithPermissions directly with workosUserId
    const userWithPermissions = await getCurrentUserWithPermissions(ctx, workosUserId);
    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    const settings = await ctx.db.query("settings").collect();
    
    // Convert to key-value object for easy access
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, any>);

    // Ensure default values exist
    const defaultSettings = {
      mobile_money_approval_threshold: 5000,
      bank_transfer_approval_threshold: 10000,
      whitelisted_domains: ["kmkentertainment.com", "mybet.africa"],
      max_file_size: 10 * 1024 * 1024, // 10MB
      max_avatar_size: 2 * 1024 * 1024, // 2MB
      max_ticket_image_size: 5 * 1024 * 1024, // 5MB
      notification_retention_days: 30,
      session_timeout_minutes: 480, // 8 hours
      auto_logout_warning_minutes: 15,
      ...settingsObject,
    };

    return defaultSettings;
  },
});

// Get settings with metadata (for admin interface)
export const getSettingsWithMetadata = query({
  args: {
    category: v.optional(v.string()),
    isSystem: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.SETTINGS_VIEW);

    let query = ctx.db.query("settings");

    if (args.category) {
      query = query.filter((q) => q.eq(q.field("category"), args.category));
    }

    if (args.isSystem !== undefined) {
      query = query.filter((q) => q.eq(q.field("isSystem"), args.isSystem));
    }

    const settings = await query.collect();

    // Enrich with user information
    const enrichedSettings = await Promise.all(
      settings.map(async (setting) => {
        const updatedBy = await ctx.db.get(setting.updatedBy);
        return {
          ...setting,
          updatedBy,
        };
      })
    );

    return enrichedSettings;
  },
});

// Get a specific setting by key
export const getSetting = query({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    await requireAuth(ctx);

    const setting = await ctx.db
      .query("settings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    return setting?.value;
  },
});

// Update multiple settings at once
export const updateSettings = mutation({
  args: {
    settings: v.object({
      mobile_money_approval_threshold: v.optional(v.number()),
      bank_transfer_approval_threshold: v.optional(v.number()),
      whitelisted_domains: v.optional(v.array(v.string())),
      max_file_size: v.optional(v.number()),
      max_avatar_size: v.optional(v.number()),
      max_ticket_image_size: v.optional(v.number()),
      notification_retention_days: v.optional(v.number()),
      session_timeout_minutes: v.optional(v.number()),
      auto_logout_warning_minutes: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SETTINGS_UPDATE);

    const now = Date.now();
    const updatedSettings: Record<string, any> = {};

    // Process each setting
    for (const [key, value] of Object.entries(args.settings)) {
      if (value !== undefined) {
        // Validate setting values
        await validateSettingValue(key, value);

        const existingSetting = await ctx.db
          .query("settings")
          .withIndex("by_key", (q) => q.eq("key", key))
          .first();

        if (existingSetting) {
          // Update existing setting
          await ctx.db.patch(existingSetting._id, {
            value,
            updatedBy: userWithPermissions.user._id,
            updatedAt: now,
          });
        } else {
          // Create new setting
          await ctx.db.insert("settings", {
            key,
            value,
            description: getSettingDescription(key),
            category: getSettingCategory(key),
            isSystem: true,
            updatedBy: userWithPermissions.user._id,
            updatedAt: now,
            createdAt: now,
          });
        }

        updatedSettings[key] = value;

        // Log the action
        await ctx.db.insert("audit_logs", {
          action: "setting_updated",
          entityType: "setting",
          entityId: key,
          userId: userWithPermissions.user._id,
          newValues: { [key]: value },
          timestamp: now,
        });
      }
    }

    return updatedSettings;
  },
});

// Create or update a single setting
export const updateSetting = mutation({
  args: {
    key: v.string(),
    value: v.any(),
    description: v.optional(v.string()),
    category: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SETTINGS_UPDATE);

    // Validate setting value
    await validateSettingValue(args.key, args.value);

    const now = Date.now();
    const existingSetting = await ctx.db
      .query("settings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    if (existingSetting) {
      // Update existing setting
      await ctx.db.patch(existingSetting._id, {
        value: args.value,
        description: args.description || existingSetting.description,
        category: args.category || existingSetting.category,
        updatedBy: userWithPermissions.user._id,
        updatedAt: now,
      });
    } else {
      // Create new setting
      await ctx.db.insert("settings", {
        key: args.key,
        value: args.value,
        description: args.description || getSettingDescription(args.key),
        category: args.category || getSettingCategory(args.key),
        isSystem: false, // User-created settings are not system settings
        updatedBy: userWithPermissions.user._id,
        updatedAt: now,
        createdAt: now,
      });
    }

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "setting_updated",
      entityType: "setting",
      entityId: args.key,
      userId: userWithPermissions.user._id,
      newValues: { [args.key]: args.value },
      timestamp: now,
    });

    return args.value;
  },
});

// Delete a setting (only non-system settings)
export const deleteSetting = mutation({
  args: { key: v.string() },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SETTINGS_UPDATE);

    const setting = await ctx.db
      .query("settings")
      .withIndex("by_key", (q) => q.eq("key", args.key))
      .first();

    if (!setting) {
      throw new Error("Setting not found");
    }

    if (setting.isSystem) {
      throw new Error("Cannot delete system settings");
    }

    await ctx.db.delete(setting._id);

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "setting_deleted",
      entityType: "setting",
      entityId: args.key,
      userId: userWithPermissions.user._id,
      oldValues: setting,
      timestamp: Date.now(),
    });

    return args.key;
  },
});

// Initialize default system settings
export const initializeDefaultSettings = mutation({
  args: {},
  handler: async (ctx) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SETTINGS_UPDATE);

    const defaultSettings = [
      {
        key: "mobile_money_approval_threshold",
        value: 5000,
        description: "Maximum amount for mobile money requests that can be approved by shop support",
        category: "approval_thresholds",
      },
      {
        key: "bank_transfer_approval_threshold",
        value: 10000,
        description: "Maximum amount for bank transfer requests that can be approved by shop support",
        category: "approval_thresholds",
      },
      {
        key: "whitelisted_domains",
        value: ["kmkentertainment.com", "mybet.africa"],
        description: "Email domains allowed for user registration",
        category: "security",
      },
      {
        key: "max_file_size",
        value: 10 * 1024 * 1024, // 10MB
        description: "Maximum file size for uploads in bytes",
        category: "file_management",
      },
      {
        key: "max_avatar_size",
        value: 2 * 1024 * 1024, // 2MB
        description: "Maximum avatar file size in bytes",
        category: "file_management",
      },
      {
        key: "max_ticket_image_size",
        value: 5 * 1024 * 1024, // 5MB
        description: "Maximum ticket image file size in bytes",
        category: "file_management",
      },
      {
        key: "notification_retention_days",
        value: 30,
        description: "Number of days to retain notifications",
        category: "notifications",
      },
      {
        key: "session_timeout_minutes",
        value: 480, // 8 hours
        description: "Session timeout in minutes",
        category: "security",
      },
      {
        key: "auto_logout_warning_minutes",
        value: 15,
        description: "Minutes before session timeout to show warning",
        category: "security",
      },
    ];

    const now = Date.now();
    const createdSettings: string[] = [];

    for (const settingData of defaultSettings) {
      // Check if setting already exists
      const existingSetting = await ctx.db
        .query("settings")
        .withIndex("by_key", (q) => q.eq("key", settingData.key))
        .first();

      if (!existingSetting) {
        await ctx.db.insert("settings", {
          ...settingData,
          isSystem: true,
          updatedBy: userWithPermissions.user._id,
          updatedAt: now,
          createdAt: now,
        });
        createdSettings.push(settingData.key);
      }
    }

    // Log the action
    if (createdSettings.length > 0) {
      await ctx.db.insert("audit_logs", {
        action: "default_settings_initialized",
        entityType: "setting",
        entityId: "bulk",
        userId: userWithPermissions.user._id,
        newValues: { createdSettings },
        timestamp: now,
      });
    }

    return createdSettings;
  },
});

// Get settings by category
export const getSettingsByCategory = query({
  args: { category: v.string() },
  handler: async (ctx, args) => {
    await requireAuth(ctx);

    const settings = await ctx.db
      .query("settings")
      .withIndex("by_category", (q) => q.eq("category", args.category))
      .collect();

    return settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, any>);
  },
});

// Get all setting categories
export const getSettingCategories = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.SETTINGS_VIEW);

    const settings = await ctx.db.query("settings").collect();
    const categorySet = new Set(settings.map(s => s.category));
    const categories = Array.from(categorySet);
    
    return categories.sort();
  },
});

// Reset settings to default values
export const resetSettingsToDefault = mutation({
  args: { keys: v.array(v.string()) },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SETTINGS_UPDATE);

    const defaultValues: Record<string, any> = {
      mobile_money_approval_threshold: 5000,
      bank_transfer_approval_threshold: 10000,
      whitelisted_domains: ["kmkentertainment.com", "mybet.africa"],
      max_file_size: 10 * 1024 * 1024,
      max_avatar_size: 2 * 1024 * 1024,
      max_ticket_image_size: 5 * 1024 * 1024,
      notification_retention_days: 30,
      session_timeout_minutes: 480,
      auto_logout_warning_minutes: 15,
    };

    const now = Date.now();
    const resetSettings: Record<string, any> = {};

    for (const key of args.keys) {
      if (key in defaultValues) {
        const setting = await ctx.db
          .query("settings")
          .withIndex("by_key", (q) => q.eq("key", key))
          .first();

        if (setting) {
          await ctx.db.patch(setting._id, {
            value: defaultValues[key],
            updatedBy: userWithPermissions.user._id,
            updatedAt: now,
          });
          resetSettings[key] = defaultValues[key];
        }
      }
    }

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "settings_reset_to_default",
      entityType: "setting",
      entityId: "bulk",
      userId: userWithPermissions.user._id,
      newValues: resetSettings,
      timestamp: now,
    });

    return resetSettings;
  },
});

// Helper function to validate setting values
async function validateSettingValue(key: string, value: any): Promise<void> {
  switch (key) {
    case "mobile_money_approval_threshold":
    case "bank_transfer_approval_threshold":
      if (typeof value !== "number" || value < 0) {
        throw new Error(`${key} must be a positive number`);
      }
      if (value > 1000000) {
        throw new Error(`${key} cannot exceed 1,000,000`);
      }
      break;

    case "whitelisted_domains":
      if (!Array.isArray(value)) {
        throw new Error("whitelisted_domains must be an array");
      }
      for (const domain of value) {
        if (typeof domain !== "string" || !domain.includes(".")) {
          throw new Error("Invalid domain format in whitelisted_domains");
        }
      }
      break;

    case "max_file_size":
    case "max_avatar_size":
    case "max_ticket_image_size":
      if (typeof value !== "number" || value < 0) {
        throw new Error(`${key} must be a positive number`);
      }
      if (value > 100 * 1024 * 1024) { // 100MB max
        throw new Error(`${key} cannot exceed 100MB`);
      }
      break;

    case "notification_retention_days":
      if (typeof value !== "number" || value < 1) {
        throw new Error("notification_retention_days must be at least 1");
      }
      if (value > 365) {
        throw new Error("notification_retention_days cannot exceed 365");
      }
      break;

    case "session_timeout_minutes":
      if (typeof value !== "number" || value < 15) {
        throw new Error("session_timeout_minutes must be at least 15");
      }
      if (value > 1440) { // 24 hours max
        throw new Error("session_timeout_minutes cannot exceed 1440 (24 hours)");
      }
      break;

    case "auto_logout_warning_minutes":
      if (typeof value !== "number" || value < 1) {
        throw new Error("auto_logout_warning_minutes must be at least 1");
      }
      if (value > 60) {
        throw new Error("auto_logout_warning_minutes cannot exceed 60");
      }
      break;

    default:
      // Allow custom settings without validation
      break;
  }
}

// Helper function to get setting description
function getSettingDescription(key: string): string {
  const descriptions: Record<string, string> = {
    mobile_money_approval_threshold: "Maximum amount for mobile money requests that can be approved by shop support",
    bank_transfer_approval_threshold: "Maximum amount for bank transfer requests that can be approved by shop support",
    whitelisted_domains: "Email domains allowed for user registration",
    max_file_size: "Maximum file size for uploads in bytes",
    max_avatar_size: "Maximum avatar file size in bytes",
    max_ticket_image_size: "Maximum ticket image file size in bytes",
    notification_retention_days: "Number of days to retain notifications",
    session_timeout_minutes: "Session timeout in minutes",
    auto_logout_warning_minutes: "Minutes before session timeout to show warning",
  };

  return descriptions[key] || "Custom setting";
}

// Helper function to get setting category
function getSettingCategory(key: string): string {
  const categories: Record<string, string> = {
    mobile_money_approval_threshold: "approval_thresholds",
    bank_transfer_approval_threshold: "approval_thresholds",
    whitelisted_domains: "security",
    max_file_size: "file_management",
    max_avatar_size: "file_management",
    max_ticket_image_size: "file_management",
    notification_retention_days: "notifications",
    session_timeout_minutes: "security",
    auto_logout_warning_minutes: "security",
  };

  return categories[key] || "custom";
}