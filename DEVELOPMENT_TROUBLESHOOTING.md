# Development Environment Troubleshooting Guide

## 🔧 Common Issues & Solutions

### **Network Connectivity Issues**

#### **Symptoms:**
- `Error: connect ETIMEDOUT **************:443`
- `Unexpected error when authorizing - are you connected to the internet?`
- Convex dev process hangs or fails to connect

#### **Solutions:**

1. **Use the Enhanced Dev Script**
   ```bash
   npm run dev
   ```
   This script handles network issues gracefully and provides clear error messages.

2. **Run Convex and Vite Separately**
   ```bash
   # First terminal
   npm run convex:dev:once
   
   # Second terminal
   npx vite
   ```

3. **Check Network Connectivity**
   ```bash
   ping convex.dev
   curl -I https://convex.dev
   ```

4. **Check Firewall Settings**
   - Ensure your firewall allows outbound connections to Convex servers
   - Try temporarily disabling VPN if you're using one

5. **Use Deployment Without Watch Mode**
   ```bash
   npm run convex:dev:once
   ```
   This deploys once without maintaining a persistent connection.

### **Authentication Issues**

#### **Symptoms:**
- `Unexpected error when authorizing`
- `Not authenticated` errors

#### **Solutions:**

1. **Re-authenticate with Convex**
   ```bash
   npx convex logout
   npx convex login
   ```

2. **Check Environment Variables**
   ```bash
   npx convex env list
   ```
   Ensure all required environment variables are set.

3. **Verify Project Configuration**
   ```bash
   cat convex.json
   ```
   Make sure the project configuration is correct.

### **Development Workflow Options**

#### **1. Enhanced Development Script (Recommended)**
```bash
npm run dev
```
- Deploys Convex functions once
- Starts Vite development server
- Handles network issues gracefully

#### **2. Legacy Development Mode**
```bash
npm run dev:legacy
```
- Simple sequential execution
- Less error handling

#### **3. Watch Mode (May Have Network Issues)**
```bash
npm run dev:watch
```
- Continuous watching for changes
- May encounter network timeouts

#### **4. Separate Terminal Approach**
```bash
# Terminal 1
npm run convex:dev:once

# Terminal 2
npx vite
```
- Most reliable but requires two terminals

## 🚀 Production Deployment

For production deployment, use:
```bash
npm run build
```

This will deploy Convex functions and build the Vite application for production.

## 📋 Additional Resources

- [Convex Documentation](https://docs.convex.dev/)
- [Vite Documentation](https://vitejs.dev/guide/)
- [WorkOS Documentation](https://workos.com/docs)