import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Eye, EyeOff, Lock } from 'lucide-react';

interface SetPasswordFormProps {
  token?: string;
}

export const SetPasswordForm: React.FC<SetPasswordFormProps> = ({ token: propToken }) => {
  const [searchParams] = useSearchParams();
  const token = propToken || searchParams.get('token');
  const email = searchParams.get('email');
  
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [isValidToken, setIsValidToken] = useState<boolean | null>(null);
  const navigate = useNavigate();

  // Validate token on component mount
  useEffect(() => {
    const validateToken = async () => {
      if (!token) {
        setIsValidToken(false);
        return;
      }

      try {
        const response = await fetch('/api/auth/validate-invitation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ token }),
        });

        if (response.ok) {
          setIsValidToken(true);
        } else {
          setIsValidToken(false);
          const error = await response.json();
          toast({
            title: "Invalid Invitation",
            description: error.message || "This invitation link is invalid or has expired.",
            variant: "destructive",
          });
        }
      } catch (error) {
        console.error('Token validation error:', error);
        setIsValidToken(false);
        toast({
          title: "Error",
          description: "Unable to validate invitation. Please try again.",
          variant: "destructive",
        });
      }
    };

    validateToken();
  }, [token]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      toast({
        title: "Error",
        description: "Passwords do not match",
        variant: "destructive",
      });
      return;
    }

    if (formData.password.length < 8) {
      toast({
        title: "Error",
        description: "Password must be at least 8 characters long",
        variant: "destructive",
      });
      return;
    }

    if (!token) {
      toast({
        title: "Error",
        description: "Invalid invitation token",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await fetch('/api/auth/accept-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          password: formData.password,
          email,
        }),
      });

      if (response.ok) {
        toast({
          title: "Success",
          description: "Your account has been set up successfully. You can now sign in.",
        });

        // Redirect to login page
        navigate('/login');
      } else {
        const error = await response.json();
        throw new Error(error.message || 'Failed to set up account');
      }
    } catch (error: any) {
      console.error('SetPasswordForm: Error setting password:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to set up account. The invitation may have expired.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while validating token
  if (isValidToken === null) {
    return (
      <Card className="w-full shadow-lg border-gray-200/50 backdrop-blur-sm bg-white/90 dark:bg-gray-900/90 border-0">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-sm text-gray-500">Validating invitation...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Show error state for invalid token
  if (isValidToken === false) {
    return (
      <Card className="w-full shadow-lg border-gray-200/50 backdrop-blur-sm bg-white/90 dark:bg-gray-900/90 border-0">
        <CardHeader className="space-y-1 text-center pb-8">
          <div className="flex justify-center pb-6 border-b border-gray-100">
            <img
              src="/lovable-uploads/d809f2fb-430a-4759-9377-9438f8aacc4f.png"
              alt="MyBet Africa"
              className="h-8 w-auto"
            />
          </div>
          <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 mt-6">
            Invalid Invitation
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            This invitation link is invalid or has expired. Please contact your administrator for a new invitation.
          </p>
        </CardHeader>
        <CardContent>
          <Button
            onClick={() => navigate('/login')}
            className="w-full py-6"
          >
            Go to Login
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Render the password form
  return (
    <Card className="w-full shadow-lg border-gray-200/50 backdrop-blur-sm bg-white/90 dark:bg-gray-900/90 border-0">
      <CardHeader className="space-y-1 text-center pb-8">
        {/* Logo section within the card */}
        <div className="flex justify-center pb-6 border-b border-gray-100">
          <img
            src="/lovable-uploads/d809f2fb-430a-4759-9377-9438f8aacc4f.png"
            alt="MyBet Africa"
            className="h-8 w-auto"
          />
        </div>

        <h2 className="text-2xl font-bold tracking-tight text-gray-900 dark:text-gray-100 mt-6">
          Set Your Password
        </h2>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          Please create a password for your MyBet Africa Cash Management System account
        </p>
        {email && (
          <p className="text-xs text-gray-400">
            Setting up account for: {email}
          </p>
        )}
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4" />
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="New Password"
                value={formData.password}
                onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                required
                disabled={isLoading}
                className="pl-10 py-6"
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-3 top-1/2 -translate-y-1/2 text-gray-500"
              >
                {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
              </button>
            </div>
            <div className="relative">
              <Lock className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 h-4 w-4" />
              <Input
                type={showPassword ? "text" : "password"}
                placeholder="Confirm Password"
                value={formData.confirmPassword}
                onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                required
                disabled={isLoading}
                className="pl-10 py-6"
              />
            </div>
          </div>
          <Button
            type="submit"
            disabled={isLoading}
            className="w-full py-6"
          >
            {isLoading ? (
              <span className="flex items-center justify-center">
                <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-t-transparent border-white" />
                Setting Up Account...
              </span>
            ) : (
              "Set Password & Continue"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
