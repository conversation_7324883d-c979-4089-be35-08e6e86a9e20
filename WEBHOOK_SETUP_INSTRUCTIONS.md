# WorkOS Webhook Setup Instructions

## Step 1: Configure Webhook in WorkOS Dashboard

1. Go to the [WorkOS Dashboard](https://dashboard.workos.com/)
2. Navigate to the Webhooks section
3. Click "Add Endpoint" or "Create Webhook"
4. Configure the following:

### Webhook URL
```
https://efficient-toucan-547.convex.cloud/http/workos-webhook
```

**Important:** Note the `/http/` prefix - this is required for Convex HTTP routes.

### Events to Subscribe To
Select the following events:
- `user.created`
- `user.updated` 
- `user.deleted`
- `organization.created`
- `organization.updated`
- `organization.deleted`
- `organization_membership.created`
- `organization_membership.updated`
- `organization_membership.deleted`

### Security
- The webhook secret will be generated automatically
- Copy this secret and add it to your Convex environment variables

## Step 2: Add Webhook Secret to Convex Environment

After creating the webhook in the dashboard, you'll get a webhook secret. Add it to your Convex environment:

```bash
# Use the Convex dashboard or CLI to set this environment variable
WORKOS_WEBHOOK_SECRET=your_webhook_secret_here
```

## Step 3: Test the Webhook

Use the "Send test event" button in the WorkOS dashboard to test your webhook endpoint.

## Current Configuration Status
- ✅ Webhook endpoint implemented in `convex/webhooks.ts`
- ✅ HTTP route configured in `convex/http.ts`
- ✅ HTTP routing issue resolved (routes accessible via `/http/` prefix)
- ✅ Webhook URL: `https://efficient-toucan-547.convex.cloud/http/workos-webhook`
- ✅ Basic webhook testing successful
- ⏳ Webhook registration in WorkOS Dashboard (manual step)
- ⏳ Webhook secret configuration (after registration)