import { v } from "convex/values";
import { action, mutation } from "./_generated/server";
import { api } from "./_generated/api";
import { Id } from "./_generated/dataModel";
import {
  getRequestSubmittedTemplate,
  getRequestApprovedTemplate,
  getRequestRejectedTemplate,
  getRequestResubmittedTemplate,
  EmailTemplateData
} from "./emailTemplates";

// Send email notification for request status changes
export const sendRequestStatusEmail = action({
  args: {
    requestId: v.id("requests"),
    statusType: v.union(
      v.literal("request_submitted"),
      v.literal("request_approved"),
      v.literal("request_rejected"),
      v.literal("request_resubmitted")
    ),
    recipientEmails: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Get request details
      const request = await ctx.runQuery(api.requests.getRequest, {
        requestId: args.requestId
      });

      if (!request) {
        console.error("Request not found:", args.requestId);
        return { success: false, error: "Request not found" };
      }

      // Get SMTP configuration
      const settings = await ctx.runQuery(api.settings.getSettings);
      if (!settings.smtp_enabled) {
        console.log("SMTP is disabled, skipping email notifications");
        return { success: false, error: "SMTP is disabled" };
      }

      // Prepare email template data
      const templateData: EmailTemplateData = {
        requestNumber: request.ticketNumber,
        amount: request.amount,
        shopName: request.shop?.name || "Unknown Shop",
        areaName: request.area?.name || "Unknown Area",
        requesterName: request.requestedBy ? 
          `${request.requestedBy.firstName || ''} ${request.requestedBy.lastName || ''}`.trim() || 
          request.requestedBy.email : "Unknown User",
        paymentMethod: request.paymentMethod === "mobile_money" ? "Mobile Money" : "Bank Transfer",
        networkProvider: request.mobileMoneyProvider,
        rejectionReason: request.rejectionReason,
        approverName: request.approvedBy ? 
          `${request.approvedBy.firstName || ''} ${request.approvedBy.lastName || ''}`.trim() || 
          request.approvedBy.email : undefined,
        requestUrl: `${process.env.SITE_URL || 'https://your-app.com'}/requests/${request._id}`,
        systemName: settings.smtp_from_name || "Request Management System"
      };

      // Get appropriate email template
      let emailTemplate;
      switch (args.statusType) {
        case "request_submitted":
          emailTemplate = getRequestSubmittedTemplate(templateData);
          break;
        case "request_approved":
          emailTemplate = getRequestApprovedTemplate(templateData);
          break;
        case "request_rejected":
          emailTemplate = getRequestRejectedTemplate(templateData);
          break;
        case "request_resubmitted":
          emailTemplate = getRequestResubmittedTemplate(templateData);
          break;
        default:
          throw new Error(`Unknown status type: ${args.statusType}`);
      }

      // Send emails to all recipients
      const emailResults = [];
      for (const email of args.recipientEmails) {
        try {
          const result = await ctx.runAction(api.realSMTPService.sendRealEmail, {
            to: email,
            subject: emailTemplate.subject,
            html: emailTemplate.html,
            text: emailTemplate.text,
          });
          
          emailResults.push({
            email,
            success: result.success,
            error: result.error,
            messageId: result.messageId
          });
        } catch (error) {
          console.error(`Failed to send email to ${email}:`, error);
          emailResults.push({
            email,
            success: false,
            error: error.message
          });
        }
      }

      const successCount = emailResults.filter(r => r.success).length;
      const failureCount = emailResults.filter(r => !r.success).length;

      console.log(`Email notification results for ${args.statusType}:`, {
        total: emailResults.length,
        successful: successCount,
        failed: failureCount,
        requestId: args.requestId
      });

      return {
        success: successCount > 0,
        total: emailResults.length,
        successful: successCount,
        failed: failureCount,
        results: emailResults
      };

    } catch (error) {
      console.error("Error sending request status emails:", error);
      return { success: false, error: error.message };
    }
  },
});

// Get email recipients for a request based on status type and user roles
export const getEmailRecipientsForRequest = action({
  args: {
    requestId: v.id("requests"),
    statusType: v.union(
      v.literal("request_submitted"),
      v.literal("request_approved"),
      v.literal("request_rejected"),
      v.literal("request_resubmitted")
    ),
  },
  handler: async (ctx, args) => {
    try {
      const request = await ctx.runQuery(api.requests.getRequest, {
        requestId: args.requestId
      });

      if (!request) {
        return [];
      }

      const recipients: string[] = [];

      switch (args.statusType) {
        case "request_submitted":
        case "request_resubmitted":
          // Notify approvers in the area and accounts team
          const areaUsers = await ctx.runQuery(api.userQueries.getUsersByArea, {
            areaId: request.areaId
          });
          
          // Add area users with approval permissions
          for (const user of areaUsers) {
            if (user.email && user.isActive) {
              recipients.push(user.email);
            }
          }

          // Add accounts team members
          const accountsUsers = await ctx.runQuery(api.userQueries.getUsersByRole, {
            roleName: "accounts"
          });
          
          for (const user of accountsUsers) {
            if (user.email && user.isActive && !recipients.includes(user.email)) {
              recipients.push(user.email);
            }
          }
          break;

        case "request_approved":
        case "request_rejected":
          // Notify the requester
          if (request.requestedBy?.email && request.requestedBy.isActive) {
            recipients.push(request.requestedBy.email);
          }
          break;
      }

      return recipients;

    } catch (error) {
      console.error("Error getting email recipients:", error);
      return [];
    }
  },
});

// Send comprehensive notification (both in-app and email)
export const sendComprehensiveNotification = action({
  args: {
    requestId: v.id("requests"),
    statusType: v.union(
      v.literal("request_submitted"),
      v.literal("request_approved"),
      v.literal("request_rejected"),
      v.literal("request_resubmitted")
    ),
  },
  handler: async (ctx, args) => {
    try {
      // Get email recipients
      const emailRecipients = await ctx.runAction(api.emailNotificationService.getEmailRecipientsForRequest, {
        requestId: args.requestId,
        statusType: args.statusType
      });

      // Send email notifications if there are recipients
      let emailResult = { success: true, total: 0, successful: 0, failed: 0 };
      if (emailRecipients.length > 0) {
        emailResult = await ctx.runAction(api.emailNotificationService.sendRequestStatusEmail, {
          requestId: args.requestId,
          statusType: args.statusType,
          recipientEmails: emailRecipients
        });
      }

      // The in-app notifications are already handled by the existing createRequestNotification function
      // in requests.ts, so we don't need to duplicate that here

      return {
        success: true,
        emailNotifications: emailResult,
        message: `Sent ${emailResult.successful} email notifications out of ${emailResult.total} recipients`
      };

    } catch (error) {
      console.error("Error sending comprehensive notification:", error);
      return { success: false, error: error.message };
    }
  },
});