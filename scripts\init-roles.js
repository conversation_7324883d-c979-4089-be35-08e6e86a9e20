#!/usr/bin/env node

/**
 * Initialize roles in Convex database
 * 
 * This script sets up the basic roles and permissions in your Convex database
 * to match the WorkOS roles you've created.
 */

const { ConvexHttpClient } = require("convex/browser");

const CONVEX_URL = process.env.VITE_CONVEX_URL || "https://efficient-toucan-547.convex.cloud";

async function initializeRoles() {
  console.log("🚀 Initializing roles in Convex...");
  
  try {
    const client = new ConvexHttpClient(CONVEX_URL);
    
    // Call the initialization mutation
    const result = await client.mutation("workosSync:initializeRoles", {});
    
    console.log("✅ Roles initialized successfully!");
    console.log(`📊 Created ${result.createdRoles} new roles`);
    
    console.log("\n🎯 Available roles:");
    console.log("- shop_manager: Can create and manage payment requests");
    console.log("- shop_support: Can approve mobile money requests within limits");
    console.log("- accounts: Full administrative access");
    console.log("- watcher: Read-only access to reports");
    
  } catch (error) {
    console.error("❌ Error initializing roles:", error);
    process.exit(1);
  }
}

// Run the initialization
initializeRoles();