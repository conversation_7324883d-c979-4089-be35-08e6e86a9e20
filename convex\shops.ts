import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  requireAuth,
  requirePermission,
  requirePermissionWithWorkOSUser,
  requireAnyPermission,
  requireAnyPermissionWithWorkOSUser,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  ROLES,
  canAccessArea,
  getAccessibleAreaIds,
} from "./permissions";

// Get shops with role-based filtering
export const getShops = query({
  args: {
    areaId: v.optional(v.id("areas")),
    isActive: v.optional(v.boolean()),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_VIEW);

    let query = ctx.db.query("shops");

    // Apply role-based filtering
    if (!userWithPermissions.permissions.includes(PERMISSIONS.SHOP_VIEW)) {
      // If user doesn't have general shop view permission, they can only see shops in their areas
      const accessibleAreaIds = await getAccessibleAreaIds(ctx);
      if (accessibleAreaIds.length === 0) {
        return [];
      }
      query = query.filter((q) => 
        q.or(...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId)))
      );
    }

    // Apply additional filters
    if (args.areaId) {
      // Check if user can access this area
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied to this area");
      }
      query = query.filter((q) => q.eq(q.field("areaId"), args.areaId));
    }

    if (args.isActive !== undefined) {
      query = query.filter((q) => q.eq(q.field("isActive"), args.isActive));
    }

    const shops = await query
      .order("asc")
      .take(args.limit || 50);

    // Enrich with related data
    const enrichedShops = await Promise.all(
      shops.map(async (shop) => {
        const [area, manager, createdBy] = await Promise.all([
          ctx.db.get(shop.areaId),
          shop.managerId ? ctx.db.get(shop.managerId) : null,
          ctx.db.get(shop.createdBy),
        ]);

        return {
          ...shop,
          area,
          manager,
          createdBy,
        };
      })
    );

    return enrichedShops;
  },
});

// Get single shop by ID
export const getShop = query({
  args: { 
    shopId: v.id("shops"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_VIEW);

    const shop = await ctx.db.get(args.shopId);
    if (!shop) {
      return null;
    }

    // Check if user can access this shop's area (accounts role has access to all areas)
    if (args.workosUserId) {
      // For WorkOS users, check if they have accounts role
      const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
      if (!userWithPermissions?.roles.some(role => role.name === 'accounts')) {
        const canAccess = await canAccessArea(ctx, shop.areaId);
        if (!canAccess) {
          throw new Error("Access denied");
        }
      }
    } else {
      // For Convex Auth users, use the old method
      const canAccess = await canAccessArea(ctx, shop.areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    // Enrich with related data
    const [area, manager, createdBy] = await Promise.all([
      ctx.db.get(shop.areaId),
      shop.managerId ? ctx.db.get(shop.managerId) : null,
      ctx.db.get(shop.createdBy),
    ]);

    return {
      ...shop,
      area,
      manager,
      createdBy,
    };
  },
});

// Create new shop
export const createShop = mutation({
  args: {
    name: v.string(),
    code: v.string(),
    areaId: v.id("areas"),
    managerId: v.optional(v.id("users")),
    address: v.optional(v.string()),
    phone: v.optional(v.string()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_CREATE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_CREATE);

    // Check if user can access the area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Cannot create shop in this area");
      }
    }

    // Verify area exists and is active
    const area = await ctx.db.get(args.areaId);
    if (!area || !area.isActive) {
      throw new Error("Area not found or inactive");
    }

    // Check for duplicate shop code
    const existingShop = await ctx.db
      .query("shops")
      .withIndex("by_code", (q) => q.eq("code", args.code))
      .first();

    if (existingShop) {
      throw new Error("Shop with this code already exists");
    }

    // Check for duplicate shop name in the same area
    const existingNameInArea = await ctx.db
      .query("shops")
      .withIndex("by_area", (q) => q.eq("areaId", args.areaId))
      .filter((q) => q.eq(q.field("name"), args.name))
      .first();

    if (existingNameInArea) {
      throw new Error("Shop with this name already exists in this area");
    }

    // If manager is specified, verify they exist and can be assigned
    if (args.managerId) {
      const manager = await ctx.db.get(args.managerId);
      if (!manager || !manager.isActive) {
        throw new Error("Manager not found or inactive");
      }

      // Check if manager has shop_manager role
      const managerRoles = await ctx.db
        .query("user_roles")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", args.managerId).eq("isActive", true)
        )
        .collect();

      const roles = await Promise.all(
        managerRoles.map(async (userRole) => {
          const role = await ctx.db.get(userRole.roleId);
          return role;
        })
      );

      const hasManagerRole = roles.some(role => role?.name === ROLES.SHOP_MANAGER);
      if (!hasManagerRole) {
        throw new Error("User must have shop_manager role to be assigned as manager");
      }
    }

    const now = Date.now();
    const shopId = await ctx.db.insert("shops", {
      name: args.name,
      code: args.code,
      areaId: args.areaId,
      managerId: args.managerId,
      address: args.address,
      phone: args.phone,
      isActive: true,
      createdAt: now,
      updatedAt: now,
      createdBy: userWithPermissions.user._id,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "shop_created",
      entityType: "shop",
      entityId: shopId,
      userId: userWithPermissions.user._id,
      newValues: args,
      timestamp: now,
    });

    return shopId;
  },
});

// Update shop
export const updateShop = mutation({
  args: {
    shopId: v.id("shops"),
    name: v.optional(v.string()),
    code: v.optional(v.string()),
    areaId: v.optional(v.id("areas")),
    managerId: v.optional(v.id("users")),
    address: v.optional(v.string()),
    phone: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_UPDATE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_UPDATE);

    const { shopId, workosUserId, ...updates } = args;
    const shop = await ctx.db.get(shopId);
    if (!shop) {
      throw new Error("Shop not found");
    }

    // Check if user can access the current shop's area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccessCurrent = await canAccessArea(ctx, shop.areaId);
      if (!canAccessCurrent) {
        throw new Error("Access denied");
      }

      // If area is being changed, check access to new area
      if (updates.areaId && updates.areaId !== shop.areaId) {
        const canAccessNew = await canAccessArea(ctx, updates.areaId);
        if (!canAccessNew) {
          throw new Error("Cannot move shop to this area");
        }
      }
    } else if (updates.areaId && updates.areaId !== shop.areaId) {
      // For accounts role, still verify new area exists and is active

      // Verify new area exists and is active
      const newArea = await ctx.db.get(updates.areaId);
      if (!newArea || !newArea.isActive) {
        throw new Error("New area not found or inactive");
      }
    }

    // Check for duplicate shop code (excluding current shop)
    if (updates.code && updates.code !== shop.code) {
      const existingShop = await ctx.db
        .query("shops")
        .withIndex("by_code", (q) => q.eq("code", updates.code))
        .filter((q) => q.neq(q.field("_id"), shopId))
        .first();

      if (existingShop) {
        throw new Error("Shop with this code already exists");
      }
    }

    // Check for duplicate shop name in the area
    if (updates.name && updates.name !== shop.name) {
      const targetAreaId = updates.areaId || shop.areaId;
      const existingNameInArea = await ctx.db
        .query("shops")
        .withIndex("by_area", (q) => q.eq("areaId", targetAreaId))
        .filter((q) => 
          q.and(
            q.eq(q.field("name"), updates.name),
            q.neq(q.field("_id"), shopId)
          )
        )
        .first();

      if (existingNameInArea) {
        throw new Error("Shop with this name already exists in this area");
      }
    }

    // If manager is being changed, verify they exist and can be assigned
    if (updates.managerId !== undefined) {
      if (updates.managerId) {
        const manager = await ctx.db.get(updates.managerId);
        if (!manager || !manager.isActive) {
          throw new Error("Manager not found or inactive");
        }

        // Check if manager has shop_manager role
        const managerRoles = await ctx.db
          .query("user_roles")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", updates.managerId).eq("isActive", true)
          )
          .collect();

        const roles = await Promise.all(
          managerRoles.map(async (userRole) => {
            const role = await ctx.db.get(userRole.roleId);
            return role;
          })
        );

        const hasManagerRole = roles.some(role => role?.name === ROLES.SHOP_MANAGER);
        if (!hasManagerRole) {
          throw new Error("User must have shop_manager role to be assigned as manager");
        }
      }
    }

    const now = Date.now();
    await ctx.db.patch(shopId, {
      ...updates,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "shop_updated",
      entityType: "shop",
      entityId: shopId,
      userId: userWithPermissions.user._id,
      oldValues: shop,
      newValues: updates,
      timestamp: now,
    });

    return shopId;
  },
});

// Delete shop (soft delete)
export const deleteShop = mutation({
  args: { 
    shopId: v.id("shops"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_DELETE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_DELETE);

    const shop = await ctx.db.get(args.shopId);
    if (!shop) {
      throw new Error("Shop not found");
    }

    // Check if user can access this shop's area (accounts role has access to all areas)
    if (!userWithPermissions.roles.some(role => role.name === 'accounts')) {
      const canAccess = await canAccessArea(ctx, shop.areaId);
      if (!canAccess) {
        throw new Error("Access denied");
      }
    }

    // Check if shop has any pending requests
    const pendingRequests = await ctx.db
      .query("requests")
      .withIndex("by_shop_status", (q) => 
        q.eq("shopId", args.shopId).eq("status", "pending")
      )
      .first();

    if (pendingRequests) {
      throw new Error("Cannot delete shop with pending requests");
    }

    const now = Date.now();
    await ctx.db.patch(args.shopId, {
      isActive: false,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "shop_deleted",
      entityType: "shop",
      entityId: args.shopId,
      userId: userWithPermissions.user._id,
      oldValues: shop,
      timestamp: now,
    });

    return args.shopId;
  },
});

// Assign manager to shop
export const assignManager = mutation({
  args: {
    shopId: v.id("shops"),
    managerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SHOP_ASSIGN_MANAGER);

    const shop = await ctx.db.get(args.shopId);
    if (!shop) {
      throw new Error("Shop not found");
    }

    // Check if user can access this shop's area
    const canAccess = await canAccessArea(ctx, shop.areaId);
    if (!canAccess) {
      throw new Error("Access denied");
    }

    // Verify manager exists and is active
    const manager = await ctx.db.get(args.managerId);
    if (!manager || !manager.isActive) {
      throw new Error("Manager not found or inactive");
    }

    // Check if manager has shop_manager role
    const managerRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.managerId).eq("isActive", true)
      )
      .collect();

    const roles = await Promise.all(
      managerRoles.map(async (userRole) => {
        const role = await ctx.db.get(userRole.roleId);
        return role;
      })
    );

    const hasManagerRole = roles.some(role => role?.name === ROLES.SHOP_MANAGER);
    if (!hasManagerRole) {
      throw new Error("User must have shop_manager role to be assigned as manager");
    }

    const now = Date.now();
    const oldManagerId = shop.managerId;

    await ctx.db.patch(args.shopId, {
      managerId: args.managerId,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "shop_manager_assigned",
      entityType: "shop",
      entityId: args.shopId,
      userId: userWithPermissions.user._id,
      oldValues: { managerId: oldManagerId },
      newValues: { managerId: args.managerId },
      timestamp: now,
    });

    return args.shopId;
  },
});

// Remove manager from shop
export const removeManager = mutation({
  args: { shopId: v.id("shops") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SHOP_ASSIGN_MANAGER);

    const shop = await ctx.db.get(args.shopId);
    if (!shop) {
      throw new Error("Shop not found");
    }

    // Check if user can access this shop's area
    const canAccess = await canAccessArea(ctx, shop.areaId);
    if (!canAccess) {
      throw new Error("Access denied");
    }

    if (!shop.managerId) {
      throw new Error("Shop does not have a manager assigned");
    }

    const now = Date.now();
    const oldManagerId = shop.managerId;

    await ctx.db.patch(args.shopId, {
      managerId: undefined,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "shop_manager_removed",
      entityType: "shop",
      entityId: args.shopId,
      userId: userWithPermissions.user._id,
      oldValues: { managerId: oldManagerId },
      newValues: { managerId: undefined },
      timestamp: now,
    });

    return args.shopId;
  },
});

// Get shops by manager
export const getShopsByManager = query({
  args: { 
    managerId: v.id("users"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.SHOP_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.SHOP_VIEW);

    // Check if user can view this manager's shops
    const canViewAll = userWithPermissions.permissions.includes(PERMISSIONS.SHOP_VIEW);
    const isOwnShops = userWithPermissions.user._id === args.managerId;

    if (!canViewAll && !isOwnShops) {
      throw new Error("Access denied");
    }

    const shops = await ctx.db
      .query("shops")
      .withIndex("by_manager", (q) => q.eq("managerId", args.managerId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Filter by accessible areas if not accounts role
    const accessibleAreaIds = await getAccessibleAreaIds(ctx);
    const filteredShops = canViewAll 
      ? shops 
      : shops.filter(shop => accessibleAreaIds.includes(shop.areaId));

    // Enrich with related data
    const enrichedShops = await Promise.all(
      filteredShops.map(async (shop) => {
        const [area, manager, createdBy] = await Promise.all([
          ctx.db.get(shop.areaId),
          shop.managerId ? ctx.db.get(shop.managerId) : null,
          ctx.db.get(shop.createdBy),
        ]);

        return {
          ...shop,
          area,
          manager,
          createdBy,
        };
      })
    );

    return enrichedShops;
  },
});

// Get shops for current user (shop manager's assigned shops)
export const getMyShops = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    console.log('[getMyShops] Query called with:', { workosUserId: args.workosUserId });
    
    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      console.log('[getMyShops] No user found, authentication required');
      throw new Error("Authentication required");
    }

    console.log('[getMyShops] User found:', {
      userId: userWithPermissions.user._id,
      roles: userWithPermissions.roles.map(r => r.name),
      hasAccountsRole: userWithPermissions.roles.some(role => role.name === ROLES.ACCOUNTS),
      hasShopManagerRole: userWithPermissions.roles.some(role => role.name === ROLES.SHOP_MANAGER)
    });

    // If user has accounts role, they can see all shops
    if (userWithPermissions.roles.some(role => role.name === ROLES.ACCOUNTS)) {
      console.log('[getMyShops] User has accounts role, returning all shops');
      
      const allShops = await ctx.db
        .query("shops")
        .filter((q) => q.eq(q.field("isActive"), true))
        .collect();

      console.log('[getMyShops] Found shops for accounts user:', allShops.length);

      // Enrich with related data
      const enrichedShops = await Promise.all(
        allShops.map(async (shop) => {
          const [area, manager, createdBy] = await Promise.all([
            ctx.db.get(shop.areaId),
            shop.managerId ? ctx.db.get(shop.managerId) : null,
            ctx.db.get(shop.createdBy),
          ]);

          return {
            ...shop,
            area,
            manager,
            createdBy,
          };
        })
      );

      console.log('[getMyShops] Returning enriched shops for accounts user:', enrichedShops.length);
      return enrichedShops;
    }

    // For shop managers, only return shops they manage
    console.log('[getMyShops] User is shop manager, querying managed shops');
    
    const shops = await ctx.db
      .query("shops")
      .withIndex("by_manager", (q) => q.eq("managerId", userWithPermissions.user._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    console.log('[getMyShops] Found managed shops:', {
      count: shops.length,
      shopIds: shops.map(s => s._id),
      shopNames: shops.map(s => s.name)
    });

    // Enrich with related data
    const enrichedShops = await Promise.all(
      shops.map(async (shop) => {
        const [area, manager, createdBy] = await Promise.all([
          ctx.db.get(shop.areaId),
          shop.managerId ? ctx.db.get(shop.managerId) : null,
          ctx.db.get(shop.createdBy),
        ]);

        return {
          ...shop,
          area,
          manager,
          createdBy,
        };
      })
    );

    console.log('[getMyShops] Returning enriched managed shops:', {
      count: enrichedShops.length,
      shops: enrichedShops.map(s => ({ id: s._id, name: s.name, areaId: s.areaId }))
    });

    return enrichedShops;
  },
});

// Get shop statistics
export const getShopStats = query({
  args: {
    areaId: v.optional(v.id("areas")),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAnyPermission(ctx, [
      PERMISSIONS.REPORTS_VIEW_SHOP,
      PERMISSIONS.REPORTS_VIEW_AREA,
      PERMISSIONS.REPORTS_VIEW_ALL,
    ]);

    let query = ctx.db.query("shops");

    // Apply role-based filtering
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REPORTS_VIEW_ALL)) {
      const accessibleAreaIds = await getAccessibleAreaIds(ctx);
      if (accessibleAreaIds.length === 0) {
        return { total: 0, active: 0, inactive: 0, withManager: 0, withoutManager: 0 };
      }
      query = query.filter((q) => 
        q.or(...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId)))
      );
    }

    // Apply additional filters
    if (args.areaId) {
      const canAccess = await canAccessArea(ctx, args.areaId);
      if (!canAccess) {
        throw new Error("Access denied to this area");
      }
      query = query.filter((q) => q.eq(q.field("areaId"), args.areaId));
    }

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("createdAt"), args.startDate));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("createdAt"), args.endDate));
    }

    const shops = await query.collect();

    const stats = shops.reduce(
      (acc, shop) => {
        acc.total++;
        if (shop.isActive) {
          acc.active++;
        } else {
          acc.inactive++;
        }
        if (shop.managerId) {
          acc.withManager++;
        } else {
          acc.withoutManager++;
        }
        return acc;
      },
      { total: 0, active: 0, inactive: 0, withManager: 0, withoutManager: 0 }
    );

    return stats;
  },
});
