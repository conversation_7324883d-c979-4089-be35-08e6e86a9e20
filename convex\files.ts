import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  requireAuth,
  requirePermission,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  canAccessShop,
} from "./permissions";

// Generate upload URL for file
export const generateUploadUrl = mutation({
  args: {
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // For file uploads, we need to handle WorkOS authentication
    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    return await ctx.storage.generateUploadUrl();
  },
});

// Upload file with metadata
export const uploadFile = mutation({
  args: {
    storageId: v.id("_storage"),
    filename: v.string(),
    contentType: v.string(),
    size: v.number(),
    relatedEntityType: v.optional(v.string()),
    relatedEntityId: v.optional(v.string()),
    isPublic: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    // Validate file size (10MB limit)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (args.size > maxSize) {
      throw new Error("File size exceeds 10MB limit");
    }

    // Validate content type
    const allowedTypes = [
      "image/jpeg",
      "image/jpg", 
      "image/png",
      "image/gif",
      "image/webp",
      "application/pdf",
      "text/plain",
      "text/csv",
    ];

    if (!allowedTypes.includes(args.contentType)) {
      throw new Error("File type not allowed");
    }

    // For ticket images, validate access to related entity
    if (args.relatedEntityType === "request" && args.relatedEntityId) {
      const request = await ctx.db.get(args.relatedEntityId as Id<"requests">);
      if (request) {
        const canAccess = await canAccessShop(ctx, request.shopId);
        if (!canAccess) {
          throw new Error("Access denied to upload file for this request");
        }
      }
    }

    const now = Date.now();
    const fileId = await ctx.db.insert("file_metadata", {
      storageId: args.storageId,
      filename: args.filename,
      contentType: args.contentType,
      size: args.size,
      uploadedBy: userWithPermissions.user._id,
      relatedEntityType: args.relatedEntityType,
      relatedEntityId: args.relatedEntityId,
      isPublic: args.isPublic || false,
      uploadedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "file_uploaded",
      entityType: "file",
      entityId: fileId,
      userId: userWithPermissions.user._id,
      newValues: {
        filename: args.filename,
        contentType: args.contentType,
        size: args.size,
        relatedEntityType: args.relatedEntityType,
        relatedEntityId: args.relatedEntityId,
      },
      timestamp: now,
    });

    return fileId;
  },
});

// Get file metadata
export const getFileMetadata = query({
  args: { fileId: v.id("file_metadata") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const file = await ctx.db.get(args.fileId);
    if (!file) {
      return null;
    }

    // Check access permissions
    if (file.isPublic) {
      return file;
    }

    // File owner can always access
    if (file.uploadedBy === userWithPermissions.user._id) {
      return file;
    }

    // Check access based on related entity
    if (file.relatedEntityType === "request" && file.relatedEntityId) {
      const request = await ctx.db.get(file.relatedEntityId as Id<"requests">);
      if (request) {
        const canAccess = await canAccessShop(ctx, request.shopId);
        if (!canAccess) {
          throw new Error("Access denied");
        }
      }
    }

    // Accounts team can access all files
    if (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL)) {
      return file;
    }

    throw new Error("Access denied");
  },
});

// Get download URL for file by storage ID (for ticket images)
export const getImageUrl = query({
  args: { storageId: v.id("_storage") },
  handler: async (ctx, args) => {
    // For ticket images, we can provide the URL directly from storage ID
    // Access control is handled at the request level
    const downloadUrl = await ctx.storage.getUrl(args.storageId);
    return downloadUrl;
  },
});

// Get download URL for file by file metadata ID
export const getDownloadUrl = mutation({
  args: { fileId: v.id("file_metadata") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const file = await ctx.db.get(args.fileId);
    if (!file) {
      throw new Error("File not found");
    }

    // Check access permissions
    if (!file.isPublic) {
      // File owner can always access
      if (file.uploadedBy !== userWithPermissions.user._id) {
        // Check access based on related entity
        if (file.relatedEntityType === "request" && file.relatedEntityId) {
          const request = await ctx.db.get(file.relatedEntityId as Id<"requests">);
          if (request) {
            const canAccess = await canAccessShop(ctx, request.shopId);
            if (!canAccess) {
              throw new Error("Access denied");
            }
          }
        } else if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL)) {
          throw new Error("Access denied");
        }
      }
    }

    const downloadUrl = await ctx.storage.getUrl(file.storageId);
    return downloadUrl;
  },
});

// Delete file
export const deleteFile = mutation({
  args: { fileId: v.id("file_metadata") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const file = await ctx.db.get(args.fileId);
    if (!file) {
      throw new Error("File not found");
    }

    // Only file owner or accounts team can delete
    const canDelete = 
      file.uploadedBy === userWithPermissions.user._id ||
      userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL);

    if (!canDelete) {
      throw new Error("Access denied");
    }

    // Delete from storage
    await ctx.storage.delete(file.storageId);

    // Delete metadata
    await ctx.db.delete(args.fileId);

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "file_deleted",
      entityType: "file",
      entityId: args.fileId,
      userId: userWithPermissions.user._id,
      oldValues: file,
      timestamp: Date.now(),
    });

    return args.fileId;
  },
});

// Upload user avatar
export const uploadAvatar = mutation({
  args: {
    storageId: v.id("_storage"),
    filename: v.string(),
    contentType: v.string(),
    size: v.number(),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    // Validate file size (2MB limit for avatars)
    const maxSize = 2 * 1024 * 1024; // 2MB
    if (args.size > maxSize) {
      throw new Error("Avatar file size exceeds 2MB limit");
    }

    // Validate content type (images only)
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];

    if (!allowedTypes.includes(args.contentType)) {
      throw new Error("Avatar must be an image file");
    }

    // Delete old avatar if exists
    const oldAvatarFile = await ctx.db
      .query("file_metadata")
      .withIndex("by_related_entity", (q) => 
        q.eq("relatedEntityType", "user_avatar").eq("relatedEntityId", userWithPermissions.user._id)
      )
      .first();

    if (oldAvatarFile) {
      await ctx.storage.delete(oldAvatarFile.storageId);
      await ctx.db.delete(oldAvatarFile._id);
    }

    const now = Date.now();
    const fileId = await ctx.db.insert("file_metadata", {
      storageId: args.storageId,
      filename: args.filename,
      contentType: args.contentType,
      size: args.size,
      uploadedBy: userWithPermissions.user._id,
      relatedEntityType: "user_avatar",
      relatedEntityId: userWithPermissions.user._id,
      isPublic: false,
      uploadedAt: now,
    });

    // Update user profile picture reference
    await ctx.db.patch(userWithPermissions.user._id, {
      profilePicture: args.storageId,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "avatar_uploaded",
      entityType: "file",
      entityId: fileId,
      userId: userWithPermissions.user._id,
      newValues: {
        filename: args.filename,
        contentType: args.contentType,
        size: args.size,
      },
      timestamp: now,
    });

    return fileId;
  },
});

// Get user avatar URL
export const getAvatarUrl = query({
  args: { userId: v.optional(v.id("users")) },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);
    const targetUserId = args.userId || userWithPermissions.user._id;

    const user = await ctx.db.get(targetUserId);
    if (!user || !user.profilePicture) {
      return null;
    }

    // Anyone can view avatars (they're considered semi-public)
    const avatarUrl = await ctx.storage.getUrl(user.profilePicture);
    return avatarUrl;
  },
});

// Get files by entity
export const getEntityFiles = query({
  args: {
    entityType: v.string(),
    entityId: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    // Check access based on entity type
    if (args.entityType === "request") {
      const request = await ctx.db.get(args.entityId as Id<"requests">);
      if (request) {
        const canAccess = await canAccessShop(ctx, request.shopId);
        if (!canAccess) {
          throw new Error("Access denied");
        }
      }
    }

    const files = await ctx.db
      .query("file_metadata")
      .withIndex("by_related_entity", (q) => 
        q.eq("relatedEntityType", args.entityType).eq("relatedEntityId", args.entityId)
      )
      .order("desc")
      .take(args.limit || 10);

    // Enrich with uploader info
    const enrichedFiles = await Promise.all(
      files.map(async (file) => {
        const uploadedBy = await ctx.db.get(file.uploadedBy);
        return {
          ...file,
          uploadedBy,
        };
      })
    );

    return enrichedFiles;
  },
});

// Get user's uploaded files
export const getUserFiles = query({
  args: {
    userId: v.optional(v.id("users")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);
    const targetUserId = args.userId || userWithPermissions.user._id;

    // Users can only see their own files unless they have admin permissions
    if (targetUserId !== userWithPermissions.user._id && 
        !userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL)) {
      throw new Error("Access denied");
    }

    const files = await ctx.db
      .query("file_metadata")
      .withIndex("by_uploaded_by", (q) => q.eq("uploadedBy", targetUserId))
      .order("desc")
      .take(args.limit || 20);

    return files;
  },
});

// Process image for ticket upload (resize and compress)
export const processTicketImage = mutation({
  args: {
    storageId: v.id("_storage"),
    filename: v.string(),
    contentType: v.string(),
    size: v.number(),
    requestId: v.id("requests"),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    // Verify access to the request
    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    const canAccess = await canAccessShop(ctx, request.shopId);
    if (!canAccess) {
      throw new Error("Access denied");
    }

    // Validate file is an image
    const allowedTypes = [
      "image/jpeg",
      "image/jpg",
      "image/png",
      "image/gif",
      "image/webp",
    ];

    if (!allowedTypes.includes(args.contentType)) {
      throw new Error("File must be an image");
    }

    // Validate file size (5MB limit for ticket images)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (args.size > maxSize) {
      throw new Error("Image file size exceeds 5MB limit");
    }

    const now = Date.now();
    const fileId = await ctx.db.insert("file_metadata", {
      storageId: args.storageId,
      filename: args.filename,
      contentType: args.contentType,
      size: args.size,
      uploadedBy: userWithPermissions.user._id,
      relatedEntityType: "request",
      relatedEntityId: args.requestId,
      isPublic: false,
      uploadedAt: now,
    });

    // Update request with ticket image
    await ctx.db.patch(args.requestId, {
      ticketImageId: args.storageId,
      updatedAt: now,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "ticket_image_uploaded",
      entityType: "file",
      entityId: fileId,
      userId: userWithPermissions.user._id,
      newValues: {
        filename: args.filename,
        requestId: args.requestId,
        size: args.size,
      },
      timestamp: now,
    });

    return fileId;
  },
});

// Get file statistics
export const getFileStats = query({
  args: {
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    // Only accounts team can view file statistics
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REPORTS_VIEW_ALL)) {
      throw new Error("Access denied");
    }

    let query = ctx.db.query("file_metadata");

    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("uploadedAt"), args.startDate));
    }

    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("uploadedAt"), args.endDate));
    }

    const files = await query.collect();

    const stats = files.reduce(
      (acc, file) => {
        acc.totalFiles++;
        acc.totalSize += file.size;

        if (file.relatedEntityType === "request") {
          acc.ticketImages++;
        } else if (file.relatedEntityType === "user_avatar") {
          acc.avatars++;
        } else {
          acc.other++;
        }

        // Count by content type
        if (file.contentType.startsWith("image/")) {
          acc.images++;
        } else if (file.contentType === "application/pdf") {
          acc.pdfs++;
        } else {
          acc.other++;
        }

        return acc;
      },
      {
        totalFiles: 0,
        totalSize: 0,
        ticketImages: 0,
        avatars: 0,
        images: 0,
        pdfs: 0,
        other: 0,
      }
    );

    return stats;
  },
});

// Clean up orphaned files (files not referenced by any entity)
export const cleanupOrphanedFiles = mutation({
  args: {},
  handler: async (ctx) => {
    const userWithPermissions = await requireAuth(ctx);

    // Only accounts team can run cleanup
    if (!userWithPermissions.permissions.includes(PERMISSIONS.SETTINGS_UPDATE)) {
      throw new Error("Access denied");
    }

    const files = await ctx.db.query("file_metadata").collect();
    const orphanedFiles: Id<"file_metadata">[] = [];

    for (const file of files) {
      let isOrphaned = false;

      if (file.relatedEntityType === "request" && file.relatedEntityId) {
        const request = await ctx.db.get(file.relatedEntityId as Id<"requests">);
        if (!request || request.ticketImageId !== file.storageId) {
          isOrphaned = true;
        }
      } else if (file.relatedEntityType === "user_avatar" && file.relatedEntityId) {
        const user = await ctx.db.get(file.relatedEntityId as Id<"users">);
        if (!user || user.profilePicture !== file.storageId) {
          isOrphaned = true;
        }
      }

      if (isOrphaned) {
        orphanedFiles.push(file._id);
        // Delete from storage
        await ctx.storage.delete(file.storageId);
        // Delete metadata
        await ctx.db.delete(file._id);
      }
    }

    // Log the cleanup action
    if (orphanedFiles.length > 0) {
      await ctx.db.insert("audit_logs", {
        action: "files_cleanup",
        entityType: "file",
        entityId: "bulk",
        userId: userWithPermissions.user._id,
        newValues: { cleanedFiles: orphanedFiles.length },
        timestamp: Date.now(),
      });
    }

    return orphanedFiles.length;
  },
});
