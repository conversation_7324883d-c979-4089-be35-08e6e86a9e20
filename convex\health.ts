import { query } from "./_generated/server";

// Simple health check that doesn't require authentication
export const healthCheck = query({
  args: {},
  handler: async (ctx) => {
    return {
      status: "healthy",
      timestamp: Date.now(),
      message: "Convex backend is running successfully"
    };
  },
});

// Check if settings table exists
export const checkSettingsTable = query({
  args: {},
  handler: async (ctx) => {
    try {
      const settingsCount = await ctx.db.query("settings").collect();
      return {
        settingsTableExists: true,
        settingsCount: settingsCount.length,
        message: "Settings table is accessible"
      };
    } catch (error) {
      return {
        settingsTableExists: false,
        error: error.message,
        message: "Settings table not accessible"
      };
    }
  },
});