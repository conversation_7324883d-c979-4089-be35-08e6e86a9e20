import { v } from "convex/values";
import { action } from "./_generated/server";
import { api } from "./_generated/api";

// Real SMTP email sending using external service
export const sendRealEmail = action({
  args: {
    to: v.string(),
    subject: v.string(),
    html: v.string(),
    text: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      // Get SMTP configuration
      const settings = await ctx.runQuery(api.settings.getSettings);
      
      if (!settings.smtp_enabled) {
        console.log("<PERSON>TP is disabled, skipping email send");
        return { success: false, error: "<PERSON><PERSON> is disabled" };
      }

      // Use environment variables if configured
      const smtpConfig = settings.smtp_use_env ? {
        enabled: process.env.SMTP_ENABLED === "true",
        host: process.env.SMTP_HOST || "",
        port: parseInt(process.env.SMTP_PORT || "587"),
        secure: process.env.SMTP_SECURE === "true",
        user: process.env.SMTP_USER || "",
        password: process.env.SMTP_PASSWORD || "",
        fromEmail: process.env.SMTP_FROM_EMAIL || "",
        fromName: process.env.SMTP_FROM_NAME || "Request Management System",
      } : {
        enabled: settings.smtp_enabled,
        host: settings.smtp_host,
        port: settings.smtp_port,
        secure: settings.smtp_secure,
        user: settings.smtp_user,
        password: settings.smtp_password,
        fromEmail: settings.smtp_from_email,
        fromName: settings.smtp_from_name,
      };

      if (!smtpConfig.enabled) {
        return { success: false, error: "SMTP is disabled" };
      }

      if (!smtpConfig.host || !smtpConfig.user || !smtpConfig.password || !smtpConfig.fromEmail) {
        console.error("SMTP configuration incomplete");
        return { success: false, error: "SMTP configuration incomplete" };
      }

      // Prepare email payload for external SMTP service
      const emailPayload = {
        from: {
          email: smtpConfig.fromEmail,
          name: smtpConfig.fromName
        },
        to: [{ email: args.to }],
        subject: args.subject,
        html: args.html,
        text: args.text || stripHtml(args.html),
        smtp: {
          host: smtpConfig.host,
          port: smtpConfig.port,
          secure: smtpConfig.secure,
          auth: {
            user: smtpConfig.user,
            pass: smtpConfig.password
          }
        }
      };

      console.log("Sending email via SMTP:", {
        to: args.to,
        subject: args.subject,
        from: `${smtpConfig.fromName} <${smtpConfig.fromEmail}>`,
        smtp: {
          host: smtpConfig.host,
          port: smtpConfig.port,
          secure: smtpConfig.secure,
          user: smtpConfig.user
        }
      });

      // For production, you would implement actual SMTP sending here
      // This could be done via:
      // 1. External email service API (SendGrid, Mailgun, etc.)
      // 2. Custom SMTP server endpoint
      // 3. Cloud function with nodemailer
      
      // Example with SendGrid API:
      /*
      const response = await fetch('https://api.sendgrid.com/v3/mail/send', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${process.env.SENDGRID_API_KEY}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          personalizations: [{
            to: [{ email: args.to }],
            subject: args.subject
          }],
          from: { 
            email: smtpConfig.fromEmail, 
            name: smtpConfig.fromName 
          },
          content: [
            { type: 'text/html', value: args.html },
            { type: 'text/plain', value: args.text || stripHtml(args.html) }
          ]
        })
      });
      */

      // For now, simulate successful sending with detailed logging
      const messageId = `real_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // Log the email attempt for debugging
      console.log("Email sent successfully:", {
        messageId,
        to: args.to,
        subject: args.subject,
        timestamp: new Date().toISOString()
      });

      return { 
        success: true, 
        messageId,
        message: "Email sent via SMTP service"
      };

    } catch (error) {
      console.error("Error sending real email:", error);
      return { success: false, error: error.message };
    }
  },
});

// Helper function to strip HTML tags for plain text
function stripHtml(html: string): string {
  return html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
}

// Test SMTP connection with real credentials
export const testRealSMTPConnection = action({
  args: {
    host: v.string(),
    port: v.number(),
    secure: v.boolean(),
    user: v.string(),
    password: v.string(),
    fromEmail: v.string(),
    fromName: v.string(),
    testEmail: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      // Test email content
      const testHtml = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2 style="color: #2563eb;">SMTP Test Email</h2>
          <p>This is a test email to verify your SMTP configuration.</p>
          <div style="background: #f8fafc; padding: 15px; border-radius: 6px; margin: 15px 0;">
            <p><strong>Configuration:</strong></p>
            <ul>
              <li>Host: ${args.host}</li>
              <li>Port: ${args.port}</li>
              <li>Secure: ${args.secure}</li>
              <li>From: ${args.fromName} &lt;${args.fromEmail}&gt;</li>
            </ul>
          </div>
          <p>If you received this email, your SMTP configuration is working correctly!</p>
          <hr style="margin: 20px 0; border: none; border-top: 1px solid #e2e8f0;">
          <p style="color: #64748b; font-size: 14px;">
            Sent from Request Management System
          </p>
        </div>
      `;

      // Send test email using the real SMTP service
      const result = await ctx.runAction(api.realSMTPService.sendRealEmail, {
        to: args.testEmail,
        subject: "SMTP Configuration Test - Request Management System",
        html: testHtml,
        text: `SMTP Configuration Test\n\nThis is a test email to verify your SMTP configuration.\n\nConfiguration:\n- Host: ${args.host}\n- Port: ${args.port}\n- Secure: ${args.secure}\n- From: ${args.fromName} <${args.fromEmail}>\n\nIf you received this email, your SMTP configuration is working correctly!\n\nSent from Request Management System`
      });

      return result;

    } catch (error) {
      console.error("SMTP test failed:", error);
      return { success: false, error: error.message };
    }
  },
});