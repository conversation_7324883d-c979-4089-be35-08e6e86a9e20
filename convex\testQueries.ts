import { query } from "./_generated/server";
import { v } from "convex/values";

// Simple query to check if users exist (no auth required)
export const countUsers = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    return {
      total: users.length,
      users: users.map(u => ({
        id: u._id,
        email: u.email,
        workosId: u.workosId,
        createdAt: u.createdAt
      }))
    };
  },
});

// Check for test user specifically
export const findTestUser = query({
  args: { workosId: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", args.workosId))
      .unique();
    
    return user ? {
      id: user._id,
      email: user.email,
      workosId: user.workosId,
      firstName: user.firstName,
      lastName: user.lastName,
      createdAt: user.createdAt
    } : null;
  },
});