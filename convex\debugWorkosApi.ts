import { action } from "./_generated/server";
import { v } from "convex/values";

/**
 * Debug WorkOS API calls to understand role assignment
 */

// Get user details from WorkOS including organization memberships
export const getWorkOSUserDetails = action({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    try {
      console.log("Getting WorkOS user details for:", args.userId);
      
      // Get user details
      const userResponse = await fetch(`https://api.workos.com/user_management/users/${args.userId}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
          "Content-Type": "application/json",
        },
      });

      if (!userResponse.ok) {
        const errorText = await userResponse.text();
        console.error(`WorkOS API error: ${userResponse.status} ${userResponse.statusText}`, errorText);
        throw new Error(`WorkOS API error: ${userResponse.status} ${userResponse.statusText}`);
      }

      const userData = await userResponse.json();
      console.log("User data:", userData);
      
      return userData;
      
    } catch (error) {
      console.error("Error getting WorkOS user details:", error);
      throw error;
    }
  },
});

// Get organization memberships for a user
export const getWorkOSUserOrganizations = action({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    try {
      console.log("Getting WorkOS user organizations for:", args.userId);
      
      // Get organization memberships
      const response = await fetch(`https://api.workos.com/user_management/organization_memberships?user_id=${args.userId}`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`WorkOS API error: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`WorkOS API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Organization memberships:", data);
      
      return data;
      
    } catch (error) {
      console.error("Error getting WorkOS user organizations:", error);
      throw error;
    }
  },
});

// List all organizations in WorkOS
export const listWorkOSOrganizations = action({
  args: {},
  handler: async (ctx) => {
    try {
      console.log("Listing WorkOS organizations...");
      
      const response = await fetch("https://api.workos.com/organizations", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`WorkOS API error: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`WorkOS API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Organizations:", data);
      
      return data;
      
    } catch (error) {
      console.error("Error listing WorkOS organizations:", error);
      throw error;
    }
  },
});