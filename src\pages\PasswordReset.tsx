import React from 'react';
import { RequestResetForm } from '@/components/auth/RequestResetForm';
import { ResetPasswordForm } from '@/components/auth/ResetPasswordForm';
import { useSearchParams } from 'react-router-dom';

const PasswordReset: React.FC = () => {
  const [searchParams] = useSearchParams();
  const token = searchParams.get('token');

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
      <div className="w-full max-w-[420px]">
        <div className="bg-white rounded-2xl shadow-[0_4px_20px_rgba(0,0,0,0.05)] p-8">
          {token ? <ResetPasswordForm token={token} /> : <RequestResetForm />}
        </div>
      </div>
    </div>
  );
};

export default PasswordReset;
