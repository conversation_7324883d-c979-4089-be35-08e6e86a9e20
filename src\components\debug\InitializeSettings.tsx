import React, { useState } from 'react';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react';

/**
 * Component to initialize default settings if they don't exist
 */
export const InitializeSettings: React.FC = () => {
  const [isInitializing, setIsInitializing] = useState(false);
  const [initResult, setInitResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // Query current settings using public endpoint
  const currentSettings = useQuery(api.simpleInit.getPublicBasicSettings);

  // Mutations for initialization
  const publicInit = useMutation(api.simpleInit.initializePublicSettings);
  const simpleInit = useMutation(api.simpleInit.initializeDefaultSettings);

  // Check if threshold settings exist
  const hasThresholds = currentSettings?.mobile_money_approval_threshold !== undefined && 
                       currentSettings?.bank_transfer_approval_threshold !== undefined;

  const handleInitialize = async () => {
    setIsInitializing(true);
    setError(null);
    setInitResult(null);

    try {
      console.log('🔧 Attempting to initialize default settings...');

      // Try public initialization first (no auth required)
      try {
        const result = await publicInit({});
        setInitResult(result);
        console.log('✅ Public initialization successful:', result);
      } catch (publicError) {
        console.log('⚠️ Public init failed, trying authenticated init...', publicError);

        // Fallback to authenticated initialization
        try {
          const authResult = await simpleInit({});
          setInitResult(authResult);
          console.log('✅ Authenticated initialization successful:', authResult);
        } catch (authError) {
          console.log('❌ Both initialization methods failed:', authError);
          throw authError;
        }
      }
      
    } catch (finalError: any) {
      console.error('❌ All initialization attempts failed:', finalError);
      setError(finalError.message || 'Failed to initialize settings');
    } finally {
      setIsInitializing(false);
    }
  };

  const handleRefresh = () => {
    setInitResult(null);
    setError(null);
    // The query will automatically refetch
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔧 Settings Initialization
          {currentSettings === undefined && <Loader2 className="h-4 w-4 animate-spin" />}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status */}
        <div>
          <h3 className="font-semibold mb-2">Current Status</h3>
          {currentSettings === undefined ? (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>Loading current settings...</AlertDescription>
            </Alert>
          ) : hasThresholds ? (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription className="text-green-700">
                ✅ Threshold settings are properly configured!
                <div className="mt-2 text-sm">
                  • Mobile Money Threshold: ¢{currentSettings.mobile_money_approval_threshold?.toLocaleString()}
                  <br />
                  • Bank Transfer Threshold: ¢{currentSettings.bank_transfer_approval_threshold?.toLocaleString()}
                </div>
              </AlertDescription>
            </Alert>
          ) : (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                ⚠️ Threshold settings are missing from the database!
                <div className="mt-2 text-sm">
                  Missing: mobile_money_approval_threshold, bank_transfer_approval_threshold
                </div>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Initialization Controls */}
        {!hasThresholds && (
          <div>
            <h3 className="font-semibold mb-2">Initialize Default Settings</h3>
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">
                Click the button below to create the default threshold settings:
              </p>
              <ul className="text-sm text-muted-foreground list-disc list-inside">
                <li>Mobile Money Approval Threshold: ¢5,000</li>
                <li>Bank Transfer Approval Threshold: ¢10,000</li>
                <li>Whitelisted Domains: kmkentertainment.com, mybet.africa</li>
              </ul>
              
              <Button 
                onClick={handleInitialize} 
                disabled={isInitializing}
                className="w-full"
              >
                {isInitializing ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Initializing Settings...
                  </>
                ) : (
                  <>
                    🔧 Initialize Default Settings
                  </>
                )}
              </Button>
            </div>
          </div>
        )}

        {/* Results */}
        {initResult && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              ✅ Settings initialized successfully!
              <div className="mt-2">
                <Button variant="outline" size="sm" onClick={handleRefresh}>
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Refresh Status
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              ❌ Error: {error}
              <div className="mt-2">
                <Button variant="outline" size="sm" onClick={handleInitialize}>
                  🔄 Try Again
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Current Settings Display */}
        {currentSettings && (
          <div>
            <h3 className="font-semibold mb-2">Current Settings in Database</h3>
            <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto max-h-40">
              {JSON.stringify(currentSettings, null, 2)}
            </pre>
          </div>
        )}

        {/* Browser Console Instructions */}
        <div>
          <h3 className="font-semibold mb-2">Alternative: Browser Console Method</h3>
          <p className="text-sm text-muted-foreground mb-2">
            You can also initialize settings using the browser console:
          </p>
          <div className="bg-muted p-3 rounded-lg text-xs font-mono">
            <div className="mb-2">1. Press F12 to open Developer Tools</div>
            <div className="mb-2">2. Go to Console tab</div>
            <div className="mb-2">3. Paste this code:</div>
            <code className="block bg-background p-2 rounded text-xs overflow-x-auto">
              {`window.convex.mutation('simpleInit:initializePublicSettings', {})`}
            </code>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
