/**
 * Central configuration file for the application
 * 
 * This file contains all the configuration values used throughout the application.
 * Centralizing these values makes it easier to maintain and update them.
 */

// Convex configuration
export const CONVEX_CONFIG = {
  URL: import.meta.env.VITE_CONVEX_URL || "",
};

// WorkOS AuthKit configuration
export const WORKOS_CONFIG = {
  CLIENT_ID: import.meta.env.VITE_WORKOS_CLIENT_ID || "",
  REDIRECT_URI: import.meta.env.VITE_WORKOS_REDIRECT_URI || `${import.meta.env.VITE_APP_URL || "http://localhost:8080"}/auth/callback`,
  // AuthKit specific configuration
  API_HOSTNAME: import.meta.env.VITE_WORKOS_API_HOSTNAME || "api.workos.com",
};

// Application settings
export const APP_CONFIG = {
  APP_NAME: "MyBet Africa Cash Management System",
  VERSION: "1.0.0",
  DEFAULT_CURRENCY: "GHS",
  DEFAULT_LOCALE: "en-GH",
  DEFAULT_DATE_FORMAT: "yyyy-MM-dd",
  DEFAULT_TIME_FORMAT: "HH:mm:ss",
  DEFAULT_DATETIME_FORMAT: "yyyy-MM-dd HH:mm:ss",
  DEFAULT_TIMEZONE: "Africa/Accra",
  APP_URL: import.meta.env.VITE_APP_URL || "http://localhost:5173",
};

// Default thresholds for request approvals
export const DEFAULT_THRESHOLDS = {
  MOBILE_MONEY: 5000,
  BANK_TRANSFER: 10000,
};

// Whitelisted domains for user invitations
export const WHITELISTED_DOMAINS = [
  "kmkentertainment.com",
  "mybet.africa",
];

// Environment validation
export const validateConfig = () => {
  const errors: string[] = [];

  if (!CONVEX_CONFIG.URL) {
    errors.push("VITE_CONVEX_URL is required");
  }

  if (!WORKOS_CONFIG.CLIENT_ID) {
    errors.push("VITE_WORKOS_CLIENT_ID is required");
  }

  if (!WORKOS_CONFIG.REDIRECT_URI) {
    errors.push("VITE_WORKOS_REDIRECT_URI is required");
  }

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join("\n")}`);
  }
};

// Development mode check
export const isDevelopment = import.meta.env.DEV;
export const isProduction = import.meta.env.PROD;

// API endpoints and URLs
export const API_CONFIG = {
  BASE_URL: APP_CONFIG.APP_URL,
  AUTH_CALLBACK_PATH: "/auth/callback",
  AUTH_LOGIN_PATH: "/auth/login",
  AUTH_LOGOUT_PATH: "/auth/logout",
};

// File upload configuration
export const FILE_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  MAX_AVATAR_SIZE: 2 * 1024 * 1024, // 2MB
  MAX_TICKET_IMAGE_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_IMAGE_TYPES: [
    "image/jpeg",
    "image/jpg",
    "image/png",
    "image/gif",
    "image/webp",
  ],
  ALLOWED_DOCUMENT_TYPES: [
    "application/pdf",
    "text/plain",
    "text/csv",
  ],
};

// Notification configuration
export const NOTIFICATION_CONFIG = {
  DEFAULT_TIMEOUT: 5000, // 5 seconds
  ERROR_TIMEOUT: 8000, // 8 seconds
  SUCCESS_TIMEOUT: 3000, // 3 seconds
  MAX_NOTIFICATIONS: 50,
};

// Pagination configuration
export const PAGINATION_CONFIG = {
  DEFAULT_PAGE_SIZE: 20,
  MAX_PAGE_SIZE: 100,
  DEFAULT_REQUESTS_PAGE_SIZE: 25,
  DEFAULT_USERS_PAGE_SIZE: 20,
  DEFAULT_SHOPS_PAGE_SIZE: 30,
};

// Date and time configuration
export const DATE_CONFIG = {
  DEFAULT_DATE_FORMAT: "yyyy-MM-dd",
  DISPLAY_DATE_FORMAT: "MMM dd, yyyy",
  DISPLAY_DATETIME_FORMAT: "MMM dd, yyyy HH:mm",
  TIME_FORMAT: "HH:mm",
  TIMEZONE: "Africa/Accra",
};

// Request configuration
export const REQUEST_CONFIG = {
  TICKET_NUMBER_MIN_LENGTH: 6,
  TICKET_NUMBER_MAX_LENGTH: 20,
  MIN_AMOUNT: 1,
  MAX_AMOUNT: 1000000,
  DEFAULT_PRIORITY: "medium" as const,
  AUTO_REFRESH_INTERVAL: 30000, // 30 seconds
};

// Cache configuration
export const CACHE_CONFIG = {
  DEFAULT_TTL: 5 * 60 * 1000, // 5 minutes
  USER_CACHE_TTL: 10 * 60 * 1000, // 10 minutes
  SETTINGS_CACHE_TTL: 15 * 60 * 1000, // 15 minutes
  STATS_CACHE_TTL: 2 * 60 * 1000, // 2 minutes
};
