import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { v } from "convex/values";

/**
 * Comprehensive test for WorkOS-Convex synchronization
 */
export const testBidirectionalSync = action({
  args: {},
  handler: async (ctx) => {
    const testResults = [];
    
    try {
      // Test 1: Create user in Convex via webhook simulation
      console.log("=== Test 1: Webhook User Creation ===");
      const webhookResult = await ctx.runAction(api.directWebhook.processWorkOSWebhook, {
        event: "user.created",
        data: {
          id: "user_webhook_test_" + Date.now(),
          email: "<EMAIL>",
          first_name: "Webhook",
          last_name: "Test",
          email_verified: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      });
      
      testResults.push({
        test: "Webhook User Creation",
        success: webhookResult.success,
        result: webhookResult
      });
      
      // Test 2: Verify user was created in Convex
      console.log("=== Test 2: Verify User in Convex ===");
      const userCount = await ctx.runQuery(api.testQueries.countUsers, {});
      testResults.push({
        test: "User Count in Convex",
        success: userCount.total > 0,
        result: `Found ${userCount.total} users`
      });
      
      // Test 3: Test WorkOS API connectivity
      console.log("=== Test 3: WorkOS API Connectivity ===");
      const workosUsers = await ctx.runAction(api.testWorkosApi.testListWorkOSUsers, {});
      testResults.push({
        test: "WorkOS API Connectivity",
        success: workosUsers.data.length > 0,
        result: `Found ${workosUsers.data.length} users in WorkOS`
      });
      
      // Test 4: Create user in WorkOS
      console.log("=== Test 4: Create User in WorkOS ===");
      const timestamp = Date.now();
      const newWorkOSUser = await ctx.runAction(api.testWorkosApi.testCreateUserInWorkOS, {
        email: `bidirectional-test-${timestamp}@example.com`,
        firstName: "Bidirectional",
        lastName: "Test"
      });
      
      testResults.push({
        test: "Create User in WorkOS",
        success: !!newWorkOSUser.id,
        result: `Created user ${newWorkOSUser.id} in WorkOS`
      });
      
      // Test 5: Simulate webhook for the newly created WorkOS user
      console.log("=== Test 5: Simulate Webhook for New WorkOS User ===");
      const syncBackResult = await ctx.runAction(api.directWebhook.processWorkOSWebhook, {
        event: "user.created",
        data: newWorkOSUser
      });
      
      testResults.push({
        test: "Sync WorkOS User Back to Convex",
        success: syncBackResult.success,
        result: syncBackResult
      });
      
      // Test 6: Verify the user exists in both systems
      console.log("=== Test 6: Verify Bidirectional Sync ===");
      const syncedUser = await ctx.runQuery(api.testQueries.findTestUser, {
        workosId: newWorkOSUser.id
      });
      
      testResults.push({
        test: "Bidirectional Sync Verification",
        success: !!syncedUser,
        result: syncedUser ? `User ${syncedUser.email} exists in both systems` : "User not found in Convex"
      });
      
      // Summary
      const successCount = testResults.filter(t => t.success).length;
      const totalTests = testResults.length;
      
      console.log(`=== Test Summary: ${successCount}/${totalTests} tests passed ===`);
      
      return {
        summary: {
          passed: successCount,
          total: totalTests,
          success: successCount === totalTests
        },
        tests: testResults,
        message: successCount === totalTests ? 
          "🎉 All synchronization tests passed!" : 
          `⚠️ ${totalTests - successCount} test(s) failed`
      };
      
    } catch (error) {
      console.error("Error in sync test:", error);
      return {
        summary: { passed: 0, total: testResults.length + 1, success: false },
        tests: testResults,
        error: error.message,
        message: "❌ Sync test failed with error"
      };
    }
  },
});