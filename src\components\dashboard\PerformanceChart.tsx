import React, { useMemo } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { Request } from '@/lib/types';
import { TrendingUp, TrendingDown } from 'lucide-react';
import { formatAmount } from '@/lib/currency';

interface PerformanceChartProps {
  requests: Request[];
}

const PerformanceChart: React.FC<PerformanceChartProps> = React.memo(({ requests }) => {

  // Process data for area performance
  const areaPerformance = useMemo(() => {
    const areaStats = new Map();

    requests.forEach(request => {
      const area = request.areaName || 'Unknown';
      if (!areaStats.has(area)) {
        areaStats.set(area, {
          name: area,
          totalRequests: 0,
          approvedRequests: 0,
          rejectedRequests: 0,
          pendingRequests: 0,
          totalAmount: 0,
          approvedAmount: 0
        });
      }

      const stats = areaStats.get(area);
      stats.totalRequests++;
      stats.totalAmount += request.amount;

      if (request.status === 'approved') {
        stats.approvedRequests++;
        stats.approvedAmount += request.amount;
      } else if (request.status === 'rejected') {
        stats.rejectedRequests++;
      } else if (request.status === 'pending') {
        stats.pendingRequests++;
      } else if (request.status === 'resubmitted') {
        stats.pendingRequests++; // Count resubmitted as pending
      }
    });

    return Array.from(areaStats.values()).map(area => ({
      ...area,
      approvalRate: area.totalRequests > 0 ? (area.approvedRequests / area.totalRequests) * 100 : 0,
      avgAmount: area.totalRequests > 0 ? area.totalAmount / area.totalRequests : 0
    })).sort((a, b) => b.totalAmount - a.totalAmount);
  }, [requests]);

  // Process data for shop performance
  const shopPerformance = useMemo(() => {
    const shopStats = new Map();

    requests.forEach(request => {
      const shop = request.shopName || 'Unknown';
      if (!shopStats.has(shop)) {
        shopStats.set(shop, {
          name: shop,
          totalRequests: 0,
          approvedRequests: 0,
          rejectedRequests: 0,
          pendingRequests: 0,
          totalAmount: 0,
          approvedAmount: 0
        });
      }

      const stats = shopStats.get(shop);
      stats.totalRequests++;
      stats.totalAmount += request.amount;

      if (request.status === 'approved') {
        stats.approvedRequests++;
        stats.approvedAmount += request.amount;
      } else if (request.status === 'rejected') {
        stats.rejectedRequests++;
      } else if (request.status === 'pending') {
        stats.pendingRequests++;
      } else if (request.status === 'resubmitted') {
        stats.pendingRequests++; // Count resubmitted as pending
      }
    });

    return Array.from(shopStats.values()).map(shop => ({
      ...shop,
      approvalRate: shop.totalRequests > 0 ? (shop.approvedRequests / shop.totalRequests) * 100 : 0,
      avgAmount: shop.totalRequests > 0 ? shop.totalAmount / shop.totalRequests : 0
    })).sort((a, b) => b.totalAmount - a.totalAmount);
  }, [requests]);

  // Colors for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className="rounded-lg bg-white shadow-lg border p-3 text-sm">
          <div className="font-medium mb-2">{label}</div>
          <div className="space-y-1">
            <div className="flex justify-between gap-4">
              <span className="text-gray-600">Total Requests:</span>
              <span className="font-medium">{data.totalRequests}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-gray-600">Approved:</span>
              <span className="font-medium text-green-600">{data.approvedRequests}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-gray-600">Total Amount:</span>
              <span className="font-medium">{formatAmount(data.totalAmount)}</span>
            </div>
            <div className="flex justify-between gap-4">
              <span className="text-gray-600">Approval Rate:</span>
              <span className="font-medium">{data.approvalRate.toFixed(1)}%</span>
            </div>
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card>
      <CardHeader>
        <div>
          <CardTitle>Performance Comparison</CardTitle>
          <CardDescription>Shop and area performance analysis</CardDescription>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="areas" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="areas">Areas</TabsTrigger>
            <TabsTrigger value="shops">Shops</TabsTrigger>
          </TabsList>

          <TabsContent value="areas" className="space-y-4">
            <div className="h-[400px] w-full">
              {areaPerformance.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={areaPerformance.slice(0, 8)}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 20,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#888', fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#888', fontSize: 12 }}
                      tickFormatter={(value) => formatAmount(value)}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar
                      dataKey="totalAmount"
                      fill="hsl(var(--primary))"
                      radius={[4, 4, 0, 0]}
                      maxBarSize={60}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  No area data available
                </div>
              )}
            </div>

            {/* Top performing areas summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {areaPerformance.slice(0, 3).map((area, index) => (
                <div key={area.name} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">{area.name}</h4>
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                  </div>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>Total:</span>
                      <span className="font-medium">{formatAmount(area.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Requests:</span>
                      <span>{area.totalRequests}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Approval Rate:</span>
                      <div className="flex items-center gap-1">
                        {area.approvalRate >= 70 ? (
                          <TrendingUp className="h-3 w-3 text-green-500" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-500" />
                        )}
                        <span className={area.approvalRate >= 70 ? 'text-green-600' : 'text-red-600'}>
                          {area.approvalRate.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="shops" className="space-y-4">
            <div className="h-[400px] w-full">
              {shopPerformance.length > 0 ? (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={shopPerformance.slice(0, 8)}
                    margin={{
                      top: 20,
                      right: 30,
                      left: 20,
                      bottom: 20,
                    }}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                    <XAxis
                      dataKey="name"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#888', fontSize: 12 }}
                      angle={-45}
                      textAnchor="end"
                      height={80}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fill: '#888', fontSize: 12 }}
                      tickFormatter={(value) => formatAmount(value)}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Bar
                      dataKey="totalAmount"
                      fill="hsl(var(--primary))"
                      radius={[4, 4, 0, 0]}
                      maxBarSize={60}
                    />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-full text-muted-foreground">
                  No shop data available
                </div>
              )}
            </div>

            {/* Top performing shops summary */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {shopPerformance.slice(0, 3).map((shop, index) => (
                <div key={shop.name} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-sm">{shop.name}</h4>
                    <span className="text-xs text-gray-500">#{index + 1}</span>
                  </div>
                  <div className="space-y-1 text-xs">
                    <div className="flex justify-between">
                      <span>Total:</span>
                      <span className="font-medium">{formatAmount(shop.totalAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Requests:</span>
                      <span>{shop.totalRequests}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span>Approval Rate:</span>
                      <div className="flex items-center gap-1">
                        {shop.approvalRate >= 70 ? (
                          <TrendingUp className="h-3 w-3 text-green-500" />
                        ) : (
                          <TrendingDown className="h-3 w-3 text-red-500" />
                        )}
                        <span className={shop.approvalRate >= 70 ? 'text-green-600' : 'text-red-600'}>
                          {shop.approvalRate.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
});

export default PerformanceChart;
