
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { RequestStatus } from '@/lib/types';
import { cn } from '@/lib/utils';

interface StatusBadgeProps {
  status: RequestStatus;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const getVariant = (status: RequestStatus) => {
    switch (status) {
      case 'approved':
        return 'bg-status-approved';
      case 'pending':
        return 'bg-status-pending';
      case 'rejected':
        return 'bg-status-rejected';
      case 'resubmitted':
        return 'bg-status-resubmitted';
      default:
        return '';
    }
  };

  const getLabel = (status: RequestStatus): string => {
    switch (status) {
      case 'approved':
        return 'Approved';
      case 'pending':
        return 'Pending';
      case 'rejected':
        return 'Rejected';
      case 'resubmitted':
        return 'Resubmitted';
      default:
        return status;
    }
  };

  return (
    <Badge 
      className={cn(
        'font-medium rounded-full px-3 py-1 transition-colors border-transparent', 
        getVariant(status), 
        className
      )}
      variant="outline"
    >
      {getLabel(status)}
    </Badge>
  );
};

export default StatusBadge;
