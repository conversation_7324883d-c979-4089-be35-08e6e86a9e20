import { action } from "./_generated/server";
import { api } from "./_generated/api";

/**
 * Comprehensive sync function to sync all users and their roles from WorkOS
 */
export const syncAllUsersFromWorkOS = action({
  args: {},
  handler: async (ctx) => {
    console.log("Starting comprehensive sync of all users from WorkOS...");
    
    const results = {
      usersProcessed: 0,
      rolesAssigned: 0,
      errors: [],
      details: []
    };
    
    try {
      // Get all users from WorkOS
      const usersResponse = await fetch("https://api.workos.com/user_management/users", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
          "Content-Type": "application/json",
        },
      });

      if (!usersResponse.ok) {
        throw new Error(`Failed to fetch users from WorkOS: ${usersResponse.status}`);
      }

      const usersData = await usersResponse.json();
      console.log(`Found ${usersData.data.length} users in WorkOS`);
      
      // Process each user
      for (const workosUser of usersData.data) {
        try {
          console.log(`Processing user: ${workosUser.email}`);
          results.usersProcessed++;
          
          // Get organization memberships for this user
          const membershipResponse = await fetch(
            `https://api.workos.com/user_management/organization_memberships?user_id=${workosUser.id}`,
            {
              method: "GET",
              headers: {
                "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
                "Content-Type": "application/json",
              },
            }
          );

          if (!membershipResponse.ok) {
            throw new Error(`Failed to fetch memberships for user ${workosUser.email}: ${membershipResponse.status}`);
          }

          const membershipData = await membershipResponse.json();
          console.log(`Found ${membershipData.data.length} memberships for ${workosUser.email}`);
          
          const userDetail = {
            email: workosUser.email,
            workosId: workosUser.id,
            memberships: membershipData.data.length,
            rolesAssigned: 0,
            status: 'success'
          };
          
          // Process each membership (role assignment)
          for (const membership of membershipData.data) {
            if (membership.role?.slug && membership.status === 'active') {
              try {
                console.log(`Syncing role ${membership.role.slug} for ${workosUser.email}`);
                
                const syncResult = await ctx.runMutation(api.fixUserRole.syncRoleFromWorkOS, {
                  userEmail: workosUser.email,
                  workosRoleSlug: membership.role.slug,
                  organizationMembershipId: membership.id
                });
                
                if (syncResult.success) {
                  results.rolesAssigned++;
                  userDetail.rolesAssigned++;
                  console.log(`✅ Successfully synced role ${membership.role.slug} for ${workosUser.email}`);
                } else {
                  console.log(`⚠️ Role already exists: ${membership.role.slug} for ${workosUser.email}`);
                }
                
              } catch (roleError) {
                console.error(`❌ Error syncing role ${membership.role.slug} for ${workosUser.email}:`, roleError);
                results.errors.push({
                  user: workosUser.email,
                  role: membership.role.slug,
                  error: roleError.message
                });
              }
            }
          }
          
          results.details.push(userDetail);
          
        } catch (userError) {
          console.error(`❌ Error processing user ${workosUser.email}:`, userError);
          results.errors.push({
            user: workosUser.email,
            error: userError.message
          });
          
          results.details.push({
            email: workosUser.email,
            workosId: workosUser.id,
            status: 'error',
            error: userError.message
          });
        }
      }
      
      console.log("=== Sync Summary ===");
      console.log(`Users processed: ${results.usersProcessed}`);
      console.log(`Roles assigned: ${results.rolesAssigned}`);
      console.log(`Errors: ${results.errors.length}`);
      
      return results;
      
    } catch (error) {
      console.error("❌ Fatal error in sync process:", error);
      results.errors.push({
        fatal: true,
        error: error.message
      });
      return results;
    }
  },
});