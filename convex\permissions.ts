import { QueryCtx, MutationCtx } from "./_generated/server";
import { Id } from "./_generated/dataModel";

// Permission constants
export const PERMISSIONS = {
  // Request permissions
  REQUEST_CREATE: "request:create",
  REQUEST_VIEW_OWN: "request:view:own",
  REQUEST_VIEW_SHOP: "request:view:shop",
  REQUEST_VIEW_AREA: "request:view:area",
  REQUEST_VIEW_ALL: "request:view:all",
  REQUEST_APPROVE_MOBILE: "request:approve:mobile",
  REQUEST_APPROVE_ALL: "request:approve:all",
  REQUEST_REJECT: "request:reject",
  REQUEST_RESUBMIT: "request:resubmit",

  // User management permissions
  USER_VIEW: "user:view",
  USER_CREATE: "user:create",
  USER_UPDATE: "user:update",
  USER_DELETE: "user:delete",
  USER_INVITE: "user:invite",
  USER_ASSIGN_ROLE: "user:assign:role",
  USER_ASSIGN_AREA: "user:assign:area",

  // Shop management permissions
  SHOP_VIEW: "shop:view",
  SHOP_CREATE: "shop:create",
  SHOP_UPDATE: "shop:update",
  SHOP_DELETE: "shop:delete",
  SHOP_ASSIGN_MANAGER: "shop:assign:manager",

  // Area management permissions
  AREA_VIEW: "area:view",
  AREA_CREATE: "area:create",
  AREA_UPDATE: "area:update",
  AREA_DELETE: "area:delete",

  // Settings permissions
  SETTINGS_VIEW: "settings:view",
  SETTINGS_UPDATE: "settings:update",

  // Reports permissions
  REPORTS_VIEW_OWN: "reports:view:own",
  REPORTS_VIEW_SHOP: "reports:view:shop",
  REPORTS_VIEW_AREA: "reports:view:area",
  REPORTS_VIEW_ALL: "reports:view:all",
  REPORTS_EXPORT: "reports:export",
} as const;

// Role definitions
export const ROLES = {
  SHOP_MANAGER: "shop_manager",
  SHOP_SUPPORT: "shop_support",
  ACCOUNTS: "accounts",
  WATCHER: "watcher",
} as const;

// Get current user with permissions (for WorkOS AuthKit integration)
export async function getCurrentUserWithPermissions(
  ctx: QueryCtx | MutationCtx,
  workosUserId?: string
) {
  let user = null;
  
  if (workosUserId) {
    // Direct lookup by WorkOS ID (for AuthKit integration)
    user = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", workosUserId))
      .unique();
  } else {
    // Try Convex Auth first (fallback for server-side auth)
    try {
      const identity = await ctx.auth.getUserIdentity();
      if (identity) {
        user = await ctx.db
          .query("users")
          .withIndex("by_workos_id", (q) => q.eq("workosId", identity.subject))
          .unique();
      }
    } catch (error) {
      // Convex Auth not available, this is expected with WorkOS AuthKit
      console.log("Convex Auth not available, using WorkOS AuthKit mode");
      return null; // Return null to indicate no authentication
    }
  }

  if (!user) {
    return null;
  }

  // Get user roles
  const userRoles = await ctx.db
    .query("user_roles")
    .withIndex("by_user_active", (q) => 
      q.eq("userId", user._id).eq("isActive", true)
    )
    .collect();

  const roles = await Promise.all(
    userRoles.map(async (userRole) => {
      const role = await ctx.db.get(userRole.roleId);
      return role;
    })
  );

  // Aggregate permissions
  const permissions = new Set<string>();
  roles.filter(Boolean).forEach((role) => {
    role!.permissions.forEach((permission) => permissions.add(permission));
  });

  // Get user areas
  const userAreas = await ctx.db
    .query("user_area_assignments")
    .withIndex("by_user_active", (q) => 
      q.eq("userId", user._id).eq("isActive", true)
    )
    .collect();

  const areas = await Promise.all(
    userAreas.map(async (userArea) => {
      const area = await ctx.db.get(userArea.areaId);
      return area;
    })
  );

  return {
    user,
    roles: roles.filter(Boolean),
    areas: areas.filter(Boolean),
    permissions: Array.from(permissions),
    areaIds: areas.filter(Boolean).map((area) => area!._id),
  };
}

// Check if user has permission
export async function hasPermission(
  ctx: QueryCtx | MutationCtx,
  permission: string
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  return userWithPermissions.permissions.includes(permission);
}

// Check if user has any of the given permissions
export async function hasAnyPermission(
  ctx: QueryCtx | MutationCtx,
  permissions: string[]
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  return permissions.some((permission) =>
    userWithPermissions.permissions.includes(permission)
  );
}

// Check if user has all of the given permissions
export async function hasAllPermissions(
  ctx: QueryCtx | MutationCtx,
  permissions: string[]
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  return permissions.every((permission) =>
    userWithPermissions.permissions.includes(permission)
  );
}

// Check if user has role
export async function hasRole(
  ctx: QueryCtx | MutationCtx,
  roleName: string
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  return userWithPermissions.roles.some((role) => role.name === roleName);
}

// Check if user can access area
export async function canAccessArea(
  ctx: QueryCtx | MutationCtx,
  areaId: Id<"areas">
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  // Accounts role can access all areas
  if (userWithPermissions.roles.some((role) => role.name === ROLES.ACCOUNTS)) {
    return true;
  }

  // Check if user is assigned to this area
  return userWithPermissions.areaIds.includes(areaId);
}

// Check if user can access shop
export async function canAccessShop(
  ctx: QueryCtx | MutationCtx,
  shopId: Id<"shops">
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  // Accounts role can access all shops
  if (userWithPermissions.roles.some((role) => role.name === ROLES.ACCOUNTS)) {
    return true;
  }

  // Get shop to check area
  const shop = await ctx.db.get(shopId);
  if (!shop) {
    return false;
  }

  // Check if user can access the shop's area
  return canAccessArea(ctx, shop.areaId);
}

// Check if user can manage shop (is shop manager)
export async function canManageShop(
  ctx: QueryCtx | MutationCtx,
  shopId: Id<"shops">
): Promise<boolean> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return false;
  }

  // Accounts role can manage all shops
  if (userWithPermissions.roles.some((role) => role.name === ROLES.ACCOUNTS)) {
    return true;
  }

  // Get shop to check manager
  const shop = await ctx.db.get(shopId);
  if (!shop) {
    return false;
  }

  // Check if user is the shop manager
  return shop.managerId === userWithPermissions.user._id;
}

// Require authentication
export async function requireAuth(ctx: QueryCtx | MutationCtx) {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    throw new Error("Authentication required");
  }
  return userWithPermissions;
}

// Require permission
export async function requirePermission(
  ctx: QueryCtx | MutationCtx,
  permission: string
) {
  const userWithPermissions = await requireAuth(ctx);
  if (!userWithPermissions.permissions.includes(permission)) {
    throw new Error(`Permission required: ${permission}`);
  }
  return userWithPermissions;
}

// Require permission with WorkOS user ID (for AuthKit integration)
export async function requirePermissionWithWorkOSUser(
  ctx: QueryCtx | MutationCtx,
  permission: string,
  workosUserId?: string
) {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx, workosUserId);
  if (!userWithPermissions) {
    throw new Error("Authentication required");
  }
  if (!userWithPermissions.permissions.includes(permission)) {
    throw new Error(`Permission required: ${permission}`);
  }
  return userWithPermissions;
}

// Require any permission with WorkOS user ID (for AuthKit integration)
export async function requireAnyPermissionWithWorkOSUser(
  ctx: QueryCtx | MutationCtx,
  permissions: string[],
  workosUserId?: string
) {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx, workosUserId);
  if (!userWithPermissions) {
    throw new Error("Authentication required");
  }
  const hasPermission = permissions.some((permission) =>
    userWithPermissions.permissions.includes(permission)
  );
  if (!hasPermission) {
    throw new Error(`One of these permissions required: ${permissions.join(", ")}`);
  }
  return userWithPermissions;
}

// Require any permission
export async function requireAnyPermission(
  ctx: QueryCtx | MutationCtx,
  permissions: string[]
) {
  const userWithPermissions = await requireAuth(ctx);
  const hasPermission = permissions.some((permission) =>
    userWithPermissions.permissions.includes(permission)
  );
  if (!hasPermission) {
    throw new Error(`One of these permissions required: ${permissions.join(", ")}`);
  }
  return userWithPermissions;
}

// Require role
export async function requireRole(
  ctx: QueryCtx | MutationCtx,
  roleName: string
) {
  const userWithPermissions = await requireAuth(ctx);
  const hasRequiredRole = userWithPermissions.roles.some(
    (role) => role.name === roleName
  );
  if (!hasRequiredRole) {
    throw new Error(`Role required: ${roleName}`);
  }
  return userWithPermissions;
}

// Get accessible area IDs for user
export async function getAccessibleAreaIds(
  ctx: QueryCtx | MutationCtx
): Promise<Id<"areas">[]> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return [];
  }

  // Accounts role can access all areas
  if (userWithPermissions.roles.some((role) => role.name === ROLES.ACCOUNTS)) {
    const allAreas = await ctx.db.query("areas").collect();
    return allAreas.map((area) => area._id);
  }

  return userWithPermissions.areaIds;
}

// Get accessible shop IDs for user
export async function getAccessibleShopIds(
  ctx: QueryCtx | MutationCtx
): Promise<Id<"shops">[]> {
  const userWithPermissions = await getCurrentUserWithPermissions(ctx);
  if (!userWithPermissions) {
    return [];
  }

  // Accounts role can access all shops
  if (userWithPermissions.roles.some((role) => role.name === ROLES.ACCOUNTS)) {
    const allShops = await ctx.db.query("shops").collect();
    return allShops.map((shop) => shop._id);
  }

  // Get shops in accessible areas
  const accessibleAreaIds = userWithPermissions.areaIds;
  const shops = await ctx.db.query("shops").collect();
  
  return shops
    .filter((shop) => accessibleAreaIds.includes(shop.areaId))
    .map((shop) => shop._id);
}
