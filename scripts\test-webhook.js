// Script to test our WorkOS webhook endpoint
const fetch = require('node-fetch');
const crypto = require('crypto');

const WEBHOOK_URL = 'https://efficient-toucan-547.convex.cloud/workos-webhook';
const WEBHOOK_SECRET = 'test_secret_for_development'; // This should match your Convex env var

// Create a test webhook payload
const createTestPayload = (eventType = 'user.created') => {
  const timestamp = Date.now();
  const payload = {
    event: eventType,
    data: {
      id: 'user_test_123',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      email_verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    created_at: new Date().toISOString()
  };
  
  const payloadString = JSON.stringify(payload);
  
  // Create signature according to WorkOS format
  const signatureString = timestamp + '.' + payloadString;
  const signature = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(signatureString, 'utf8')
    .digest('hex');
  
  const workosSignature = `t=${timestamp},v1=${signature}`;
  
  return {
    payload: payloadString,
    signature: workosSignature,
    timestamp
  };
};

async function testWebhook() {
  try {
    console.log('Testing WorkOS webhook endpoint...');
    console.log('Webhook URL:', WEBHOOK_URL);
    
    const testData = createTestPayload('user.created');
    
    console.log('Sending test payload...');
    console.log('Signature:', testData.signature);
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'WorkOS-Signature': testData.signature,
        'User-Agent': 'WorkOS-Webhook/1.0'
      },
      body: testData.payload
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (response.ok) {
      console.log('✅ Webhook test successful!');
    } else {
      console.log('❌ Webhook test failed');
    }
    
  } catch (error) {
    console.error('Error testing webhook:', error);
  }
}

// Test different event types
async function testMultipleEvents() {
  const events = ['user.created', 'user.updated', 'user.deleted'];
  
  for (const eventType of events) {
    console.log(`\n--- Testing ${eventType} ---`);
    const testData = createTestPayload(eventType);
    
    try {
      const response = await fetch(WEBHOOK_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'WorkOS-Signature': testData.signature,
          'User-Agent': 'WorkOS-Webhook/1.0'
        },
        body: testData.payload
      });
      
      console.log(`${eventType}: ${response.status} ${response.statusText}`);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.log('Error:', errorText);
      }
      
    } catch (error) {
      console.error(`Error testing ${eventType}:`, error.message);
    }
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Run the tests
console.log('Starting webhook tests...');
testWebhook().then(() => {
  console.log('\nTesting multiple event types...');
  return testMultipleEvents();
}).then(() => {
  console.log('\nAll tests completed!');
}).catch(error => {
  console.error('Test suite failed:', error);
});