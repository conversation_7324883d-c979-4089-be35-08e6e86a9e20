
import React, { useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import PageTransition from '@/components/common/PageTransition';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { toast } from '@/components/ui/use-toast';
import { X, Loader2, RefreshCw, Link as LinkIcon, Settings as SettingsIcon, Mail } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { useAuth } from '@/lib/auth-context';
import { SMTPSettings } from '@/components/settings/SMTPSettings';
import { SettingsTest } from '@/components/debug/SettingsTest';
import { InitializeSettings } from '@/components/debug/InitializeSettings';

const Settings: React.FC = () => {
  useRequireAuth(['accounts']); // Correct - only admins can access
  const { user: workosUser } = useAuth();
  
  // Use Convex queries and mutations
  const settings = useQuery(
    api.settings.getSettings,
    workosUser?.id ? { workosUserId: workosUser.id } : "skip"
  );
  const updateSettingsMutation = useMutation(api.settings.updateSettings);
  
  const settingsLoading = !settings;
  const error = null; // Convex handles errors automatically

  // Use local loading state for save operations
  const [isSaving, setIsSaving] = React.useState(false);

  // Form states - initialize with current threshold from store
  const [formData, setFormData] = React.useState({
    mobileMoneyThreshold: settings?.mobile_money_approval_threshold?.toString() || '',
    bankTransferThreshold: settings?.bank_transfer_approval_threshold?.toString() || '',
    whitelistedDomains: settings?.whitelisted_domains || []
  });

  // Data fetching is now handled by Convex queries automatically

  // Update form data when settings are loaded
  useEffect(() => {
    if (settings) {
      setFormData(prev => ({
        ...prev,
        mobileMoneyThreshold: settings.mobile_money_approval_threshold?.toString() || '',
        bankTransferThreshold: settings.bank_transfer_approval_threshold?.toString() || '',
        whitelistedDomains: settings.whitelisted_domains || []
      }));
    }
  }, [settings]);

  const [newDomain, setNewDomain] = React.useState('');

  const handleAddDomain = () => {
    if (!newDomain) return;

    const domain = newDomain.toLowerCase().trim();
    if (formData.whitelistedDomains.includes(domain)) {
      toast({
        title: "Domain exists",
        description: "This domain is already whitelisted",
        variant: "destructive",
      });
      return;
    }

    setFormData(prev => ({
      ...prev,
      whitelistedDomains: [...prev.whitelistedDomains, domain]
    }));
    setNewDomain('');
  };

  const handleRemoveDomain = (domain: string) => {
    if (domain === 'kmkentertainment.com' || domain === 'mybet.africa') {
      toast({
        title: "Cannot remove",
        description: "Default domains cannot be removed",
        variant: "destructive",
      });
      return;
    }

    setFormData(prev => ({
      ...prev,
      whitelistedDomains: prev.whitelistedDomains.filter(d => d !== domain)
    }));
  };

  const handleSaveAll = async () => {
    setIsSaving(true);
    try {
      // Validate Mobile Money Threshold
      const momoAmount = parseFloat(formData.mobileMoneyThreshold);
      if (isNaN(momoAmount) || momoAmount <= 0) {
        toast({
          variant: "destructive",
          title: "Invalid Mobile Money amount",
          description: "Please enter a valid amount greater than 0",
        });
        setIsSaving(false);
        return;
      }

      // Validate Bank Transfer Threshold
      const bankAmount = parseFloat(formData.bankTransferThreshold);
      if (isNaN(bankAmount) || bankAmount <= 0) {
        toast({
          variant: "destructive",
          title: "Invalid Bank Transfer amount",
          description: "Please enter a valid amount greater than 0",
        });
        setIsSaving(false);
        return;
      }

      console.log('Saving settings:', {
        mobile_money_approval_threshold: momoAmount,
        bank_transfer_approval_threshold: bankAmount,
        whitelisted_domains: formData.whitelistedDomains
      });

      // Update settings using Convex mutation
      await updateSettingsMutation({
        mobile_money_approval_threshold: momoAmount,
        bank_transfer_approval_threshold: bankAmount,
        whitelisted_domains: formData.whitelistedDomains,
        workosUserId: workosUser?.id || ""
      });

      toast({
        title: "Settings saved",
        description: "Your changes have been saved successfully to the database",
      });
    } catch (error: any) {
      console.error('Error saving settings:', error);
      toast({
        variant: "destructive",
        title: "Error",
        description: `Failed to save settings: ${error.message || 'Unknown error'}`,
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Common amount input handler
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>, field: 'mobileMoneyThreshold' | 'bankTransferThreshold') => {
    let value = e.target.value;

    // Remove leading zeros and non-numeric characters
    value = value.replace(/^0+/, '');
    if (value === '') value = '0';

    // Ensure it's a valid number
    const numValue = parseFloat(value);
    if (!isNaN(numValue)) {
      setFormData(prev => ({
        ...prev,
        [field]: numValue.toString()
      }));
    }
  };

  return (
    <Layout>
      <PageTransition>
        <div className="container max-w-6xl space-y-6 p-4 md:p-8">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-3xl font-bold tracking-tight">Settings</h2>
              <p className="text-muted-foreground">
                Manage your application settings and preferences
              </p>
            </div>
            <div className="flex items-center gap-4">
              {settingsLoading && (
                <div className="flex items-center">
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  <span>Loading settings...</span>
                </div>
              )}
              <Link to="/sync-management">
                <Button variant="outline" className="flex items-center gap-2">
                  <RefreshCw className="h-4 w-4" />
                  WorkOS Sync Management
                </Button>
              </Link>
            </div>
          </div>

          <Tabs defaultValue="general" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <SettingsIcon className="h-4 w-4" />
                General Settings
              </TabsTrigger>
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email & SMTP
              </TabsTrigger>
              <TabsTrigger value="debug" className="flex items-center gap-2">
                🔧 Debug
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              <Card className="glass-panel">
                <CardHeader>
                  <CardTitle>Approval Thresholds</CardTitle>
                  <CardDescription>
                    Configure amount limits for different payment methods
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-2">
                      <Label htmlFor="mobileMoney">Mobile Money Approval Limit (¢)</Label>
                      <Input
                        id="mobileMoney"
                        type="number"
                        min="1"
                        step="any"
                        onKeyDown={(e) => {
                          // Prevent negative numbers and 'e'
                          if (e.key === '-' || e.key === 'e') {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => handleAmountChange(e, 'mobileMoneyThreshold')}
                        value={formData.mobileMoneyThreshold}
                        disabled={isSaving || settingsLoading}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="bankTransfer">Bank Transfer Minimum (¢)</Label>
                      <Input
                        id="bankTransfer"
                        type="number"
                        min="1"
                        step="any"
                        onKeyDown={(e) => {
                          if (e.key === '-' || e.key === 'e') {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => handleAmountChange(e, 'bankTransferThreshold')}
                        value={formData.bankTransferThreshold}
                        disabled={isSaving || settingsLoading}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="glass-panel">
                <CardHeader>
                  <CardTitle>Domain Whitelist</CardTitle>
                  <CardDescription>
                    Manage allowed email domains for user registration
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-4">
                    <div className="flex gap-2">
                      <Input
                        placeholder="Enter domain (e.g., example.com)"
                        value={newDomain}
                        onChange={(e) => setNewDomain(e.target.value)}
                        disabled={isSaving || settingsLoading}
                      />
                      <Button
                        onClick={handleAddDomain}
                        disabled={isSaving || settingsLoading}
                      >
                        Add Domain
                      </Button>
                    </div>

                    <div className="space-y-2">
                      {formData.whitelistedDomains.map((domain) => (
                        <div key={domain} className="flex items-center justify-between p-2 bg-muted rounded-md">
                          <span className="font-medium">{domain}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleRemoveDomain(domain)}
                            disabled={domain === 'kmkentertainment.com' || domain === 'mybet.africa' || isSaving || settingsLoading}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <div className="flex justify-end">
                <Button
                  onClick={handleSaveAll}
                  disabled={isSaving || settingsLoading}
                  className="w-full sm:w-auto"
                >
                  {isSaving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving Changes...
                    </>
                  ) : (
                    'Save General Settings'
                  )}
                </Button>
              </div>
            </TabsContent>

            <TabsContent value="email" className="space-y-6">
              <SMTPSettings />
            </TabsContent>

            <TabsContent value="debug" className="space-y-6">
              <InitializeSettings />
              <SettingsTest />
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>
    </Layout>
  );
};

export default Settings;
