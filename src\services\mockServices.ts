/**
 * Mock Services
 *
 * This file provides mock implementations of all the service functions
 * that previously used Supabase. It uses the mock data from mockData.ts.
 */

import {
  mockUsers,
  mockAreas,
  mockShops,
  mockRequests,
  mockNotifications,
  mockSettings,
  mockStats
} from '@/lib/mockData';

import {
  User,
  Area,
  Shop,
  Request,
  RequestStatus,
  PaymentMethod,
  UserRole,
  AppNotification,
  DashboardStats
} from '@/lib/types';

// Helper function to generate a unique ID
const generateId = (prefix: string) => {
  return `${prefix}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
};

// Helper function to simulate async behavior - reduced for better performance
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

// Performance-optimized delays (much faster than before)
const FAST_DELAY = 50;   // For critical operations
const NORMAL_DELAY = 100; // For standard operations
const SLOW_DELAY = 200;   // For heavy operations

// Track current logged-in user
let currentLoggedInUser: User | null = null;

// ==================== User Services ====================

export async function getUsers(): Promise<User[]> {
  await delay(FAST_DELAY); // Optimized delay
  return [...mockUsers];
}

export async function getUserById(id: string): Promise<User | null> {
  await delay(FAST_DELAY);
  return mockUsers.find(user => user.id === id) || null;
}

export async function createUser(user: Omit<User, 'id' | 'createdAt' | 'updatedAt'>): Promise<User> {
  await delay(NORMAL_DELAY);
  const newUser: User = {
    id: generateId('user'),
    ...user,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  mockUsers.push(newUser);
  return newUser;
}

export async function updateUser(id: string, updates: Partial<User>): Promise<User> {
  await delay(NORMAL_DELAY);
  const userIndex = mockUsers.findIndex(user => user.id === id);
  if (userIndex === -1) {
    throw new Error('User not found');
  }

  const updatedUser = {
    ...mockUsers[userIndex],
    ...updates,
    updatedAt: new Date()
  };

  mockUsers[userIndex] = updatedUser;
  return updatedUser;
}

export async function deleteUser(id: string): Promise<void> {
  await delay(500);
  const userIndex = mockUsers.findIndex(user => user.id === id);
  if (userIndex === -1) {
    throw new Error('User not found');
  }

  mockUsers.splice(userIndex, 1);
}

export async function getCurrentUser(): Promise<User | null> {
  await delay(300);
  // Return the currently logged-in user
  return currentLoggedInUser;
}

export async function inviteUser(userData: {
  name: string;
  email: string;
  role: UserRole;
  shop?: string;
  areas?: string[];
}): Promise<void> {
  await delay(1000);
  // Mock implementation - just simulate success
  console.log('Mock: Inviting user', userData);
  // In a real implementation, this would send an invitation email
}

// ==================== Area Services ====================

export async function getAreas(): Promise<Area[]> {
  await delay(FAST_DELAY);
  return [...mockAreas];
}

export async function getAreaById(id: string): Promise<Area | null> {
  await delay(FAST_DELAY);
  return mockAreas.find(area => area.id === id) || null;
}

export async function createArea(area: Omit<Area, 'id' | 'createdAt' | 'updatedAt'>): Promise<Area> {
  await delay(700);
  const newArea: Area = {
    id: generateId('area'),
    ...area,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  mockAreas.push(newArea);
  return newArea;
}

export async function updateArea(id: string, updates: Partial<Area>): Promise<Area> {
  await delay(500);
  const areaIndex = mockAreas.findIndex(area => area.id === id);
  if (areaIndex === -1) {
    throw new Error('Area not found');
  }

  const updatedArea = {
    ...mockAreas[areaIndex],
    ...updates,
    updatedAt: new Date()
  };

  mockAreas[areaIndex] = updatedArea;
  return updatedArea;
}

export async function deleteArea(id: string): Promise<void> {
  await delay(500);
  const areaIndex = mockAreas.findIndex(area => area.id === id);
  if (areaIndex === -1) {
    throw new Error('Area not found');
  }

  mockAreas.splice(areaIndex, 1);
}

// ==================== Shop Services ====================

export async function getShops(): Promise<Shop[]> {
  await delay(FAST_DELAY);
  return [...mockShops];
}

export async function getShopById(id: string): Promise<Shop | null> {
  await delay(FAST_DELAY);
  return mockShops.find(shop => shop.id === id) || null;
}

export async function getShopsByArea(areaId: string): Promise<Shop[]> {
  await delay(FAST_DELAY);
  return mockShops.filter(shop => shop.areaId === areaId);
}

export async function createShop(shop: Omit<Shop, 'id' | 'createdAt' | 'updatedAt' | 'areaName' | 'managerName'>): Promise<Shop> {
  await delay(700);

  // Get area and manager names
  const area = mockAreas.find(a => a.id === shop.areaId);
  const manager = mockUsers.find(u => u.id === shop.managerId);

  if (!area) {
    throw new Error('Area not found');
  }

  if (!manager) {
    throw new Error('Manager not found');
  }

  const newShop: Shop = {
    id: generateId('shop'),
    ...shop,
    areaName: area.name,
    managerName: manager.name,
    createdAt: new Date(),
    updatedAt: new Date()
  };

  mockShops.push(newShop);
  return newShop;
}

export async function updateShop(id: string, updates: Partial<Omit<Shop, 'areaName' | 'managerName'>>): Promise<Shop> {
  await delay(500);
  const shopIndex = mockShops.findIndex(shop => shop.id === id);
  if (shopIndex === -1) {
    throw new Error('Shop not found');
  }

  // Update area and manager names if IDs are updated
  let areaName = mockShops[shopIndex].areaName;
  let managerName = mockShops[shopIndex].managerName;

  if (updates.areaId) {
    const area = mockAreas.find(a => a.id === updates.areaId);
    if (!area) {
      throw new Error('Area not found');
    }
    areaName = area.name;
  }

  if (updates.managerId) {
    const manager = mockUsers.find(u => u.id === updates.managerId);
    if (!manager) {
      throw new Error('Manager not found');
    }
    managerName = manager.name;
  }

  const updatedShop = {
    ...mockShops[shopIndex],
    ...updates,
    areaName,
    managerName,
    updatedAt: new Date()
  };

  mockShops[shopIndex] = updatedShop;
  return updatedShop;
}

export async function deleteShop(id: string): Promise<void> {
  await delay(500);
  const shopIndex = mockShops.findIndex(shop => shop.id === id);
  if (shopIndex === -1) {
    throw new Error('Shop not found');
  }

  mockShops.splice(shopIndex, 1);
}

// ==================== Request Services ====================

export async function getRequests(): Promise<Request[]> {
  await delay(FAST_DELAY); // Critical for dashboard performance

  // Get current user to filter requests based on role
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    return [];
  }

  // Filter requests based on user role
  if (currentUser.role === 'shop_manager') {
    // Shop managers only see their own requests
    return mockRequests.filter(request => request.createdBy === currentUser.id);
  }

  // Admin roles (accounts, shop_support, watcher) see all requests
  return [...mockRequests];
}

export async function getRequestById(id: string): Promise<Request | null> {
  await delay(FAST_DELAY);
  return mockRequests.find(req => req.id === id) || null;
}

export async function getRequestsByShop(shopId: string): Promise<Request[]> {
  await delay(FAST_DELAY);
  return mockRequests.filter(req => req.shopId === shopId);
}

export async function getRequestsByStatus(status: RequestStatus): Promise<Request[]> {
  await delay(FAST_DELAY);
  return mockRequests.filter(req => req.status === status);
}

export async function createRequest(request: Omit<Request, 'id' | 'createdAt' | 'updatedAt' | 'shopName' | 'areaName' | 'createdByName' | 'approvedByName' | 'rejectedByName'>): Promise<Request> {
  await delay(800);

  // Get related entity names
  const shop = mockShops.find(s => s.id === request.shopId);
  const area = mockAreas.find(a => a.id === request.areaId);
  const creator = mockUsers.find(u => u.id === request.createdBy);

  if (!shop) {
    throw new Error('Shop not found');
  }

  if (!area) {
    throw new Error('Area not found');
  }

  if (!creator) {
    throw new Error('Creator not found');
  }

  const newRequest: Request = {
    id: generateId('req'),
    ...request,
    shopName: shop.name,
    areaName: area.name,
    createdByName: creator.name,
    approvedByName: null,
    rejectedByName: null,
    createdAt: new Date(),
    updatedAt: new Date(),
    approvedAt: null,
    rejectedAt: null,
    attachments: request.attachments || []
  };

  mockRequests.push(newRequest);
  return newRequest;
}

export async function updateRequest(id: string, updates: Partial<Request>): Promise<Request> {
  await delay(500);
  const requestIndex = mockRequests.findIndex(req => req.id === id);
  if (requestIndex === -1) {
    throw new Error('Request not found');
  }

  const updatedRequest = {
    ...mockRequests[requestIndex],
    ...updates,
    updatedAt: new Date()
  };

  mockRequests[requestIndex] = updatedRequest;
  return updatedRequest;
}

export async function approveRequest(id: string, approvalReason: string): Promise<Request> {
  await delay(500);
  const requestIndex = mockRequests.findIndex(req => req.id === id);
  if (requestIndex === -1) {
    throw new Error('Request not found');
  }

  const approver = mockUsers[0]; // Use first user as approver
  const originalRequest = mockRequests[requestIndex];

  // Create new admin action for approval
  const approvalAction = {
    id: generateId('action'),
    type: 'approve' as const,
    timestamp: new Date(),
    adminId: approver.id,
    adminName: approver.name,
    adminRole: approver.role,
    comment: approvalReason,
    previousStatus: originalRequest.status,
    newStatus: 'approved' as RequestStatus
  };

  const updatedRequest = {
    ...originalRequest,
    status: 'approved' as RequestStatus,
    approvedBy: approver.id,
    approvedByName: approver.name,
    approvedAt: new Date(),
    approvalReason,
    updatedAt: new Date(),
    adminActionHistory: [
      ...(originalRequest.adminActionHistory || []),
      approvalAction
    ]
  };

  mockRequests[requestIndex] = updatedRequest;
  return updatedRequest;
}

export async function rejectRequest(id: string, rejectionReason: string): Promise<Request> {
  await delay(500);
  const requestIndex = mockRequests.findIndex(req => req.id === id);
  if (requestIndex === -1) {
    throw new Error('Request not found');
  }

  const rejector = mockUsers[0]; // Use first user as rejector
  const originalRequest = mockRequests[requestIndex];

  // Create new admin action for rejection
  const rejectionAction = {
    id: generateId('action'),
    type: 'reject' as const,
    timestamp: new Date(),
    adminId: rejector.id,
    adminName: rejector.name,
    adminRole: rejector.role,
    comment: rejectionReason,
    previousStatus: originalRequest.status,
    newStatus: 'rejected' as RequestStatus
  };

  const updatedRequest = {
    ...originalRequest,
    status: 'rejected' as RequestStatus,
    rejectedBy: rejector.id,
    rejectedByName: rejector.name,
    rejectedAt: new Date(),
    rejectionReason,
    updatedAt: new Date(),
    adminActionHistory: [
      ...(originalRequest.adminActionHistory || []),
      rejectionAction
    ]
  };

  mockRequests[requestIndex] = updatedRequest;
  return updatedRequest;
}

export async function resubmitRequest(id: string, updates: Partial<Request>, notes: string): Promise<Request> {
  await delay(500);
  const requestIndex = mockRequests.findIndex(req => req.id === id);
  if (requestIndex === -1) {
    throw new Error('Request not found');
  }

  const originalRequest = mockRequests[requestIndex];

  // Create admin action for resubmission acknowledgment
  const resubmissionAction = {
    id: generateId('action'),
    type: 'comment' as const,
    timestamp: new Date(),
    adminId: 'system',
    adminName: 'System',
    adminRole: 'accounts' as const,
    comment: `Request resubmitted by ${originalRequest.createdByName}. Changes: ${notes}`,
    previousStatus: originalRequest.status,
    newStatus: 'resubmitted' as RequestStatus
  };

  const updatedRequest = {
    ...originalRequest,
    ...updates,
    status: 'resubmitted' as RequestStatus,
    updatedAt: new Date(),
    adminActionHistory: [
      ...(originalRequest.adminActionHistory || []),
      resubmissionAction
    ],
    resubmissionHistory: [
      ...(originalRequest.resubmissionHistory || []),
      {
        timestamp: new Date(),
        changes: {},
        notes,
        previousRejectionReason: originalRequest.rejectionReason || '',
        adminComments: [resubmissionAction]
      }
    ]
  };

  mockRequests[requestIndex] = updatedRequest;
  return updatedRequest;
}

export async function addAdminComment(id: string, comment: string, adminId: string = 'user-1'): Promise<Request> {
  await delay(300);
  const requestIndex = mockRequests.findIndex(req => req.id === id);
  if (requestIndex === -1) {
    throw new Error('Request not found');
  }

  const admin = mockUsers.find(u => u.id === adminId) || mockUsers[0];
  const originalRequest = mockRequests[requestIndex];

  // Create new admin comment action
  const commentAction = {
    id: generateId('action'),
    type: 'comment' as const,
    timestamp: new Date(),
    adminId: admin.id,
    adminName: admin.name,
    adminRole: admin.role,
    comment: comment,
    previousStatus: originalRequest.status,
    newStatus: originalRequest.status
  };

  const updatedRequest = {
    ...originalRequest,
    updatedAt: new Date(),
    adminActionHistory: [
      ...(originalRequest.adminActionHistory || []),
      commentAction
    ]
  };

  mockRequests[requestIndex] = updatedRequest;
  return updatedRequest;
}

export async function deleteRequest(id: string): Promise<void> {
  await delay(500);
  const requestIndex = mockRequests.findIndex(req => req.id === id);
  if (requestIndex === -1) {
    throw new Error('Request not found');
  }

  mockRequests.splice(requestIndex, 1);
}

// ==================== Stats Services ====================

export async function getStats(): Promise<DashboardStats> {
  await delay(FAST_DELAY); // Critical for dashboard performance

  // Get current user to filter stats based on role
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    return calculateStatsFromRequests([]);
  }

  // Filter requests based on user role for stats calculation
  let filteredRequests = mockRequests;
  if (currentUser.role === 'shop_manager') {
    // Shop managers only see stats from their own requests
    filteredRequests = mockRequests.filter(request => request.createdBy === currentUser.id);
  }

  return calculateStatsFromRequests(filteredRequests);
}

export async function getStatsByDateRange(startDate: Date, endDate: Date): Promise<DashboardStats> {
  await delay(NORMAL_DELAY);

  // Get current user to filter stats based on role
  const currentUser = await getCurrentUser();

  if (!currentUser) {
    return calculateStatsFromRequests([]);
  }

  // Filter requests by user role first, then by date range
  let userFilteredRequests = mockRequests;
  if (currentUser.role === 'shop_manager') {
    // Shop managers only see stats from their own requests
    userFilteredRequests = mockRequests.filter(request => request.createdBy === currentUser.id);
  }

  // Then filter by date range
  const filteredRequests = userFilteredRequests.filter(request => {
    const requestDate = new Date(request.createdAt);
    return requestDate >= startDate && requestDate <= endDate;
  });

  return calculateStatsFromRequests(filteredRequests);
}

// Helper function to calculate stats from request data
function calculateStatsFromRequests(requests: Request[]): DashboardStats {
  const totalRequests = requests.length;
  // Count both 'pending' and 'resubmitted' as pending to match dashboard logic
  const pendingRequests = requests.filter(r => r.status === 'pending' || r.status === 'resubmitted').length;
  const approvedRequests = requests.filter(r => r.status === 'approved').length;
  const rejectedRequests = requests.filter(r => r.status === 'rejected').length;
  const resubmittedRequests = requests.filter(r => r.status === 'resubmitted').length;

  // Calculate total amount from APPROVED requests only
  const totalAmount = requests
    .filter(r => r.status === 'approved')
    .reduce((sum, r) => sum + r.amount, 0);

  // Calculate payment method amounts from APPROVED requests only
  const mobileMoneyAmount = requests
    .filter(r => r.status === 'approved' && r.paymentMethod === 'mobile_money')
    .reduce((sum, r) => sum + r.amount, 0);

  const bankTransferAmount = requests
    .filter(r => r.status === 'approved' && r.paymentMethod === 'bank_transfer')
    .reduce((sum, r) => sum + r.amount, 0);

  // Calculate unique areas and shops
  const uniqueAreas = new Set(requests.map(r => r.areaId)).size;
  const uniqueShops = new Set(requests.map(r => r.shopId)).size;

  // Calculate monthly stats for the current year
  const currentYear = new Date().getFullYear();
  const monthlyStats = Array.from({ length: 12 }, (_, index) => {
    const month = index + 1;
    const monthRequests = requests.filter(r => {
      const requestDate = new Date(r.createdAt);
      return requestDate.getFullYear() === currentYear &&
             requestDate.getMonth() + 1 === month &&
             r.status === 'approved'; // Only count approved requests for monthly totals
    });

    return {
      month,
      total: monthRequests.reduce((sum, r) => sum + r.amount, 0),
      count: monthRequests.length
    };
  });

  return {
    totalAmount,
    totalRequests,
    pendingRequests,
    approvedRequests,
    rejectedRequests,
    resubmittedRequests,
    mobileMoneyAmount,
    bankTransferAmount,
    uniqueAreas,
    uniqueShops,
    monthlyStats
  };
}

// ==================== Settings Services ====================

export async function getSettings() {
  await delay(300);
  return { ...mockSettings };
}

export async function updateSettings(updates: any) {
  await delay(500);
  Object.assign(mockSettings, updates);
  mockSettings.updatedAt = new Date();
  return { ...mockSettings };
}

// ==================== Notification Services ====================

export async function getNotifications(): Promise<AppNotification[]> {
  await delay(500);
  // For mock purposes, return all notifications
  return [...mockNotifications];
}

export async function markNotificationAsRead(id: string): Promise<void> {
  await delay(300);
  const notification = mockNotifications.find(n => n.id === id);
  if (notification) {
    notification.isRead = true;
    notification.updatedAt = new Date();
  }
}

// ==================== Auth Services ====================

export async function login(email: string, password: string): Promise<User> {
  await delay(1000);
  // For mock purposes, accept any email that looks valid and any password
  if (!email.includes('@') || !password) {
    throw new Error('Invalid email or password');
  }

  // Try to find the user by email, or return the first user as fallback
  const user = mockUsers.find(u => u.email === email) || mockUsers[0];



  // Set as current logged-in user
  currentLoggedInUser = user;

  return user;
}

export async function logout(): Promise<void> {
  await delay(500);
  // Clear the current logged-in user
  currentLoggedInUser = null;
}

export async function refreshJwtClaims(): Promise<void> {
  await delay(300);
  // Nothing to do in mock implementation
}

export async function checkPendingInvites(): Promise<void> {
  await delay(300);
  // Nothing to do in mock implementation
}
