/* eslint-disable */
/**
 * Generated `api` utility.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import type {
  ApiFromModules,
  FilterApi,
  FunctionReference,
} from "convex/server";
import type * as adminSetup from "../adminSetup.js";
import type * as areas from "../areas.js";
import type * as auth from "../auth.js";
import type * as debugAuth from "../debugAuth.js";
import type * as debugQueries from "../debugQueries.js";
import type * as debugWorkosApi from "../debugWorkosApi.js";
import type * as directWebhook from "../directWebhook.js";
import type * as emailNotificationService from "../emailNotificationService.js";
import type * as emailTemplates from "../emailTemplates.js";
import type * as files from "../files.js";
import type * as fixShopAssignment from "../fixShopAssignment.js";
import type * as fixUserName from "../fixUserName.js";
import type * as fixUserRole from "../fixUserRole.js";
import type * as health from "../health.js";
import type * as http from "../http.js";
import type * as init from "../init.js";
import type * as migrateStatusHistory from "../migrateStatusHistory.js";
import type * as notifications from "../notifications.js";
import type * as permissions from "../permissions.js";
import type * as publicQueries from "../publicQueries.js";
import type * as quickFix from "../quickFix.js";
import type * as realSMTPService from "../realSMTPService.js";
import type * as requestStatusHistory from "../requestStatusHistory.js";
import type * as requests from "../requests.js";
import type * as settings from "../settings.js";
import type * as shops from "../shops.js";
import type * as simpleInit from "../simpleInit.js";
import type * as simpleWebhook from "../simpleWebhook.js";
import type * as syncAllUsersFromWorkOS from "../syncAllUsersFromWorkOS.js";
import type * as syncTest from "../syncTest.js";
import type * as testAuthWithWorkOS from "../testAuthWithWorkOS.js";
import type * as testHttp from "../testHttp.js";
import type * as testQueries from "../testQueries.js";
import type * as testWorkosApi from "../testWorkosApi.js";
import type * as userQueries from "../userQueries.js";
import type * as users from "../users.js";
import type * as webhookVerification from "../webhookVerification.js";
import type * as webhooks from "../webhooks.js";
import type * as workosApi from "../workosApi.js";
import type * as workosSync from "../workosSync.js";

/**
 * A utility for referencing Convex functions in your app's API.
 *
 * Usage:
 * ```js
 * const myFunctionReference = api.myModule.myFunction;
 * ```
 */
declare const fullApi: ApiFromModules<{
  adminSetup: typeof adminSetup;
  areas: typeof areas;
  auth: typeof auth;
  debugAuth: typeof debugAuth;
  debugQueries: typeof debugQueries;
  debugWorkosApi: typeof debugWorkosApi;
  directWebhook: typeof directWebhook;
  emailNotificationService: typeof emailNotificationService;
  emailTemplates: typeof emailTemplates;
  files: typeof files;
  fixShopAssignment: typeof fixShopAssignment;
  fixUserName: typeof fixUserName;
  fixUserRole: typeof fixUserRole;
  health: typeof health;
  http: typeof http;
  init: typeof init;
  migrateStatusHistory: typeof migrateStatusHistory;
  notifications: typeof notifications;
  permissions: typeof permissions;
  publicQueries: typeof publicQueries;
  quickFix: typeof quickFix;
  realSMTPService: typeof realSMTPService;
  requestStatusHistory: typeof requestStatusHistory;
  requests: typeof requests;
  settings: typeof settings;
  shops: typeof shops;
  simpleInit: typeof simpleInit;
  simpleWebhook: typeof simpleWebhook;
  syncAllUsersFromWorkOS: typeof syncAllUsersFromWorkOS;
  syncTest: typeof syncTest;
  testAuthWithWorkOS: typeof testAuthWithWorkOS;
  testHttp: typeof testHttp;
  testQueries: typeof testQueries;
  testWorkosApi: typeof testWorkosApi;
  userQueries: typeof userQueries;
  users: typeof users;
  webhookVerification: typeof webhookVerification;
  webhooks: typeof webhooks;
  workosApi: typeof workosApi;
  workosSync: typeof workosSync;
}>;
export declare const api: FilterApi<
  typeof fullApi,
  FunctionReference<any, "public">
>;
export declare const internal: FilterApi<
  typeof fullApi,
  FunctionReference<any, "internal">
>;
