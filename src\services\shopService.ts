import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { useQuery, useMutation } from 'convex/react';
import { convex } from '@/lib/convex';
import { Shop } from '@/lib/types';

/**
 * Fetch all shops
 */
export async function getShops(): Promise<Shop[]> {
  try {
    const shops = await convex.query(api.shops.getShops, {});
    return shops.map(mapConvexShopToShop);
  } catch (error) {
    console.error('Error fetching shops:', error);
    throw error;
  }
}

/**
 * Fetch shops by area
 */
export async function getShopsByArea(areaId: string): Promise<Shop[]> {
  try {
    const shops = await convex.query(api.shops.getShops, { 
      areaId: areaId as Id<"areas"> 
    });
    return shops.map(mapConvexShopToShop);
  } catch (error) {
    console.error('Error fetching shops by area:', error);
    throw error;
  }
}

/**
 * Fetch a shop by ID
 */
export async function getShopById(id: string): Promise<Shop | null> {
  try {
    const shop = await convex.query(api.shops.getShop, { 
      shopId: id as Id<"shops"> 
    });
    
    if (!shop) {
      return null;
    }
    
    return mapConvexShopToShop(shop);
  } catch (error) {
    console.error('Error fetching shop by ID:', error);
    throw error;
  }
}

/**
 * Create a new shop
 */
export async function createShop(shopData: {
  name: string;
  location: string;
  areaId?: string;
  managerId?: string;
}): Promise<Shop> {
  try {
    const shopId = await convex.mutation(api.shops.createShop, {
      name: shopData.name,
      code: `SHOP-${Date.now()}`, // Generate a unique code
      areaId: shopData.areaId as Id<"areas">,
      managerId: shopData.managerId as Id<"users"> | undefined,
      address: shopData.location,
    });

    const createdShop = await convex.query(api.shops.getShop, { 
      shopId 
    });
    
    if (!createdShop) {
      throw new Error('Failed to retrieve created shop');
    }
    
    return mapConvexShopToShop(createdShop);
  } catch (error) {
    console.error('Error creating shop:', error);
    throw error;
  }
}

/**
 * Update a shop
 */
export async function updateShop(id: string, updates: Partial<{
  name: string;
  location: string;
  areaId: string;
  managerId: string;
}>): Promise<Shop> {
  try {
    await convex.mutation(api.shops.updateShop, {
      shopId: id as Id<"shops">,
      name: updates.name,
      areaId: updates.areaId as Id<"areas"> | undefined,
      managerId: updates.managerId as Id<"users"> | undefined,
      address: updates.location,
    });

    const updatedShop = await convex.query(api.shops.getShop, { 
      shopId: id as Id<"shops"> 
    });
    
    if (!updatedShop) {
      throw new Error('Failed to retrieve updated shop');
    }
    
    return mapConvexShopToShop(updatedShop);
  } catch (error) {
    console.error('Error updating shop:', error);
    throw error;
  }
}

/**
 * Delete a shop
 */
export async function deleteShop(id: string): Promise<void> {
  try {
    await convex.mutation(api.shops.deleteShop, {
      shopId: id as Id<"shops">,
    });
  } catch (error) {
    console.error('Error deleting shop:', error);
    throw error;
  }
}

/**
 * Assign manager to shop
 */
export async function assignManager(shopId: string, managerId: string): Promise<Shop> {
  try {
    await convex.mutation(api.shops.assignManager, {
      shopId: shopId as Id<"shops">,
      managerId: managerId as Id<"users">,
    });

    const updatedShop = await convex.query(api.shops.getShop, { 
      shopId: shopId as Id<"shops"> 
    });
    
    if (!updatedShop) {
      throw new Error('Failed to retrieve updated shop');
    }
    
    return mapConvexShopToShop(updatedShop);
  } catch (error) {
    console.error('Error assigning manager:', error);
    throw error;
  }
}

/**
 * Remove manager from shop
 */
export async function removeManager(shopId: string): Promise<Shop> {
  try {
    await convex.mutation(api.shops.removeManager, {
      shopId: shopId as Id<"shops">,
    });

    const updatedShop = await convex.query(api.shops.getShop, { 
      shopId: shopId as Id<"shops"> 
    });
    
    if (!updatedShop) {
      throw new Error('Failed to retrieve updated shop');
    }
    
    return mapConvexShopToShop(updatedShop);
  } catch (error) {
    console.error('Error removing manager:', error);
    throw error;
  }
}

/**
 * Get shops by manager
 */
export async function getShopsByManager(managerId: string): Promise<Shop[]> {
  try {
    const shops = await convex.query(api.shops.getShopsByManager, { 
      managerId: managerId as Id<"users"> 
    });
    return shops.map(mapConvexShopToShop);
  } catch (error) {
    console.error('Error fetching shops by manager:', error);
    throw error;
  }
}

/**
 * Get shop statistics
 */
export async function getShopStats(filters?: {
  areaId?: string;
  startDate?: Date;
  endDate?: Date;
}): Promise<any> {
  try {
    return await convex.query(api.shops.getShopStats, {
      areaId: filters?.areaId as Id<"areas"> | undefined,
      startDate: filters?.startDate?.getTime(),
      endDate: filters?.endDate?.getTime(),
    });
  } catch (error) {
    console.error('Error fetching shop stats:', error);
    throw error;
  }
}

/**
 * Helper function to map a Convex shop to a Shop object
 */
function mapConvexShopToShop(convexShop: any): Shop {
  return {
    id: convexShop._id,
    name: convexShop.name,
    location: convexShop.address || '',
    areaId: convexShop.areaId,
    managerId: convexShop.managerId || '',
  };
}

// React hooks for components
export const useShops = (filters?: any) => {
  return useQuery(api.shops.getShops, filters || {});
};

export const useShop = (shopId: string) => {
  return useQuery(api.shops.getShop, { 
    shopId: shopId as Id<"shops"> 
  });
};

export const useShopsByArea = (areaId: string) => {
  return useQuery(api.shops.getShops, { 
    areaId: areaId as Id<"areas"> 
  });
};

export const useShopsByManager = (managerId: string) => {
  return useQuery(api.shops.getShopsByManager, { 
    managerId: managerId as Id<"users"> 
  });
};

export const useCreateShop = () => {
  return useMutation(api.shops.createShop);
};

export const useUpdateShop = () => {
  return useMutation(api.shops.updateShop);
};

export const useDeleteShop = () => {
  return useMutation(api.shops.deleteShop);
};

export const useAssignManager = () => {
  return useMutation(api.shops.assignManager);
};

export const useRemoveManager = () => {
  return useMutation(api.shops.removeManager);
};
