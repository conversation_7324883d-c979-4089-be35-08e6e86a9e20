import { create } from 'zustand';
import { Area } from '@/lib/types';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { convex } from '@/lib/convex';
import { authenticatedConvex } from '@/lib/convex-auth';
import { 
  useAuthenticatedAreasQuery,
  useAuthenticatedAreaQuery,
  useAuthenticatedAccessibleAreasQuery,
  useAuthenticatedAreaUsersQuery,
  useAuthenticatedAreaShopsQuery
} from '@/lib/authenticated-hooks';

interface AreaState {
  areas: Area[];
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchAreas: (force?: boolean) => Promise<Area[]>;
  createArea: (name: string) => Promise<Area>;
  updateArea: (id: string, name: string) => Promise<Area>;
  deleteArea: (id: string) => Promise<void>;
  getAreaById: (id: string) => Area | undefined;
  getAreaUsers: (areaId: string) => Promise<any[]>;
  getAreaShops: (areaId: string) => Promise<any[]>;
}

// Cache duration: 5 minutes (areas change less frequently)
const CACHE_DURATION = 5 * 60 * 1000;

// Helper function to map Convex area to Area type
function mapConvexAreaToArea(convexArea: any): Area {
  return {
    id: convexArea._id,
    name: convexArea.name,
  };
}

export const useAreaStore = create<AreaState>((set, get) => ({
  areas: [],
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchAreas: async (force = false) => {
    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION && state.areas.length > 0) {
      return state.areas;
    }

    set({ isLoading: true, error: null });

    try {
      const convexAreas = await authenticatedConvex.getAreas();
      const areas = convexAreas.map(mapConvexAreaToArea);
      set({ areas, isLoading: false, lastFetch: now });
      return areas;
    } catch (error: any) {
      console.error('Error fetching areas:', error);
      set({ error: error.message || 'Failed to fetch areas', isLoading: false });
      throw error;
    }
  },

  createArea: async (name) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Creating area', name);

      const areaId = await authenticatedConvex.createArea(name);

      // Fetch the created area to get full data
      const createdArea = await authenticatedConvex.getArea(areaId);
      if (!createdArea) {
        throw new Error('Failed to retrieve created area');
      }

      const newArea = mapConvexAreaToArea(createdArea);

      console.log('Store: Area created successfully', newArea);

      set(state => ({
        areas: [...state.areas, newArea],
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return newArea;
    } catch (error: any) {
      console.error('Store: Error creating area', error);
      set({ error: error.message || 'Failed to create area', isLoading: false });
      throw error;
    }
  },

  updateArea: async (id, name) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Updating area', id, 'with name', name);

      await authenticatedConvex.updateArea(id as Id<"areas">, name);

      // Fetch updated area to get full data
      const updatedArea = await authenticatedConvex.getArea(id as Id<"areas">);

      if (!updatedArea) {
        throw new Error('Failed to retrieve updated area');
      }

      const mappedArea = mapConvexAreaToArea(updatedArea);

      console.log('Store: Area updated successfully', mappedArea);

      set(state => ({
        areas: state.areas.map(area => area.id === id ? mappedArea : area),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return mappedArea;
    } catch (error: any) {
      console.error('Store: Error updating area', error);
      set({ error: error.message || 'Failed to update area', isLoading: false });
      throw error;
    }
  },

  deleteArea: async (id) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Deleting area', id);

      await authenticatedConvex.deleteArea(id as Id<"areas">);

      console.log('Store: Area deleted successfully');

      set(state => ({
        areas: state.areas.filter(area => area.id !== id),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error deleting area', error);
      set({ error: error.message || 'Failed to delete area', isLoading: false });
      throw error;
    }
  },

  getAreaById: (id) => {
    return get().areas.find(area => area.id === id);
  },

  getAreaUsers: async (areaId) => {
    try {
      console.log('Store: Fetching users for area', areaId);
      
      const users = await authenticatedConvex.getAreaUsers(areaId as Id<"areas">);

      console.log('Store: Area users fetched successfully', users);
      return users;
    } catch (error: any) {
      console.error('Store: Error fetching area users', error);
      throw error;
    }
  },

  getAreaShops: async (areaId) => {
    try {
      console.log('Store: Fetching shops for area', areaId);
      
      const shops = await authenticatedConvex.getAreaShops(areaId as Id<"areas">);

      console.log('Store: Area shops fetched successfully', shops);
      return shops;
    } catch (error: any) {
      console.error('Store: Error fetching area shops', error);
      throw error;
    }
  }
}));

// React hooks for components using Convex real-time queries (authenticated versions)
export const useAreasQuery = (filters?: any) => {
  return useAuthenticatedAreasQuery(filters);
};

export const useAreaQuery = (areaId: string) => {
  return useAuthenticatedAreaQuery(areaId);
};

export const useAccessibleAreasQuery = () => {
  return useAuthenticatedAccessibleAreasQuery();
};

export const useAreaUsersQuery = (areaId: string) => {
  return useAuthenticatedAreaUsersQuery(areaId);
};

export const useAreaShopsQuery = (areaId: string) => {
  return useAuthenticatedAreaShopsQuery(areaId);
};

export const useCreateAreaMutation = () => {
  return useMutation(api.areas.createArea);
};

export const useUpdateAreaMutation = () => {
  return useMutation(api.areas.updateArea);
};

export const useDeleteAreaMutation = () => {
  return useMutation(api.areas.deleteArea);
};
