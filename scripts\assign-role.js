#!/usr/bin/env node

/**
 * Manually assign a role to a user
 * 
 * Usage: node scripts/assign-role.js <email> <role>
 * Example: node scripts/assign-role.js <EMAIL> shop_manager
 */

const { ConvexHttpClient } = require("convex/browser");

const CONVEX_URL = process.env.VITE_CONVEX_URL || "https://efficient-toucan-547.convex.cloud";

async function assignRole() {
  const args = process.argv.slice(2);
  
  if (args.length !== 2) {
    console.log("Usage: node scripts/assign-role.js <email> <role>");
    console.log("Available roles: shop_manager, shop_support, accounts, watcher");
    process.exit(1);
  }
  
  const [email, roleName] = args;
  
  console.log(`🎯 Assigning role "${roleName}" to user "${email}"...`);
  
  try {
    const client = new ConvexHttpClient(CONVEX_URL);
    
    // First, get all users to find the one with this email
    const users = await client.query("users:getUsers", {});
    const user = users.find(u => u.email === email);
    
    if (!user) {
      console.error(`❌ User with email "${email}" not found`);
      console.log("Available users:");
      users.forEach(u => console.log(`  - ${u.email}`));
      process.exit(1);
    }
    
    // Assign the role using WorkOS ID
    const result = await client.mutation("workosSync:syncUserRoleFromWorkOS", {
      workosUserId: user.workosId,
      roleNames: [roleName]
    });
    
    console.log("✅ Role assigned successfully!");
    console.log(`📊 User: ${email}`);
    console.log(`🎭 Role: ${roleName}`);
    console.log(`👤 User ID: ${result.userId}`);
    
  } catch (error) {
    console.error("❌ Error assigning role:", error);
    process.exit(1);
  }
}

// Run the assignment
assignRole();