import React, { useEffect, useState } from 'react';
import { SetPasswordForm } from '@/components/auth/SetPasswordForm';
import { useParams, useLocation } from 'react-router-dom';

const SetPassword: React.FC = () => {
  const { token: urlToken } = useParams<{ token: string }>();
  const location = useLocation();
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    console.log('SetPassword component mounted');
    console.log('Current URL:', window.location.href);

    // Try to extract token from various sources
    const extractToken = () => {
      // Try to get token from URL params first
      if (urlToken) {
        console.log('Found token in URL params:', urlToken);
        setToken(urlToken);
        return;
      }

      // If not in URL params, try to get from query params
      const queryParams = new URLSearchParams(location.search);
      console.log('URL search params:', Object.fromEntries(queryParams.entries()));

      const queryToken = queryParams.get('token');

      if (queryToken) {
        console.log('Found token in query params:', queryToken);
        setToken(queryToken);
        return;
      }

      // Check localStorage as last resort
      const storedToken = localStorage.getItem('invitation_token');
      if (storedToken) {
        console.log('Found token in localStorage:', storedToken);
        localStorage.removeItem('invitation_token'); // Remove to prevent loops
        setToken(storedToken);
        return;
      }

      console.log('No token found in any source');
    };

    extractToken().finally(() => {
      setIsLoading(false);
    });
  }, [urlToken, location]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
        <div className="w-full max-w-[420px]">
          <div className="bg-white rounded-2xl shadow-[0_4px_20px_rgba(0,0,0,0.05)] p-8 text-center">
            <div className="h-8 w-8 animate-spin rounded-full border-2 border-t-transparent border-primary mx-auto mb-4" />
            <p className="text-gray-500">Loading...</p>
          </div>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
        <div className="w-full max-w-[420px]">
          <div className="bg-white rounded-2xl shadow-[0_4px_20px_rgba(0,0,0,0.05)] p-8">
            <h2 className="text-2xl font-bold tracking-tight text-gray-900 text-center mb-4">
              Invalid Invitation Link
            </h2>
            <p className="text-center text-gray-500">
              The invitation link is invalid or has expired. Please contact your administrator for a new invitation.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
      <div className="w-full max-w-[420px]">
        <div className="bg-white rounded-2xl shadow-[0_4px_20px_rgba(0,0,0,0.05)] p-8">
          <SetPasswordForm token={token} />
        </div>
      </div>
    </div>
  );
};

export default SetPassword;
