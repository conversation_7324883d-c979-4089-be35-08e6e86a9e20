import { create } from 'zustand';
import { Shop } from '@/lib/types';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { convex } from '@/lib/convex';
import { authenticatedConvex } from '@/lib/convex-auth';
import { 
  useAuthenticatedShopsQuery,
  useAuthenticatedShopQuery,
  useAuthenticatedShopsByAreaQuery
} from '@/lib/authenticated-hooks';

interface ShopState {
  shops: Shop[];
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchShops: (force?: boolean) => Promise<Shop[]>;
  fetchShopsByArea: (areaId: string) => Promise<Shop[]>;
  createShop: (shopData: {
    name: string;
    location: string;
    areaId?: string;
    managerId?: string;
  }) => Promise<Shop>;
  updateShop: (id: string, updates: Partial<{
    name: string;
    location: string;
    areaId: string;
    managerId: string;
  }>) => Promise<Shop>;
  deleteShop: (id: string) => Promise<void>;
  getShopById: (id: string) => Shop | undefined;
  getShopsByArea: (areaId: string) => Shop[];
  assignManager: (shopId: string, managerId: string) => Promise<Shop>;
  removeManager: (shopId: string) => Promise<Shop>;
}

// Cache duration: 3 minutes
const CACHE_DURATION = 3 * 60 * 1000;

// Helper function to map Convex shop to Shop type
function mapConvexShopToShop(convexShop: any): Shop {
  return {
    id: convexShop._id,
    name: convexShop.name,
    location: convexShop.address || '',
    areaId: convexShop.areaId,
    managerId: convexShop.managerId || '',
  };
}

export const useShopStore = create<ShopState>((set, get) => ({
  shops: [],
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchShops: async (force = false) => {
    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION && state.shops.length > 0) {
      return state.shops;
    }

    set({ isLoading: true, error: null });

    try {
      const convexShops = await authenticatedConvex.getShops();
      const shops = convexShops.map(mapConvexShopToShop);
      set({ shops, isLoading: false, lastFetch: now });
      return shops;
    } catch (error: any) {
      console.error('Error fetching shops:', error);
      set({ error: error.message || 'Failed to fetch shops', isLoading: false });
      throw error;
    }
  },

  fetchShopsByArea: async (areaId) => {
    set({ isLoading: true, error: null });

    try {
      const convexShops = await authenticatedConvex.getShops(areaId as Id<"areas">);
      const shops = convexShops.map(mapConvexShopToShop);
      
      // Don't update the full shops list, just return the filtered results
      set({ isLoading: false });
      return shops;
    } catch (error: any) {
      console.error('Error fetching shops by area:', error);
      set({ error: error.message || 'Failed to fetch shops by area', isLoading: false });
      throw error;
    }
  },

  createShop: async (shopData) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Creating shop', shopData);

      const shopId = await authenticatedConvex.createShop(
        shopData.name,
        `SHOP-${Date.now()}`, // Generate a unique code
        shopData.areaId as Id<"areas">,
        shopData.managerId as Id<"users"> | undefined,
        shopData.location // address
      );

      // Fetch the created shop to get full data
      const createdShop = await authenticatedConvex.getShop(shopId);
      if (!createdShop) {
        throw new Error('Failed to retrieve created shop');
      }

      const newShop = mapConvexShopToShop(createdShop);

      console.log('Store: Shop created successfully', newShop);

      set(state => ({
        shops: [...state.shops, newShop],
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return newShop;
    } catch (error: any) {
      console.error('Store: Error creating shop', error);
      set({ error: error.message || 'Failed to create shop', isLoading: false });
      throw error;
    }
  },

  updateShop: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Updating shop', id, 'with updates', updates);

      await authenticatedConvex.updateShop(
        id as Id<"shops">,
        updates.name,
        undefined, // code
        updates.areaId as Id<"areas"> | undefined,
        updates.managerId as Id<"users"> | undefined,
        updates.location // address
      );

      // Fetch updated shop to get full data
      const updatedShop = await authenticatedConvex.getShop(id as Id<"shops">);

      if (!updatedShop) {
        throw new Error('Failed to retrieve updated shop');
      }

      const mappedShop = mapConvexShopToShop(updatedShop);

      console.log('Store: Shop updated successfully', mappedShop);

      set(state => ({
        shops: state.shops.map(shop => shop.id === id ? mappedShop : shop),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return mappedShop;
    } catch (error: any) {
      console.error('Store: Error updating shop', error);
      set({ error: error.message || 'Failed to update shop', isLoading: false });
      throw error;
    }
  },

  deleteShop: async (id) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Deleting shop', id);

      await authenticatedConvex.deleteShop(id as Id<"shops">);

      console.log('Store: Shop deleted successfully');

      set(state => ({
        shops: state.shops.filter(shop => shop.id !== id),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error deleting shop', error);
      set({ error: error.message || 'Failed to delete shop', isLoading: false });
      throw error;
    }
  },

  getShopById: (id) => {
    return get().shops.find(shop => shop.id === id);
  },

  getShopsByArea: (areaId) => {
    return get().shops.filter(shop => shop.areaId === areaId);
  },

  assignManager: async (shopId, managerId) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Assigning manager', managerId, 'to shop', shopId);

      await convex.mutation(api.shops.assignManager, {
        shopId: shopId as Id<"shops">,
        managerId: managerId as Id<"users">,
      });

      // Fetch updated shop to get full data
      const updatedShop = await convex.query(api.shops.getShop, { 
        shopId: shopId as Id<"shops"> 
      });

      if (!updatedShop) {
        throw new Error('Failed to retrieve updated shop');
      }

      const mappedShop = mapConvexShopToShop(updatedShop);

      console.log('Store: Manager assigned successfully', mappedShop);

      set(state => ({
        shops: state.shops.map(shop => shop.id === shopId ? mappedShop : shop),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return mappedShop;
    } catch (error: any) {
      console.error('Store: Error assigning manager', error);
      set({ error: error.message || 'Failed to assign manager', isLoading: false });
      throw error;
    }
  },

  removeManager: async (shopId) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Removing manager from shop', shopId);

      await convex.mutation(api.shops.removeManager, {
        shopId: shopId as Id<"shops">,
      });

      // Fetch updated shop to get full data
      const updatedShop = await convex.query(api.shops.getShop, { 
        shopId: shopId as Id<"shops"> 
      });

      if (!updatedShop) {
        throw new Error('Failed to retrieve updated shop');
      }

      const mappedShop = mapConvexShopToShop(updatedShop);

      console.log('Store: Manager removed successfully', mappedShop);

      set(state => ({
        shops: state.shops.map(shop => shop.id === shopId ? mappedShop : shop),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return mappedShop;
    } catch (error: any) {
      console.error('Store: Error removing manager', error);
      set({ error: error.message || 'Failed to remove manager', isLoading: false });
      throw error;
    }
  }
}));

// React hooks for components using Convex real-time queries (authenticated versions)
export const useShopsQuery = (filters?: any) => {
  return useAuthenticatedShopsQuery(filters);
};

export const useShopQuery = (shopId: string) => {
  return useAuthenticatedShopQuery(shopId);
};

export const useShopsByAreaQuery = (areaId: string) => {
  return useAuthenticatedShopsByAreaQuery(areaId);
};

export const useShopsByManagerQuery = (managerId: string) => {
  return useQuery(api.shops.getShopsByManager, { 
    managerId: managerId as Id<"users"> 
  });
};

export const useCreateShopMutation = () => {
  return useMutation(api.shops.createShop);
};

export const useUpdateShopMutation = () => {
  return useMutation(api.shops.updateShop);
};

export const useDeleteShopMutation = () => {
  return useMutation(api.shops.deleteShop);
};

export const useAssignManagerMutation = () => {
  return useMutation(api.shops.assignManager);
};

export const useRemoveManagerMutation = () => {
  return useMutation(api.shops.removeManager);
};
