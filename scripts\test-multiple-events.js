// Script to test multiple WorkOS webhook events
const fetch = require('node-fetch');
const crypto = require('crypto');

const WEBHOOK_URL = 'https://efficient-toucan-547.convex.cloud/http/workos-webhook';
const WEBHOOK_SECRET = 'test_secret_for_development';

// Create a test webhook payload
const createTestPayload = (eventType, data) => {
  const timestamp = Date.now();
  const payload = {
    event: eventType,
    data: data,
    created_at: new Date().toISOString()
  };
  
  const payloadString = JSON.stringify(payload);
  
  // Create signature according to WorkOS format
  const signatureString = timestamp + '.' + payloadString;
  const signature = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(signatureString, 'utf8')
    .digest('hex');
  
  const workosSignature = `t=${timestamp},v1=${signature}`;
  
  return {
    payload: payloadString,
    signature: workosSignature,
    timestamp
  };
};

async function testEvent(eventType, data) {
  try {
    console.log(`\n=== Testing ${eventType} ===`);
    
    const testData = createTestPayload(eventType, data);
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'WorkOS-Signature': testData.signature,
        'User-Agent': 'WorkOS-Webhook/1.0'
      },
      body: testData.payload
    });
    
    const responseText = await response.text();
    
    if (response.ok) {
      console.log(`✅ ${eventType}: SUCCESS`);
    } else {
      console.log(`❌ ${eventType}: FAILED (${response.status})`);
      console.log(`Response: ${responseText}`);
    }
    
    return response.ok;
    
  } catch (error) {
    console.error(`❌ ${eventType}: ERROR -`, error.message);
    return false;
  }
}

async function testAllEvents() {
  console.log('Testing multiple WorkOS webhook events...');
  
  const testCases = [
    {
      event: 'user.created',
      data: {
        id: 'user_multi_test_' + Date.now(),
        email: '<EMAIL>',
        first_name: 'Multi',
        last_name: 'Created',
        email_verified: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    },
    {
      event: 'user.updated',
      data: {
        id: 'user_multi_test_updated',
        email: '<EMAIL>',
        first_name: 'Multi',
        last_name: 'Updated',
        email_verified: true,
        created_at: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        updated_at: new Date().toISOString()
      }
    },
    {
      event: 'user.deleted',
      data: {
        id: 'user_multi_test_deleted',
        email: '<EMAIL>',
        first_name: 'Multi',
        last_name: 'Deleted',
        email_verified: true,
        created_at: new Date(Date.now() - 86400000).toISOString(),
        updated_at: new Date().toISOString()
      }
    },
    {
      event: 'organization.created',
      data: {
        id: 'org_test_' + Date.now(),
        name: 'Test Organization',
        domains: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    },
    {
      event: 'organization_membership.created',
      data: {
        id: 'orgmem_test_' + Date.now(),
        user_id: 'user_test_123',
        organization_id: 'org_test_123',
        role: 'member',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    }
  ];
  
  let successCount = 0;
  
  for (const testCase of testCases) {
    const success = await testEvent(testCase.event, testCase.data);
    if (success) successCount++;
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n=== Test Summary ===`);
  console.log(`✅ ${successCount}/${testCases.length} events processed successfully`);
  
  if (successCount === testCases.length) {
    console.log('🎉 All webhook events are working correctly!');
  } else {
    console.log('⚠️ Some webhook events failed. Check the logs for details.');
  }
}

testAllEvents();