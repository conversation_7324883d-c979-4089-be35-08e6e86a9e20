/**
 * Manual authentication test that can be run from browser console
 * This helps debug authentication issues step by step
 */

import { getSession } from './workos-auth';
import { convex } from './convex';
import { api } from '../../convex/_generated/api';

export const manualAuthTest = async () => {
  console.log('🔍 Starting manual authentication test...');
  
  try {
    // Step 1: Check WorkOS session
    console.log('\n📋 Step 1: Checking WorkOS session...');
    const session = await getSession();
    
    if (!session) {
      console.error('❌ No WorkOS session found');
      console.log('💡 Please log in first by going to /login');
      return { step: 1, success: false, error: 'No WorkOS session' };
    }
    
    console.log('✅ WorkOS session found:');
    console.log('   - User ID:', session.user.id);
    console.log('   - Email:', session.user.email);
    console.log('   - Name:', session.user.firstName, session.user.lastName);
    
    // Step 2: Test direct backend call with WorkOS user ID
    console.log('\n📋 Step 2: Testing direct backend call...');
    try {
      const testResult = await convex.query(api.testAuthWithWorkOS.testPermissionWithWorkOSUser, {
        workosUserId: session.user.id,
        permission: 'area:create'
      });
      
      console.log('✅ Backend authentication test result:', testResult);
      
      if (!testResult.success) {
        console.error('❌ Backend authentication failed:', testResult.error);
        return { step: 2, success: false, error: testResult.error, session };
      }
      
      // Step 3: Test area creation
      console.log('\n📋 Step 3: Testing area creation...');
      const areaName = `Manual Test Area ${Date.now()}`;
      
      try {
        const createResult = await convex.mutation(api.testAuthWithWorkOS.testCreateAreaWithWorkOSAuth, {
          name: areaName,
          workosUserId: session.user.id
        });
        
        console.log('✅ Area creation test result:', createResult);
        
        if (!createResult.success) {
          console.error('❌ Area creation failed:', createResult.error);
          return { step: 3, success: false, error: createResult.error, session, backendAuth: testResult };
        }
        
        // Step 4: Test the frontend wrapper
        console.log('\n📋 Step 4: Testing frontend authentication wrapper...');
        try {
          const { authenticatedConvex } = await import('./convex-auth');
          const wrapperResult = await authenticatedConvex.createArea(`Wrapper Test ${Date.now()}`);
          
          console.log('✅ Frontend wrapper test result:', wrapperResult);
          
          return {
            step: 4,
            success: true,
            session,
            backendAuth: testResult,
            areaCreation: createResult,
            wrapperTest: wrapperResult,
            message: '🎉 All authentication tests passed!'
          };
          
        } catch (wrapperError) {
          console.error('❌ Frontend wrapper failed:', wrapperError);
          return {
            step: 4,
            success: false,
            error: wrapperError.message,
            session,
            backendAuth: testResult,
            areaCreation: createResult,
            message: 'Backend works but frontend wrapper failed'
          };
        }
        
      } catch (createError) {
        console.error('❌ Area creation failed:', createError);
        return { step: 3, success: false, error: createError.message, session, backendAuth: testResult };
      }
      
    } catch (backendError) {
      console.error('❌ Backend authentication failed:', backendError);
      return { step: 2, success: false, error: backendError.message, session };
    }
    
  } catch (error) {
    console.error('❌ Manual auth test failed:', error);
    return { step: 0, success: false, error: error.message };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).manualAuthTest = manualAuthTest;
  console.log('💡 Manual auth test available as: window.manualAuthTest()');
}