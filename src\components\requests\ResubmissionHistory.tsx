import React from 'react';
import { format } from 'date-fns';
import { Clock, AlertCircle, MessageSquare, Shield } from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdminAction } from '@/lib/types';

interface ResubmissionHistoryProps {
  history: {
    timestamp: Date;
    changes: Record<string, { from: any; to: any }>;
    notes: string;
    previousRejectionReason: string;
    adminComments?: AdminAction[];
  }[];
}

const ResubmissionHistory: React.FC<ResubmissionHistoryProps> = ({ history }) => {
  const formatValue = (value: any) => {
    if (value === undefined || value === null) return 'None';
    if (typeof value === 'boolean') return value ? 'Yes' : 'No';
    if (typeof value === 'number') return value.toString();
    return value;
  };

  return (
    <Card className="mt-4 shadow-sm">
      <CardHeader className="pb-2">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Clock className="h-4 w-4" />
          Resubmission History
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="relative">
          <div className="space-y-6">
            {history.map((entry, index) => (
              <div key={index} className="relative flex items-start gap-4">
                <div className="flex flex-col items-center">
                  <div className="w-3 h-3 bg-emerald-500 rounded-full"></div>
                  {index < history.length - 1 && (
                    <div className="w-0.5 h-16 bg-gray-200 mt-2"></div>
                  )}
                </div>
                <div className="flex-1 pb-8">
                  <div className="flex items-center justify-between mb-1">
                    <p className="text-sm text-gray-500">
                      Request resubmitted with changes
                    </p>
                    <span className="text-sm text-gray-400">
                      [{format(new Date(entry.timestamp), 'MMM dd, yyyy')}]
                    </span>
                  </div>
                  <h3 className="font-semibold text-gray-900 mb-2">Resubmission #{index + 1}</h3>

                  {/* Previous Rejection Reason if exists */}
                  {entry.previousRejectionReason && (
                    <div className="mb-3 p-2 bg-red-50 rounded text-sm">
                      <div className="flex items-center gap-1 text-red-700 font-medium mb-1">
                        <AlertCircle className="h-3 w-3" />
                        Previous Rejection Reason
                      </div>
                      <p className="text-red-600">{entry.previousRejectionReason}</p>
                    </div>
                  )}

                  {/* Changes Made */}
                  {Object.keys(entry.changes).length > 0 && (
                    <div className="mb-3">
                      <h4 className="text-sm font-medium mb-2 text-gray-700">Changes Made</h4>
                      <div className="space-y-1 text-sm text-gray-600">
                        {Object.entries(entry.changes).map(([field, change]) => (
                          <div key={field}>
                            <span className="font-medium">{field}: </span>
                            {field === 'ticketImage' ? (
                              <span>Image updated</span>
                            ) : (
                              <span>
                                {`${formatValue(change.from)} → ${formatValue(change.to)}`}
                              </span>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Shop Manager's Notes */}
                  <div className="text-sm text-gray-600 bg-emerald-50 p-2 rounded">
                    <div className="font-medium text-emerald-800 mb-1">Shop Manager's Notes</div>
                    {entry.notes || 'No notes provided'}
                  </div>

                  {/* Admin Comments for this Resubmission */}
                  {entry.adminComments && entry.adminComments.length > 0 && (
                    <div className="mt-3">
                      <h4 className="text-sm font-medium mb-2 text-gray-700 flex items-center gap-1">
                        <Shield className="h-3 w-3 text-blue-600" />
                        Admin Response
                      </h4>
                      <div className="space-y-2">
                        {entry.adminComments.map((adminComment, commentIndex) => (
                          <div key={adminComment.id} className="bg-blue-50 p-2 rounded text-sm">
                            <div className="flex items-center justify-between mb-1">
                              <div className="flex items-center gap-2">
                                <span className="font-medium text-blue-900">
                                  {adminComment.adminName}
                                </span>
                                <Badge variant="outline" className="text-xs">
                                  {adminComment.adminRole.replace('_', ' ')}
                                </Badge>
                              </div>
                              <span className="text-xs text-blue-600">
                                {format(new Date(adminComment.timestamp), 'MMM d')}
                              </span>
                            </div>
                            <p className="text-blue-800">{adminComment.comment}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ResubmissionHistory;
