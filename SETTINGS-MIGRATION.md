# Settings Migration Guide

This guide explains how to migrate from the previous settings implementation to the new Convex-based settings system.

## What Changed

### Before (Mock/Hardcoded Settings)
```typescript
// Settings were hardcoded or used mock services
const momoThreshold = 5000; // Hardcoded
const settings = await getMockSettings(); // Mock service
```

### After (Convex Settings)
```typescript
// Settings are managed in Convex with real-time updates
const settings = await convex.query(api.settings.getSettings, {});
const momoThreshold = settings.mobile_money_approval_threshold;
```

## Migration Steps

### 1. Update Settings Store Usage

**Old way:**
```typescript
import { useSettingsStore } from '@/stores';

const { settings } = useSettingsStore();
const threshold = settings?.momoThreshold || 5000; // Manual fallback
```

**New way:**
```typescript
import { useSettingsStore, useSettingsQuery } from '@/stores';

// Option 1: Use the store (with caching)
const { settings } = useSettingsStore();
const threshold = settings?.momoThreshold; // Automatic fallback

// Option 2: Use real-time query
const settings = useSettingsQuery();
const threshold = settings?.mobile_money_approval_threshold;
```

### 2. Update Setting Names

The setting keys have been standardized:

| Old Frontend Key | New Convex Key |
|------------------|----------------|
| `momoThreshold` | `mobile_money_approval_threshold` |
| `bankThreshold` | `bank_transfer_approval_threshold` |
| `whitelistedDomains` | `whitelisted_domains` |

### 3. Update Components

**Before:**
```typescript
const SettingsPage = () => {
  const { settings, updateSettings } = useSettingsStore();
  
  const handleUpdate = async (values) => {
    await updateSettings({
      momoThreshold: values.momo,
      bankThreshold: values.bank
    });
  };
  
  return (
    <form onSubmit={handleUpdate}>
      <input 
        defaultValue={settings?.momoThreshold || 5000}
        name="momo" 
      />
    </form>
  );
};
```

**After:**
```typescript
const SettingsPage = () => {
  const { settings, updateSettings } = useSettingsStore();
  
  const handleUpdate = async (values) => {
    await updateSettings({
      momoThreshold: values.momo,
      bankThreshold: values.bank
    });
  };
  
  return (
    <form onSubmit={handleUpdate}>
      <input 
        defaultValue={settings?.momoThreshold}
        name="momo" 
      />
    </form>
  );
};
```

### 4. Remove Mock Services

Delete or comment out these files:
- `src/services/mockServices.ts` (settings-related functions)
- `src/lib/mockData.ts` (settings data)

### 5. Update Environment Setup

Ensure your Convex environment has the settings initialized:

```typescript
// Run once in development
await convex.mutation(api.init.initializeSystem, {});
```

## New Features Available

### 1. Real-time Settings Updates
```typescript
// Settings automatically update across all clients
const settings = useSettingsQuery();
```

### 2. Settings Administration
```typescript
// Get settings with metadata for admin UI
const settingsWithMeta = useSettingsWithMetadataQuery();

// Update individual settings
const updateSetting = useUpdateSettingMutation();
await updateSetting({
  key: "mobile_money_approval_threshold",
  value: 6000
});
```

### 3. Settings Categories
```typescript
// Get settings by category
const securitySettings = await convex.query(api.settings.getSettingsByCategory, {
  category: "security"
});
```

### 4. Settings Reset
```typescript
// Reset to defaults
const resetSettings = useResetSettingsToDefaultMutation();
await resetSettings({
  keys: ["mobile_money_approval_threshold"]
});
```

## Validation Changes

### Before
```typescript
// Manual validation in components
if (momoThreshold < 0 || momoThreshold > 1000000) {
  throw new Error("Invalid threshold");
}
```

### After
```typescript
// Automatic validation in Convex
await updateSettings({
  momoThreshold: 6000 // Automatically validated
});
// Throws error if invalid
```

## Error Handling

### Before
```typescript
try {
  await updateSettings(values);
} catch (error) {
  // Generic error handling
  setError("Failed to update settings");
}
```

### After
```typescript
try {
  await updateSettings(values);
} catch (error) {
  // Specific error messages from validation
  if (error.message.includes("threshold")) {
    setError("Threshold value is invalid");
  } else if (error.message.includes("permission")) {
    setError("You don't have permission to update settings");
  }
}
```

## Testing Migration

### 1. Test Settings Fetch
```typescript
// Should return settings object with all defaults
const settings = await convex.query(api.settings.getSettings, {});
console.log(settings);
```

### 2. Test Settings Update
```typescript
// Should update and persist
await convex.mutation(api.settings.updateSettings, {
  settings: {
    mobile_money_approval_threshold: 6000
  }
});
```

### 3. Test Permissions
```typescript
// Should fail for non-accounts users
try {
  await convex.mutation(api.settings.updateSettings, {
    settings: { mobile_money_approval_threshold: 6000 }
  });
} catch (error) {
  console.log("Permission check working:", error.message);
}
```

## Rollback Plan

If issues occur, you can temporarily rollback by:

1. **Restore fallback logic** in settings store:
```typescript
const settings = convexSettings || {
  momoThreshold: 5000,
  bankThreshold: 10000,
  whitelistedDomains: ['kmkentertainment.com', 'mybet.africa']
};
```

2. **Use environment variables** as backup:
```typescript
const momoThreshold = settings?.momoThreshold || 
  parseInt(import.meta.env.VITE_MOMO_THRESHOLD) || 
  5000;
```

## Verification Checklist

- [ ] Settings store updated to use Convex API
- [ ] All components using new setting keys
- [ ] Mock services removed/disabled
- [ ] Default settings initialized in Convex
- [ ] Permissions working correctly
- [ ] Real-time updates working
- [ ] Error handling updated
- [ ] Tests passing

## Support

If you encounter issues:

1. **Check Convex dev server** is running
2. **Verify settings are initialized** with `api.init.checkSystemInitialization`
3. **Check user permissions** for settings access
4. **Review audit logs** for setting changes
5. **Test with the settings test script**

## Next Steps

After migration:

1. **Create admin settings page** using new metadata functions
2. **Add settings validation** to forms
3. **Implement settings backup/restore**
4. **Add settings change notifications**
5. **Create settings documentation** for users