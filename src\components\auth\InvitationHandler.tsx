import React, { useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';

/**
 * This component acts as middleware to capture invitation tokens
 * from any page and redirect to the set-password page
 *
 * Updated to work with WorkOS invitation system
 */
const InvitationHandler: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Skip if we're already on the set-password page
    if (location.pathname.includes('/set-password')) {
      return;
    }

    const detectInvitation = () => {
      // Check URL search parameters
      const searchParams = new URLSearchParams(location.search);

      // Look for WorkOS invitation parameters
      const token = searchParams.get('token') || searchParams.get('invitation_token');
      const email = searchParams.get('email') || searchParams.get('invitation_email');

      if (token) {
        console.log('InvitationHandler: Found invitation token in URL, redirecting to set-password');

        // Build the set-password URL with parameters
        const setPasswordUrl = new URL('/set-password', window.location.origin);
        setPasswordUrl.searchParams.set('token', token);
        
        if (email) {
          setPasswordUrl.searchParams.set('email', email);
        }

        // Navigate to set-password page
        navigate(setPasswordUrl.pathname + setPasswordUrl.search);
        return;
      }

      // Check for stored invitation data (from previous redirects)
      const storedToken = localStorage.getItem('workos_invitation_token');
      const storedEmail = localStorage.getItem('workos_invitation_email');

      if (storedToken && (location.pathname === '/' || location.pathname === '/login')) {
        console.log('InvitationHandler: Found stored invitation token, redirecting to set-password');
        
        // Build the set-password URL with stored parameters
        const setPasswordUrl = new URL('/set-password', window.location.origin);
        setPasswordUrl.searchParams.set('token', storedToken);
        
        if (storedEmail) {
          setPasswordUrl.searchParams.set('email', storedEmail);
        }

        // Clear stored data
        localStorage.removeItem('workos_invitation_token');
        localStorage.removeItem('workos_invitation_email');

        // Navigate to set-password page
        navigate(setPasswordUrl.pathname + setPasswordUrl.search);
        return;
      }
    };

    detectInvitation();
  }, [location, navigate]);

  // This component doesn't render anything
  return null;
};

export default InvitationHandler;
