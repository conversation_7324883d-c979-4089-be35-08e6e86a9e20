import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient("https://efficient-toucan-547.convex.cloud");

async function testWorkOSConfig() {
  console.log("🧪 Testing WorkOS Configuration...");
  console.log("=" .repeat(50));
  
  try {
    // Test 1: Check if WorkOS provider is configured
    console.log("1. Checking WorkOS provider configuration...");
    const providers = await client.query("auth:getAuthConfiguredProviders", {});
    console.log("✅ Configured providers:", providers);
    
    if (providers.includes("workos")) {
      console.log("✅ WorkOS provider is properly configured!");
    } else {
      console.log("❌ WorkOS provider not found in configured providers");
      return false;
    }
    
    // Test 2: Check session info (should be null for unauthenticated)
    console.log("\n2. Checking session info...");
    const sessionInfo = await client.query("auth:getSessionInfo", {});
    console.log("📊 Session info:", sessionInfo || "No active session (expected)");
    
    // Test 3: Test the sign-in URL construction
    console.log("\n3. Testing sign-in URL construction...");
    const convexUrl = "https://efficient-toucan-547.convex.cloud";
    const signInUrl = `${convexUrl}/api/auth/signin/workos`;
    console.log("🔗 Sign-in URL:", signInUrl);
    
    // Test 4: Check if we can access the sign-in endpoint (should return HTML)
    console.log("\n4. Testing sign-in endpoint accessibility...");
    try {
      const response = await fetch(signInUrl, { method: 'HEAD' });
      console.log("✅ Sign-in endpoint status:", response.status);
      if (response.status === 200 || response.status === 302) {
        console.log("✅ Sign-in endpoint is accessible");
      } else {
        console.log("⚠️  Sign-in endpoint returned status:", response.status);
      }
    } catch (fetchError) {
      console.log("❌ Error accessing sign-in endpoint:", fetchError.message);
    }
    
    console.log("\n" + "=" .repeat(50));
    console.log("🎉 WorkOS Configuration Test Complete!");
    console.log("✅ WorkOS is properly configured and ready for authentication");
    
    console.log("\n📝 Next steps:");
    console.log("   1. Open http://localhost:8080/login in your browser");
    console.log("   2. Click 'Sign In with WorkOS'");
    console.log("   3. You should be redirected to WorkOS authentication");
    
    return true;
    
  } catch (error) {
    console.error("❌ Configuration test failed:", error.message);
    return false;
  }
}

testWorkOSConfig();