
export type UserRole = 'shop_manager' | 'shop_support' | 'accounts' | 'watcher';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  shop?: string;
  areas?: string[];
  avatar?: string;
  phone?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export type PaymentMethod = 'mobile_money' | 'bank_transfer';

export type RequestStatus = 'pending' | 'approved' | 'rejected' | 'resubmitted';

export interface AdminAction {
  id: string;
  type: 'approve' | 'reject' | 'comment' | 'review';
  timestamp: Date;
  adminId: string;
  adminName: string;
  adminRole: UserRole;
  comment: string;
  previousStatus?: RequestStatus;
  newStatus?: RequestStatus;
  metadata?: Record<string, any>;
}

export interface Request {
  id: string;
  title: string;
  shopId: string;
  shopName: string;
  amount: number;
  paymentMethod: PaymentMethod;
  status: RequestStatus;
  createdBy: string;
  createdByName: string;
  createdAt: Date;
  updatedAt: Date;
  approvedBy?: string;
  approvedByName?: string | null;
  approvedAt?: Date;
  approvalReason?: string;
  rejectedBy?: string;
  rejectedByName?: string | null;
  rejectedAt?: Date;
  rejectionReason?: string;
  customerName?: string;
  customerContact?: string;
  ticketImage?: string;
  comments?: string;

  // Fields
  areaId?: string;
  areaName?: string;
  ticketId?: string;
  productId?: string;
  networkProvider?: string;
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
  attachments?: string[];

  // Admin action history - professional sequence of all admin interactions
  adminActionHistory?: AdminAction[];

  // Resubmission history
  resubmissionHistory?: {
    timestamp: Date;
    changes: Record<string, { from: any; to: any }>;
    notes: string;
    previousRejectionReason: string;
    adminComments?: AdminAction[]; // Admin comments related to this resubmission
  }[];
}

export interface Shop {
  id: string;
  name: string;
  location: string;
  managerId: string;
  areaId?: string;
  areaName?: string;
  managerName?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface DashboardStats {
  totalAmount: number;
  totalRequests: number;
  pendingRequests: number;
  approvedRequests: number;
  rejectedRequests: number;
  resubmittedRequests: number;
  mobileMoneyAmount: number;
  bankTransferAmount: number;
  uniqueAreas: number;
  uniqueShops: number;
  monthlyStats: Array<{
    month: number;
    total: number;
    count: number;
  }>;
}

export interface Area {
  id: string;
  name: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AppNotification {
  id: string;
  title: string;
  message: string;
  type: string;
  recipientId: string;
  requestId?: string;
  createdAt: Date;
  updatedAt?: Date;
  isRead: boolean;
}

export interface Settings {
  id: number;
  momoThreshold: number;
  bankThreshold: number;
  whitelistedDomains: string[];
  updatedBy?: string;
  updatedAt?: Date;
}
