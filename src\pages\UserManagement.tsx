
import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import PageTransition from '@/components/common/PageTransition';
import { Button } from '@/components/ui/button';
import { PlusCircle, Edit, Trash, Clock, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from '@/components/ui/use-toast';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { 
  useUsersAndInvitationsQuery, 
  useRolesQuery, 
  useInviteUserMutation,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useResendInvitationMutation,
  useCancelInvitationMutation
} from '@/stores/userStore';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
// Removed old store import - now using Convex queries
import { UserRole } from '@/lib/types';
import { MultiSelect } from "@/components/ui/multi-select";
import { useAuthenticatedAreasQuery, useWorkOSUserId } from '@/lib/authenticated-hooks';

const UserManagement: React.FC = () => {
  useRequireAuth(['accounts']);

  // Use Convex hooks for real-time data
  const usersAndInvitationsData = useUsersAndInvitationsQuery();
  const rolesQuery = useRolesQuery();
  const inviteUserMutation = useInviteUserMutation();
  const updateUserMutation = useUpdateUserMutation();
  const deleteUserMutation = useDeleteUserMutation();
  const resendInvitationMutation = useResendInvitationMutation();
  const cancelInvitationMutation = useCancelInvitationMutation();
  // Get WorkOS user ID for authenticated mutations
  const { workosUserId } = useWorkOSUserId();
  
  // Use direct query instead of the problematic hook
  const areasData = useQuery(
    api.areas.getAreas,
    workosUserId ? { workosUserId, isActive: true } : "skip"
  );
  
  // Use Convex query for shops data
  const shopsData = useQuery(
    api.shops.getShops,
    workosUserId ? { workosUserId, isActive: true } : "skip"
  );
  const shops = Array.isArray(shopsData) ? shopsData : [];

  const [isInviting, setIsInviting] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<{
    id: string;
    name: string;
    email: string;
    role: string;
    areaId: string; // For shop managers to select area first
    shop: string | null;
    areas: string[] | null;
  } | null>(null);
  const [isInviteDialogOpen, setIsInviteDialogOpen] = useState(false);
  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    role: '',
    areaId: '', // For shop managers to select area first
    shopId: '',
    areas: [] as string[]
  });
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);
  const [invitedUserDetails, setInvitedUserDetails] = useState<{name: string; email: string; role: string}>({ name: '', email: '', role: '' });

  const whitelistedDomains = ['kmkentertainment.com', 'mybet.africa'];

  const roleLabels: Record<string, string> = {
    'accounts': 'Accounts',
    'shop_manager': 'Shop Manager',
    'shop_support': 'Shop Support',
    'watcher': 'Watcher'
  };

  // Extract data from Convex queries
  const users = usersAndInvitationsData?.users || [];
  const invitations = usersAndInvitationsData?.invitations || [];
  const roles = rolesQuery || []; // rolesQuery is now the roles array directly
  
  // Debug the areasData to see what we're getting
  console.log('UserManagement areasData:', areasData);
  console.log('UserManagement areasData type:', typeof areasData);
  console.log('UserManagement areasData isArray:', Array.isArray(areasData));
  console.log('UserManagement workosUserId:', workosUserId);
  
  // Handle areas data properly - the hook returns an object with isLoading or the actual array
  const areas = Array.isArray(areasData) ? areasData : [];
  const areasLoading = areasData?.isLoading === true;
  
  console.log('UserManagement processed areas:', areas);
  console.log('UserManagement areas length:', areas.length);
  console.log('UserManagement areasLoading:', areasLoading);
  
  const isLoading = usersAndInvitationsData === undefined || rolesQuery === undefined || areasData === undefined || shopsData === undefined;

  // Data fetching is now handled by Convex queries automatically

  const handleEditUser = async () => {
    if (!selectedUser || !workosUserId) {
      toast({
        title: "Error",
        description: "User data or authentication not available",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsInviting(true);

      // Parse the name into firstName and lastName
      const nameParts = selectedUser.name.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      await updateUserMutation({
        userId: selectedUser.id,
        firstName,
        lastName,
        workosUserId,
      });

      toast({
        title: "Success",
        description: "User information updated successfully",
      });

      setIsEditDialogOpen(false);
      setSelectedUser(null);
    } catch (error: any) {
      console.error('Error updating user:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update user",
        variant: "destructive",
      });
    } finally {
      setIsInviting(false);
    }
  };

  const handleInviteUser = async () => {
    console.log('handleInviteUser called with:', newUser);

    // First check required fields
    if (!newUser.name || !newUser.email || !newUser.role) {
      console.log('Validation failed: Missing required fields');
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    const normalizedNewEmail = newUser.email.toLowerCase().trim();

    // Check if email domain is whitelisted
    const emailDomain = normalizedNewEmail.split('@')[1];
    const isWhitelisted = whitelistedDomains.some(domain =>
      emailDomain.toLowerCase() === domain.toLowerCase()
    );

    if (!isWhitelisted) {
      toast({
        title: "Invalid Domain",
        description: "User's email domain is not whitelisted",
        variant: "destructive",
      });
      return;
    }

    // Check if email already exists in users or pending invitations
    const emailExistsInUsers = users.some(user =>
      user.email.toLowerCase().trim() === normalizedNewEmail
    );

    const emailExistsInInvitations = invitations.some(invitation =>
      invitation.email.toLowerCase().trim() === normalizedNewEmail && invitation.status === 'pending'
    );

    if (emailExistsInUsers) {
      toast({
        title: "Validation Error",
        description: "A user with this email address already exists in the system",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    if (emailExistsInInvitations) {
      toast({
        title: "Validation Error",
        description: "A pending invitation already exists for this email address",
        variant: "destructive",
        duration: 3000,
      });
      return;
    }

    // Check shop manager requirements
    if (newUser.role === 'shop_manager') {
      if (!newUser.areaId) {
        toast({
          title: "Validation Error",
          description: "Please select an area for the Shop Manager",
          variant: "destructive",
        });
        return;
      }
      if (!newUser.shopId) {
        toast({
          title: "Validation Error",
          description: "Please select a shop for the Shop Manager",
          variant: "destructive",
        });
        return;
      }
    }

    // Check watcher role requirement
    if (newUser.role === 'watcher' && newUser.areas.length === 0) {
      toast({
        title: "Validation Error",
        description: "Please select at least one area for the Watcher",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsInviting(true);
      console.log('Starting invitation process...');

      // Ensure roles is loaded and is an array
      if (!roles || !Array.isArray(roles) || roles.length === 0) {
        throw new Error('Roles data is not available. Please wait for the page to load completely.');
      }

      // Ensure WorkOS user ID is available
      if (!workosUserId) {
        throw new Error('Authentication required. Please refresh the page and try again.');
      }

      // Find the role object
      const roleObj = roles.find(r => r.name === newUser.role);
      if (!roleObj) {
        console.error('Available roles:', roles.map(r => r.name));
        throw new Error(`Role ${newUser.role} not found`);
      }

      // Get area IDs if specified
      let areaIds: string[] | undefined;
      if (newUser.role === 'watcher' && newUser.areas.length > 0) {
        areaIds = newUser.areas;
      } else if (newUser.role === 'shop_manager' && newUser.areaId) {
        // For shop managers, use the selected area
        areaIds = [newUser.areaId];
      }

      // Get shop ID for shop managers
      let shopId: string | undefined;
      if (newUser.role === 'shop_manager' && newUser.shopId) {
        shopId = newUser.shopId;
      }

      // Parse the name into firstName and lastName
      const nameParts = newUser.name.trim().split(' ');
      const firstName = nameParts[0] || '';
      const lastName = nameParts.slice(1).join(' ') || '';

      console.log('Calling inviteUserMutation with:', {
        email: newUser.email,
        firstName,
        lastName,
        roleId: roleObj._id,
        areaIds,
        shopId,
        workosUserId,
      });

      await inviteUserMutation({
        email: newUser.email,
        firstName,
        lastName,
        roleId: roleObj._id,
        areaIds,
        shopId,
        workosUserId,
      });

      // Store the invited user details for the success dialog
      setInvitedUserDetails({
        name: newUser.name,
        email: newUser.email,
        role: newUser.role
      });

      // Close invite dialog and open success dialog
      setIsInviteDialogOpen(false);
      setIsSuccessDialogOpen(true);

      // Reset form
      setNewUser({
        name: '',
        email: '',
        role: '',
        areaId: '',
        shopId: '',
        areas: []
      });

      toast({
        title: "Success",
        description: "User invitation sent successfully!",
      });
    } catch (error: any) {
      console.error('Error inviting user:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to invite user",
        variant: "destructive",
      });
    } finally {
      setIsInviting(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser || !workosUserId) {
      toast({
        title: "Error",
        description: "User data or authentication not available",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsInviting(true);

      await deleteUserMutation({
        userId: selectedUser.id,
        workosUserId,
      });

      toast({
        title: "Success",
        description: `User ${selectedUser.name} has been deleted successfully`,
      });

      setIsDeleteDialogOpen(false);
      setSelectedUser(null);
    } catch (error: any) {
      console.error('Error deleting user:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to delete user",
        variant: "destructive",
      });
    } finally {
      setIsInviting(false);
    }
  };

  // roleLabels is already defined above

  return (
    <Layout>
      <PageTransition>
        <div className="space-y-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">User Management</h1>
              <p className="text-muted-foreground mt-1">
                Manage users and their access to the system
              </p>
            </div>

            <div className="flex gap-3">
              <Dialog open={isInviteDialogOpen} onOpenChange={setIsInviteDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <PlusCircle className="mr-2 h-4 w-4" />
                    Invite User
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Invite New User</DialogTitle>
                    <DialogDescription>
                      Send an invitation to a new user to join the system
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="name">Name</Label>
                      <Input
                        id="name"
                        placeholder="Enter user's name"
                        value={newUser.name}
                        onChange={(e) => setNewUser({...newUser, name: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="Enter user's email"
                        value={newUser.email}
                        onChange={(e) => setNewUser({...newUser, email: e.target.value})}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="role">Role</Label>
                      <Select
                        value={newUser.role}
                        onValueChange={(value) => setNewUser({...newUser, role: value})}
                        disabled={isLoading || !roles || !Array.isArray(roles) || roles.length === 0}
                      >
                        <SelectTrigger id="role">
                          <SelectValue placeholder={isLoading ? "Loading roles..." : "Select role"} />
                        </SelectTrigger>
                        <SelectContent>
                          {roles && Array.isArray(roles) && roles.length > 0 && roles.map((role) => (
                            <SelectItem key={role._id} value={role.name}>
                              {roleLabels[role.name] || role.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {newUser.role === 'shop_manager' && (
                      <>
                        <div className="space-y-2">
                          <Label htmlFor="area">Assigned Area</Label>
                          <Select
                            value={newUser.areaId}
                            onValueChange={(value) => setNewUser({...newUser, areaId: value, shopId: ''})} // Reset shop when area changes
                            disabled={areasLoading}
                          >
                            <SelectTrigger id="area">
                              <SelectValue placeholder={areasLoading ? "Loading areas..." : "Select area first"} />
                            </SelectTrigger>
                            <SelectContent>
                              {areas && areas.length > 0 ? (
                                areas.map((area) => (
                                  <SelectItem key={area._id} value={area._id}>
                                    {area.name}
                                  </SelectItem>
                                ))
                              ) : (
                                !areasLoading && (
                                  <div className="p-2 text-sm text-muted-foreground">
                                    No areas available
                                  </div>
                                )
                              )}
                            </SelectContent>
                          </Select>
                          <p className="text-sm text-muted-foreground">
                            Select the area where the shop manager will work
                          </p>
                        </div>
                        {newUser.areaId && (
                          <div className="space-y-2">
                            <Label htmlFor="shop">Assigned Shop</Label>
                            <Select
                              value={newUser.shopId}
                              onValueChange={(value) => setNewUser({...newUser, shopId: value})}
                            >
                              <SelectTrigger id="shop">
                                <SelectValue placeholder="Select shop" />
                              </SelectTrigger>
                              <SelectContent>
                                {shops && shops.length > 0 ? (
                                  shops
                                    .filter(shop => shop.areaId === newUser.areaId)
                                    .map((shop) => (
                                      <SelectItem key={shop._id} value={shop._id}>
                                        {shop.name} - {shop.address}
                                      </SelectItem>
                                    ))
                                ) : null}
                              </SelectContent>
                            </Select>
                            <p className="text-sm text-muted-foreground">
                              Select the specific shop within the selected area
                            </p>
                          </div>
                        )}
                      </>
                    )}
                    {newUser.role === 'watcher' && (
                      <div className="space-y-2">
                        <Label htmlFor="areas">Assigned Areas</Label>
                        <MultiSelect
                          selected={newUser.areas}
                          options={areas.map(area => ({
                            value: area._id,
                            label: area.name
                          }))}
                          onChange={(values) => setNewUser({...newUser, areas: values})}
                          placeholder="Select areas"
                        />
                        <p className="text-sm text-muted-foreground">
                          Select one or more areas for the watcher to monitor
                        </p>
                      </div>
                    )}
                  </div>
                  <DialogFooter>
                    <Button variant="outline" onClick={() => setIsInviteDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleInviteUser} disabled={isInviting || isLoading || !roles || !Array.isArray(roles) || roles.length === 0 || !workosUserId}>
                      {isInviting ? 'Inviting...' : isLoading ? 'Loading...' : !workosUserId ? 'Authenticating...' : 'Send Invitation'}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>

          <div className="space-y-6">
            {/* Active Users */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-600" />
                  Active Users ({users.length})
                </CardTitle>
                <CardDescription>
                  Users who have completed registration and are active in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading users...</span>
                  </div>
                ) : users.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No active users found
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Areas</TableHead>
                        <TableHead>Assigned Shop</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {users.map((user) => (
                        <TableRow key={user._id}>
                          <TableCell className="font-medium">
                            {user.firstName} {user.lastName}
                          </TableCell>
                          <TableCell>{user.email}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {roleLabels[user.roles?.[0]?.name] || user.roles?.[0]?.name || 'No Role'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Active
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {user.areas && user.areas.length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {user.areas.map((area: any) => (
                                  <Badge key={area._id} variant="outline" className="text-xs">
                                    {area.name}
                                  </Badge>
                                ))}
                              </div>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                          <TableCell>
                            {user.roles?.[0]?.name === 'shop_manager' ? (
                              (() => {
                                const userShop = shops.find(shop => shop.managerId === user._id);
                                return userShop ? (
                                  <Badge variant="default" className="bg-blue-100 text-blue-800">
                                    {userShop.name}
                                  </Badge>
                                ) : (
                                  <span className="text-muted-foreground">No shop assigned</span>
                                );
                              })()
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  // For shop managers, try to get the area and shop from their assignments
                                  let areaId = '';
                                  let shopId = null;
                                  
                                  console.log('Edit button clicked for user:', user._id, user.email);
                                  console.log('User role:', user.roles?.[0]?.name);
                                  console.log('Available shops:', shops);
                                  
                                  if (user.roles?.[0]?.name === 'shop_manager') {
                                    // Find the shop this user manages
                                    const userShop = shops.find(shop => shop.managerId === user._id);
                                    console.log('Found user shop:', userShop);
                                    
                                    if (userShop) {
                                      areaId = userShop.areaId || '';
                                      shopId = userShop.id;
                                      console.log('Setting areaId:', areaId, 'shopId:', shopId);
                                    } else {
                                      console.log('No shop found for user:', user._id);
                                    }
                                  }
                                  
                                  const selectedUserData = {
                                    id: user._id,
                                    name: `${user.firstName} ${user.lastName}`,
                                    email: user.email,
                                    role: user.roles?.[0]?.name || '',
                                    areaId,
                                    shop: shopId,
                                    areas: user.areas?.map((area: any) => area._id) || []
                                  };
                                  
                                  console.log('Setting selectedUser:', selectedUserData);
                                  setSelectedUser(selectedUserData);
                                  setIsEditDialogOpen(true);
                                }}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => {
                                  // For shop managers, try to get the area and shop from their assignments
                                  let areaId = '';
                                  let shopId = null;
                                  
                                  if (user.roles?.[0]?.name === 'shop_manager') {
                                    // Find the shop this user manages
                                    const userShop = shops.find(shop => shop.managerId === user._id);
                                    if (userShop) {
                                      areaId = userShop.areaId || '';
                                      shopId = userShop.id;
                                    }
                                  }
                                  
                                  setSelectedUser({
                                    id: user._id,
                                    name: `${user.firstName} ${user.lastName}`,
                                    email: user.email,
                                    role: user.roles?.[0]?.name || '',
                                    areaId,
                                    shop: shopId,
                                    areas: user.areas?.map((area: any) => area._id) || []
                                  });
                                  setIsDeleteDialogOpen(true);
                                }}
                              >
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>

            {/* Pending Invitations */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-yellow-600" />
                  Pending Invitations ({invitations.length})
                </CardTitle>
                <CardDescription>
                  Users who have been invited but haven't completed registration yet
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-6 w-6 animate-spin" />
                    <span className="ml-2">Loading invitations...</span>
                  </div>
                ) : invitations.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    No pending invitations
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Email</TableHead>
                        <TableHead>Role</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead>Invited By</TableHead>
                        <TableHead>Invited Date</TableHead>
                        <TableHead>Expires</TableHead>
                        <TableHead>Areas</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {invitations.map((invitation) => (
                        <TableRow key={invitation._id}>
                          <TableCell className="font-medium">{invitation.email}</TableCell>
                          <TableCell>
                            <Badge variant="secondary">
                              {roleLabels[invitation.role?.name] || invitation.role?.name || 'Unknown Role'}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={invitation.status === 'pending' ? 'default' : 
                                     invitation.status === 'failed' ? 'destructive' : 'secondary'}
                              className={
                                invitation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                invitation.status === 'failed' ? 'bg-red-100 text-red-800' :
                                'bg-gray-100 text-gray-800'
                              }
                            >
                              {invitation.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                              {invitation.status === 'failed' && <XCircle className="h-3 w-3 mr-1" />}
                              {invitation.status.charAt(0).toUpperCase() + invitation.status.slice(1)}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            {invitation.invitedBy ? 
                              `${invitation.invitedBy.firstName} ${invitation.invitedBy.lastName}` : 
                              'Unknown'
                            }
                          </TableCell>
                          <TableCell>
                            {new Date(invitation.invitedAt).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {new Date(invitation.expiresAt).toLocaleDateString()}
                          </TableCell>
                          <TableCell>
                            {invitation.areas && invitation.areas.length > 0 ? (
                              <div className="flex flex-wrap gap-1">
                                {invitation.areas.map((area: any) => (
                                  <Badge key={area._id} variant="outline" className="text-xs">
                                    {area.name}
                                  </Badge>
                                ))}
                              </div>
                            ) : (
                              <span className="text-muted-foreground">-</span>
                            )}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={async () => {
                                  if (!workosUserId) {
                                    toast({
                                      title: "Error",
                                      description: "Authentication not available",
                                      variant: "destructive",
                                    });
                                    return;
                                  }

                                  try {
                                    await resendInvitationMutation({
                                      invitationId: invitation._id,
                                      workosUserId,
                                    });

                                    toast({
                                      title: "Success",
                                      description: `Invitation resent to ${invitation.email}`,
                                    });
                                  } catch (error: any) {
                                    console.error('Error resending invitation:', error);
                                    toast({
                                      title: "Error",
                                      description: error.message || "Failed to resend invitation",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                title="Resend invitation"
                              >
                                <RefreshCw className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={async () => {
                                  if (!workosUserId) {
                                    toast({
                                      title: "Error",
                                      description: "Authentication not available",
                                      variant: "destructive",
                                    });
                                    return;
                                  }

                                  try {
                                    await cancelInvitationMutation({
                                      invitationId: invitation._id,
                                      workosUserId,
                                    });

                                    toast({
                                      title: "Success",
                                      description: `Invitation cancelled for ${invitation.email}`,
                                    });
                                  } catch (error: any) {
                                    console.error('Error cancelling invitation:', error);
                                    toast({
                                      title: "Error",
                                      description: error.message || "Failed to cancel invitation",
                                      variant: "destructive",
                                    });
                                  }
                                }}
                                title="Cancel invitation"
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Edit Dialog */}
          <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Edit User</DialogTitle>
                <DialogDescription>
                  Make changes to the user's information
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-name">Name</Label>
                  <Input
                    id="edit-name"
                    value={selectedUser?.name || ''}
                    onChange={(e) => setSelectedUser(prev => prev ? {...prev, name: e.target.value} : null)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-email">Email</Label>
                  <Input
                    id="edit-email"
                    type="email"
                    value={selectedUser?.email || ''}
                    onChange={(e) => setSelectedUser(prev => prev ? {...prev, email: e.target.value} : null)}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="edit-role">Role</Label>
                  <Select
                    value={selectedUser?.role || ''}
                    onValueChange={(value) => setSelectedUser(prev => prev ? {...prev, role: value} : null)}
                  >
                    <SelectTrigger id="edit-role">
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="shop_manager">Shop Manager</SelectItem>
                      <SelectItem value="shop_support">Shop Support</SelectItem>
                      <SelectItem value="accounts">Accounts</SelectItem>
                      <SelectItem value="watcher">Watcher</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                {selectedUser?.role === 'shop_manager' && (
                  <>
                    <div className="space-y-2">
                      <Label htmlFor="edit-area">Assigned Area</Label>
                      <Select
                        value={selectedUser?.areaId || ''}
                        onValueChange={(value) => setSelectedUser(prev => prev ? {...prev, areaId: value, shop: ''} : null)} // Reset shop when area changes
                        disabled={areasLoading}
                      >
                        <SelectTrigger id="edit-area">
                          <SelectValue placeholder={areasLoading ? "Loading areas..." : "Select area first"} />
                        </SelectTrigger>
                        <SelectContent>
                          {areas && areas.length > 0 ? (
                            areas.map((area) => (
                              <SelectItem key={area._id} value={area._id}>
                                {area.name}
                              </SelectItem>
                            ))
                          ) : (
                            !areasLoading && (
                              <div className="p-2 text-sm text-muted-foreground">
                                No areas available
                              </div>
                            )
                          )}
                        </SelectContent>
                      </Select>
                      <p className="text-sm text-muted-foreground">
                        Select the area where the shop manager will work
                      </p>
                    </div>
                    {selectedUser?.areaId && (
                      <div className="space-y-2">
                        <Label htmlFor="edit-shop">Assigned Shop</Label>
                        <Select
                          value={selectedUser?.shop || ''}
                          onValueChange={(value) => setSelectedUser(prev => prev ? {...prev, shop: value} : null)}
                        >
                          <SelectTrigger id="edit-shop">
                            <SelectValue placeholder="Select shop" />
                          </SelectTrigger>
                          <SelectContent>
                            {shops && shops.length > 0 ? (
                              shops
                                .filter(shop => shop.areaId === selectedUser.areaId)
                                .map((shop) => (
                                  <SelectItem key={shop._id} value={shop._id}>
                                    {shop.name} - {shop.address}
                                  </SelectItem>
                                ))
                            ) : null}
                          </SelectContent>
                        </Select>
                        <p className="text-sm text-muted-foreground">
                          Select the specific shop within the selected area
                        </p>
                      </div>
                    )}
                  </>
                )}
                {selectedUser?.role === 'watcher' && (
                  <div className="space-y-2">
                    <Label htmlFor="edit-areas">Assigned Areas</Label>
                    <MultiSelect
                      selected={selectedUser?.areas || []}
                      options={areas.map(area => ({
                        value: area._id,
                        label: area.name
                      }))}
                      onChange={(values) => setSelectedUser(prev => prev ? {...prev, areas: values} : null)}
                      placeholder="Select areas"
                    />
                  </div>
                )}
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditUser}>
                  Save Changes
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Delete Dialog */}
          <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Delete User</DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete {selectedUser?.name}? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteUser}
                >
                  Delete User
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>

          {/* Success Dialog */}
          <Dialog open={isSuccessDialogOpen} onOpenChange={setIsSuccessDialogOpen}>
            <DialogContent className="sm:max-w-md">
              <div className="text-center p-6">
                <div className="text-green-600 text-5xl mb-4">✓</div>
                <DialogTitle className="text-xl font-semibold mb-2">User Invited Successfully!</DialogTitle>
                <DialogDescription className="mb-4">
                  An invitation has been sent to:<br/>
                  <span className="font-semibold">{invitedUserDetails.name}</span><br/>
                  ({invitedUserDetails.email})<br/>
                  Role: {roleLabels[invitedUserDetails.role]}
                </DialogDescription>
                <p className="text-sm text-gray-600">
                  The user will receive an email with instructions to set up their account.
                </p>
              </div>
              <DialogFooter>
                <Button onClick={() => setIsSuccessDialogOpen(false)}>
                  OK
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </PageTransition>
    </Layout>
  );
};

export default UserManagement;
