import React, { useState, useEffect, useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/lib/auth-context';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useSettings } from '@/hooks/useSettings';
import { PaymentMethod, Request } from '@/lib/types';
import { Loader2, AlertCircle, Cloud, X, ChevronRight, ChevronLeft, Info, FileText } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useNavigate } from 'react-router-dom';
import { toast } from '@/components/ui/use-toast';
import { Checkbox } from '@/components/ui/checkbox';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from "@/lib/utils";
import { useAuthenticatedMyShopsQuery, useAuthenticatedMyAreasQuery } from '@/lib/authenticated-hooks';

const PRODUCTS = [
  { id: '1', name: 'Sportbook' },
  { id: '2', name: 'Golden Race' },
  { id: '3', name: 'IBet' },
];

const NETWORKS = [
  'MTN',
  'Telecel',
  'AT',
];

const BANKS = [
  "STANDARD CHARTERED BANK",
  "ABSA BANK GHANA LIMITED",
  "GCB BANK LIMITED",
  "NATIONAL INVESTMENT BANK",
  "ARB APEX BANK LIMITED",
  "AGRICULTURAL DEVELOPMENT BANK",
  "UNIVERSAL MERCHANT BANK",
  "REPUBLIC BANK LIMITED",
  "ZENITH BANK GHANA LTD",
  "ECOBANK GHANA LTD",
  "CAL BANK LIMITED",
  "FIRST ATLANTIC BANK",
  "PRUDENTIAL BANK LTD",
  "STANBIC BANK",
  "FIRST BANK OF NIGERIA",
  "BANK OF AFRICA",
  "GUARANTY TRUST BANK",
  "FIDELITY BANK LIMITED",
  "SAHEL - SAHARA BANK (BSIC)",
  "UNITED BANK OF AFRICA",
  "ACCESS BANK LTD",
  "CONSOLIDATED BANK GHANA",
  "FIRST NATIONAL BANK",
  "GHL BANK",
];

interface RequestFormProps {
  existingRequest?: Request;
  isResubmission?: boolean;
}

const RequestForm: React.FC<RequestFormProps> = ({
  existingRequest,
  isResubmission = false,
}) => {
  const { userWithRoles, user: workosUser } = useAuth();
  const navigate = useNavigate();

  // Get settings using the custom hook with proper fallbacks
  const {
    mobileMoneyThreshold: momoThreshold,
    bankTransferThreshold: bankThreshold,
    maxTicketImageSize: maxFileSize,
    isLoading: settingsLoading
  } = useSettings();
  
  // Use Convex mutations for actions
  const createRequestMutation = useMutation(api.requests.createRequest);
  const resubmitRequestMutation = useMutation(api.requests.resubmitRequest);
  const generateUploadUrl = useMutation(api.files.generateUploadUrl);
  // Remove unused getImageUrl mutation
  // Note: updateRequest doesn't exist - requests can only be resubmitted, not updated

  // Use role-specific hooks for shop managers
  const myShopsQueryResult = useAuthenticatedMyShopsQuery();
  const myAreasQueryResult = useAuthenticatedMyAreasQuery();
  
  // Extract the actual data from the query results
  const myShopsQuery = myShopsQueryResult?.data;
  const myAreasQuery = myAreasQueryResult?.data;

  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 5; // Increased to add preview step

  const [areaId, setAreaId] = useState(existingRequest?.areaId || '');
  const [shopId, setShopId] = useState(existingRequest?.shopId || '');

  // Title field removed - using Ticket ID for identification
  const [ticketId, setTicketId] = useState(existingRequest?.ticketNumber || '');
  const [productId, setProductId] = useState(existingRequest?.productId || '1'); // Default to Sportbook if not specified
  const [amount, setAmount] = useState(existingRequest?.amount?.toString() || '');
  const [comments, setComments] = useState(existingRequest?.notes || '');

  const [image, setImage] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [imageName, setImageName] = useState('');
  const [uploadedImageId, setUploadedImageId] = useState<string>(existingRequest?.ticketImageId || '');

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>(existingRequest?.paymentMethod || 'mobile_money');

  const [customerName, setCustomerName] = useState(existingRequest?.customerName || '');
  const [customerContact, setCustomerContact] = useState(existingRequest?.customerPhone || existingRequest?.mobileMoneyNumber || '');
  const [networkProvider, setNetworkProvider] = useState(existingRequest?.mobileMoneyProvider || '');

  const [bankName, setBankName] = useState(existingRequest?.bankName || '');
  const [accountNumber, setAccountNumber] = useState(existingRequest?.accountNumber || '');
  const [accountHolderName, setAccountHolderName] = useState(existingRequest?.accountName || '');

  const [customerAgreement, setCustomerAgreement] = useState(false);
  const [officerConsent, setOfficerConsent] = useState(false);

  const [customerConfirmationStep1, setCustomerConfirmationStep1] = useState(false);
  const [officerConsentStep1, setOfficerConsentStep1] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [formError, setFormError] = useState<string | null>(null);
  const [changeNotes, setChangeNotes] = useState('');
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  // Determine if user is shop manager
  const isShopManager = userWithRoles?.roles?.some((role: any) => role.name === 'shop_manager') || false;

  // Get areas and shops based on user role - always use authenticated hooks
  const availableAreas = myAreasQueryResult?.data || [];
  const availableShops = myShopsQueryResult?.data || [];

  // Check loading states - use authenticated hooks
  const isAreasLoading = myAreasQueryResult?.isLoading || false;
  const isShopsLoading = myShopsQueryResult?.isLoading || false;

  // Check if we have valid data
  const hasAreasData = Boolean(myAreasQueryResult?.data && myAreasQueryResult.data.length > 0);
  const hasShopsData = isShopManager ? Boolean(myShopsQueryResult?.data && myShopsQueryResult.data.length > 0) : true;

  // Determine if we're ready to pre-populate
  const isReadyForPrePopulation =
    isShopManager &&
    !isAreasLoading &&
    !isShopsLoading &&
    hasAreasData &&
    hasShopsData &&
    !existingRequest;

  console.log('=== RequestForm Pre-population State ===');
  console.log('Role check:', { isShopManager, roles: userWithRoles?.roles?.map(r => r.name) });
  console.log('Loading states:', { isAreasLoading, isShopsLoading });
  console.log('Data availability:', { hasAreasData, hasShopsData });
  console.log('Available data counts:', { areas: availableAreas.length, shops: availableShops.length });
  console.log('Ready for pre-population:', isReadyForPrePopulation);
  console.log('Current form state:', { areaId, shopId });
  console.log('========================================');

  // Auto-populate area and shop for shop managers - improved logic
  useEffect(() => {
    console.log('[PRE-POPULATE] useEffect triggered');
    if (isReadyForPrePopulation) {
      if (!areaId && availableAreas.length > 0) {
        const firstArea = availableAreas[0];
        console.log('[PRE-POPULATE] Setting area:', firstArea._id, firstArea.name);
        setAreaId(firstArea._id);
      }
      if (!shopId && availableShops.length > 0) {
        const firstShop = availableShops[0];
        console.log('[PRE-POPULATE] Setting shop:', firstShop._id, firstShop.name);
        setShopId(firstShop._id);
      }
    } else {
      console.log('[PRE-POPULATE] Not ready for pre-population');
    }
  }, [isReadyForPrePopulation, areaId, shopId, availableAreas, availableShops]);

  // Auto-select area when shop is selected for shop managers
  useEffect(() => {
    if (isShopManager && shopId && availableShops.length > 0) {
      const selectedShop = availableShops.find(shop => shop._id === shopId);
      if (selectedShop && selectedShop.areaId !== areaId) {
        console.log('[PRE-POPULATE] Auto-selecting area for shop:', selectedShop.areaId);
        setAreaId(selectedShop.areaId);
      }
    }
  }, [isShopManager, shopId, availableShops, areaId]);
    
  // Data fetching is now handled by authenticated hooks automatically

  // Auto-select area when shop is selected for shop managers
  useEffect(() => {
    if (isShopManager && shopId && myShopsQuery) {
      const selectedShop = myShopsQuery.find(shop => shop._id === shopId);
      if (selectedShop && selectedShop.areaId !== areaId) {
        setAreaId(selectedShop.areaId);
      }
    }
  }, [isShopManager, shopId, myShopsQuery, areaId]);

  // Load existing image for resubmission
  useEffect(() => {
    if (existingRequest?.ticketImageId && !imagePreview) {
      // For existing requests, we'll show a placeholder and set the uploaded ID
      setImagePreview('/placeholder-image.svg'); // We'll use a placeholder for now
      setImageName('Existing ticket image');
      setUploadedImageId(existingRequest.ticketImageId);
    }
  }, [existingRequest?.ticketImageId, imagePreview]);

  const MAX_FILE_SIZE = maxFileSize; // Dynamic from settings
  const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];

  const handleImageChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!ALLOWED_TYPES.includes(file.type)) {
      toast({
        variant: "destructive",
        title: "Invalid file type",
        description: "Please upload a JPG, PNG or WebP image"
      });
      return;
    }

    if (file.size > MAX_FILE_SIZE) {
      toast({
        variant: "destructive",
        title: "File too large",
        description: "Image must be less than 5MB"
      });
      return;
    }

    try {
      setIsUploading(true);
      setUploadProgress(10);

      // Compress the image first
      const compressedFile = await compressImage(file);
      setUploadProgress(30);

      // Generate upload URL
      const uploadUrl = await generateUploadUrl({
        workosUserId: workosUser?.id
      });
      setUploadProgress(50);

      // Upload to Convex storage
      const result = await fetch(uploadUrl, {
        method: "POST",
        headers: { "Content-Type": compressedFile.type },
        body: compressedFile,
      });

      if (!result.ok) {
        throw new Error("Failed to upload image");
      }

      const { storageId } = await result.json();
      setUploadProgress(90);

      // Set the uploaded image data
      setUploadedImageId(storageId);
      setImage(compressedFile);
      setImageName(file.name);

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result as string);
        setUploadProgress(100);
      };
      reader.readAsDataURL(compressedFile);

      toast({
        title: "Image uploaded",
        description: "Ticket image uploaded successfully",
      });

    } catch (error) {
      console.error('Image upload error:', error);
      toast({
        variant: "destructive",
        title: "Upload failed",
        description: "Failed to upload image. Please try again."
      });
    } finally {
      // Reset after a short delay to show the completed progress
      setTimeout(() => {
        setIsUploading(false);
        setUploadProgress(0);
      }, 1000);
    }
  };

  const handleRemoveImage = () => {
    setImage(null);
    setImagePreview('');
    setImageName('');
    setUploadedImageId('');
  };

  const nextStep = () => {
    if (validateCurrentStep()) {
      setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
    }
  };

  const prevStep = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 1));
  };

  const validateCurrentStep = () => {
    setFormError(null);

    switch (currentStep) {
      case 1:
        if (!areaId?.trim()) {
          setFormError('Please select an area');
          return false;
        }

        if (!shopId?.trim()) {
          setFormError('Please select a shop');
          return false;
        }

        const selectedShop = availableShops.find(shop => shop._id === shopId);
        if (!selectedShop) {
          setFormError('Please select a valid shop');
          return false;
        }

        if (selectedShop.areaId !== areaId) {
          setFormError('Selected shop does not belong to the chosen area');
          return false;
        }

        return true;

      case 2:
        // Title validation removed - auto-generated from ticket ID
        if (!ticketId) {
          setFormError('Please enter a ticket ID');
          return false;
        }
        if (!productId) {
          setFormError('Please select a product');
          return false;
        }
        if (!amount || isNaN(parseFloat(amount)) || parseFloat(amount) <= 0) {
          setFormError('Please enter a valid amount');
          return false;
        }
        if (!existingRequest && !uploadedImageId && !imagePreview) {
          setFormError('Please upload a ticket image');
          return false;
        }
        return true;

      case 3:
        if (!paymentMethod) {
          setFormError('Please select a payment method');
          return false;
        }

        if (paymentMethod === 'mobile_money') {
          if (parseFloat(amount) > momoThreshold) {
            setFormError(`Mobile Money payments are limited to ¢${momoThreshold.toLocaleString()}. Please use Bank Transfer.`);
            return false;
          }
          if (!customerName) {
            setFormError('Please enter customer name');
            return false;
          }
          if (!customerContact) {
            setFormError('Please enter customer MoMo number');
            return false;
          }
          if (!networkProvider) {
            setFormError('Please select a network provider');
            return false;
          }
        } else if (paymentMethod === 'bank_transfer') {
          if (!bankName) {
            setFormError('Please select a bank');
            return false;
          }
          if (!accountNumber) {
            setFormError('Please enter account number');
            return false;
          }
          if (!accountHolderName) {
            setFormError('Please enter account holder name');
            return false;
          }
        }
        return true;

      case 4:
        return true;

      case 5:
        if (isResubmission && !changeNotes.trim()) {
          setFormError('Please explain what changes you made');
          return false;
        }
        if (customerAgreement && officerConsent) {
          setFormError(null);
          return true;
        }
        if (!customerAgreement) {
          setFormError('Please confirm customer agreement');
          return false;
        }
        if (!officerConsent) {
          setFormError('Please confirm officer consent');
          return false;
        }
        return true;

      default:
        return true;
    }
  };

  const validateForm = () => {
    const originalStep = currentStep;
    let isValid = true;

    setCurrentStep(1);
    if (!validateCurrentStep()) {
      isValid = false;
    }

    setCurrentStep(2);
    if (!validateCurrentStep()) {
      isValid = false;
    }

    setCurrentStep(3);
    if (!validateCurrentStep()) {
      isValid = false;
    }

    setCurrentStep(4);
    if (!validateCurrentStep()) {
      isValid = false;
    }

    setCurrentStep(5);
    if (!validateCurrentStep()) {
      isValid = false;
    }

    setCurrentStep(originalStep);

    return isValid;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setFormError(null);

    try {
      const numAmount = parseFloat(amount);
      const selectedShop = availableShops.find(s => s._id === shopId);

      const requestData = {
        title: `Request #${ticketId}`, // Generate title from ticket ID
        ticketId,
        productId,
        areaId,
        shopId,
        shopName: selectedShop?.name || '',
        amount: numAmount,
        paymentMethod,
        customerName,
        customerContact,
        networkProvider: paymentMethod === 'mobile_money' ? networkProvider : undefined,
        bankName: paymentMethod === 'bank_transfer' ? bankName : undefined,
        accountNumber: paymentMethod === 'bank_transfer' ? accountNumber : undefined,
        accountHolderName: paymentMethod === 'bank_transfer' ? accountHolderName : undefined,
        comments,
        ticketImage: uploadedImageId,
        changeNotes: isResubmission ? changeNotes : undefined,
      };

      if (isResubmission && existingRequest) {
        // For resubmission, ensure we use valid IDs (preserve original if current is empty)
        const resubmissionData = {
          ...requestData,
          areaId: areaId || existingRequest.areaId,
          shopId: shopId || existingRequest.shopId,
        };

        await resubmitRequestMutation({
          requestId: existingRequest._id,
          ...resubmissionData,
          workosUserId: workosUser?.id || "",
          changeNotes: changeNotes
        });

        toast({
          title: "Request resubmitted",
          description: "Your cash request has been resubmitted for approval",
        });
      } else if (existingRequest) {
        // Note: Direct updates are not supported - only resubmission
        throw new Error('Direct request updates are not supported. Please use resubmission instead.');
      } else {
        if (!workosUser) {
          throw new Error('User not authenticated');
        }

        const result = await createRequestMutation({
          ...requestData,
          workosUserId: workosUser.id,
        });

        if (result) {
          toast({
            title: "Request created",
            description: "Your cash request has been submitted for approval",
          });
          navigate('/requests');
          return;
        }
      }

      navigate('/requests');
    } catch (error) {
      console.error('Error submitting request:', error);

      if (error instanceof Error && error.message.includes('ticket ID')) {
        setFormError(error.message);
        toast({
          variant: "destructive",
          title: "Duplicate Ticket ID",
          description: error.message,
        });
      } else {
        setFormError('Failed to submit request. Please try again.');
        toast({
          variant: "destructive",
          title: "Error",
          description: "There was a problem submitting your request.",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const areaShops = useMemo(() => {
    return availableShops.filter(shop => shop.areaId === areaId);
  }, [availableShops, areaId]);

  useEffect(() => {
    setShopId('');
  }, [areaId]);

  const handleShopChange = (value: string) => {
    setShopId(value);
    if (formError === 'Please select a shop') {
      setFormError(null);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="area">Area</Label>
              <Select
                value={areaId}
                onValueChange={(value) => {
                  setAreaId(value);
                  if (formError === 'Please select an area') {
                    setFormError(null);
                  }
                }}
                disabled={isLoading || isAreasLoading || (isShopManager && !existingRequest)}
              >
                <SelectTrigger>
                  <SelectValue placeholder={isShopManager ? "Your assigned area" : "Select area"} />
                </SelectTrigger>
                <SelectContent>
                  {availableAreas.map((area) => (
                    <SelectItem key={area._id} value={area._id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {isShopManager && isAreasLoading && (
                <p className="text-sm text-muted-foreground mt-1">
                  Loading your assigned areas...
                </p>
              )}
              {isShopManager && !isAreasLoading && availableAreas.length <= 1 && (
                <p className="text-sm text-muted-foreground mt-1">
                  This is your assigned area
                </p>
              )}
              {isShopManager && !isAreasLoading && availableAreas.length === 0 && (
                <p className="text-sm text-destructive mt-1">
                  No areas assigned to you. Please contact an administrator.
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="shop">Shop</Label>
              <Select
                value={shopId}
                onValueChange={handleShopChange}
                disabled={isLoading || isShopsLoading || !areaId || (isShopManager && !existingRequest)}
              >
                <SelectTrigger className={formError === 'Please select a shop' ? 'border-destructive' : ''}>
                  <SelectValue placeholder={
                    !areaId ? "Select an area first" : 
                    isShopManager ? "Your assigned shop" : 
                    "Select shop"
                  } />
                </SelectTrigger>
                <SelectContent>
                  {areaShops.map((shop) => (
                    <SelectItem key={shop._id} value={shop._id}>
                      {shop.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {!areaId && !isShopManager && (
                <p className="text-sm text-muted-foreground mt-1">
                  Please select an area to view available shops
                </p>
              )}
              {areaId && areaShops.length === 0 && !isShopManager && (
                <p className="text-sm text-muted-foreground mt-1">
                  No shops available in this area
                </p>
              )}
              {isShopManager && isShopsLoading && (
                <p className="text-sm text-muted-foreground mt-1">
                  Loading your assigned shops...
                </p>
              )}
              {isShopManager && !isShopsLoading && availableShops.length <= 1 && (
                <p className="text-sm text-muted-foreground mt-1">
                  This is your assigned shop
                </p>
              )}
              {isShopManager && !isShopsLoading && availableShops.length === 0 && (
                <p className="text-sm text-destructive mt-1">
                  No shops assigned to you. Please contact an administrator.
                </p>
              )}
              {formError === 'Please select a shop' && (
                <p className="text-sm text-destructive mt-1">
                  {formError}
                </p>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            {/* Title field removed - auto-generated from ticket ID */}

            <div className="space-y-2">
              <Label htmlFor="ticketId">Ticket ID</Label>
              <Input
                id="ticketId"
                placeholder="Enter ticket ID"
                value={ticketId}
                onChange={(e) => setTicketId(e.target.value)}
                disabled={isLoading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="product">Product</Label>
              <Select
                value={productId}
                onValueChange={setProductId}
                disabled={isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select product" />
                </SelectTrigger>
                <SelectContent>
                  {PRODUCTS.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">¢</span>
                <Input
                  id="amount"
                  type="number"
                  min="1"
                  step="any"
                  placeholder="0.00"
                  onKeyDown={(e) => {
                    if (e.key === '-' || e.key === 'e') {
                      e.preventDefault();
                    }
                  }}
                  onChange={handleAmountChange}
                  value={amount}
                  disabled={isLoading}
                  className="pl-7"
                />
              </div>
              {parseFloat(amount) > momoThreshold && (
                <p className="text-xs text-muted-foreground">
                  For amounts over ¢{momoThreshold.toLocaleString()}, bank transfer is required
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ticketImage" className="flex items-center gap-2">
                Ticket Image
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Info className="h-4 w-4 text-muted-foreground cursor-help" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Upload a clear image of the ticket (JPG, PNG or WebP)</p>
                      <p>Maximum size: 5MB</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </Label>

              <div
                className={cn(
                  "border-2 border-dashed rounded-lg p-6 transition-colors duration-200",
                  "hover:bg-muted/50 hover:border-primary/50",
                  "flex flex-col items-center justify-center gap-4",
                  "cursor-pointer relative"
                )}
                onDragOver={(e: React.DragEvent<HTMLDivElement>) => {
                  e.preventDefault();
                  e.stopPropagation();
                }}
                onDrop={(e: React.DragEvent<HTMLDivElement>) => {
                  e.preventDefault();
                  e.stopPropagation();
                  const file = e.dataTransfer.files?.[0];
                  if (file) {
                    const syntheticEvent = createSyntheticEvent(file);
                    handleImageChange(syntheticEvent);
                  }
                }}
              >
                {isUploading ? (
                  <div className="flex flex-col items-center py-4 w-full">
                    <Loader2 className="h-8 w-8 animate-spin text-primary mb-3" />
                    <div className="w-full max-w-xs bg-muted rounded-full h-2.5 mb-2">
                      <div
                        className="bg-primary h-2.5 rounded-full transition-all duration-300 ease-in-out"
                        style={{ width: `${uploadProgress}%` }}
                      ></div>
                    </div>
                    <span className="text-sm text-muted-foreground">{uploadProgress}% - Processing image...</span>
                  </div>
                ) : !imagePreview ? (
                  <>
                    <Input
                      id="ticketImage"
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      disabled={isLoading}
                      className="hidden"
                    />
                    <Label
                      htmlFor="ticketImage"
                      className="cursor-pointer flex flex-col items-center gap-2"
                    >
                      <div className="p-4 rounded-full bg-muted">
                        <Cloud className="h-8 w-8 text-primary" />
                      </div>
                      <div className="text-center">
                        <p className="text-sm font-medium">
                          Drag and drop your image here, or click to browse
                        </p>
                        <p className="text-xs text-muted-foreground mt-1">
                          Supports: JPG, PNG, WebP (max 5MB)
                        </p>
                      </div>
                    </Label>
                  </>
                ) : (
                  <div className="flex flex-col items-center w-full">
                    <div className="relative group max-w-md w-full">
                      <img
                        src={imagePreview}
                        alt="Ticket preview"
                        className="rounded-md w-full object-contain max-h-[200px]"
                      />
                      <div className="absolute inset-0 bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-md flex items-center justify-center">
                        <button
                          onClick={handleRemoveImage}
                          className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors"
                          type="button"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                    {imageName && (
                      <div className="mt-2 text-sm text-muted-foreground flex items-center gap-2">
                        <FileText className="h-4 w-4" />
                        <span className="truncate max-w-[180px]">{imageName}</span>
                      </div>
                    )}
                    <Button
                      variant="outline"
                      size="sm"
                      className="mt-4"
                      onClick={() => document.getElementById('ticketImage')?.click()}
                      type="button"
                    >
                      Choose Different Image
                    </Button>
                    <Input
                      id="ticketImage"
                      type="file"
                      accept="image/*"
                      onChange={handleImageChange}
                      disabled={isLoading}
                      className="hidden"
                    />
                  </div>
                )}
              </div>

              {imagePreview && (
                <p className="text-xs text-muted-foreground flex items-center gap-1">
                  <AlertCircle className="h-3 w-3" />
                  Recommended: Upload a clear, well-lit image of the ticket
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="comments">Additional Comments</Label>
              <Textarea
                id="comments"
                placeholder="Any additional information..."
                value={comments}
                onChange={(e) => setComments(e.target.value)}
                disabled={isLoading}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Payment Method</Label>
              <RadioGroup
                value={paymentMethod}
                onValueChange={(value) => setPaymentMethod(value as PaymentMethod)}
                disabled={isLoading || parseFloat(amount) > momoThreshold}
                className="flex flex-col space-y-2"
              >
                <div className="flex items-center space-x-2">
                  <RadioGroupItem
                    value="mobile_money"
                    id="mobile_money"
                    disabled={parseFloat(amount) > momoThreshold}
                  />
                  <Label htmlFor="mobile_money" className="font-normal">
                    Mobile Money
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="bank_transfer" id="bank_transfer" />
                  <Label htmlFor="bank_transfer" className="font-normal">Bank Transfer</Label>
                </div>
              </RadioGroup>
            </div>

            {paymentMethod === 'mobile_money' ? (
              <div className="space-y-4 border p-4 rounded-md">
                <div className="space-y-2">

                  <Label htmlFor="customerContact">Customer MoMo Number</Label>
                  <Input
                    id="customerContact"
                    placeholder="0XX XXX XXXX"
                    value={customerContact}
                    onChange={handleCustomerContactChange}
                    disabled={isLoading}
                    maxLength={10}
                    type="tel"
                    className={cn(
                      customerContactValidation?.isValid === false ? 'border-destructive' : '',
                      customerContactValidation?.isValid === true ? 'border-green-500' : ''
                    )}
                  />
                  {customerContactValidation && (
                    <p
                      className={cn(
                        "text-xs mt-1",
                        customerContactValidation.isValid ? "text-green-600" : "text-destructive"
                      )}
                    >
                      {customerContactValidation.message}
                    </p>
                  )}
                  {!customerContact && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Enter a 10-digit Ghana mobile number starting with 0
                    </p>
                  )}
    
                </div>

                <div className="space-y-2">
                  <Label htmlFor="customerName">Customer Full Name</Label>
                  <Input
                    id="customerName"
                    placeholder="Full name"
                    value={customerName}
                    onChange={(e) => {
                      const value = e.target.value.replace(/[^a-zA-Z\s]/g, '');
                      setCustomerName(value);
                    }}
                    disabled={isLoading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="networkProvider">Network Provider</Label>
                  <Select
                    value={networkProvider}
                    onValueChange={setNetworkProvider}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select network" />
                    </SelectTrigger>
                    <SelectContent>
                      {NETWORKS.map((network) => (
                        <SelectItem key={network} value={network}>
                          {network}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ) : (
              <div className="space-y-4 border p-4 rounded-md">
                <div className="space-y-2">
                  <Label htmlFor="bankName">Bank Name</Label>
                  <Select
                    value={bankName}
                    onValueChange={setBankName}
                    disabled={isLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select bank" />
                    </SelectTrigger>
                    <SelectContent>
                      {BANKS.map((bank, index) => (
                        <SelectItem key={index} value={bank}>
                          {bank}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accountNumber">Account Number</Label>
                  <Input
                    id="accountNumber"
                    placeholder="Enter account number"
                    value={accountNumber}
                    onChange={(e) => setAccountNumber(e.target.value)}
                    disabled={isLoading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="accountHolderName">Account Holder Name</Label>
                  <Input
                    id="accountHolderName"
                    placeholder="Enter account holder name"
                    value={accountHolderName}
                    onChange={(e) => setAccountHolderName(e.target.value)}
                    disabled={isLoading}
                  />
                </div>
              </div>
            )}
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="bg-muted/50 p-4 rounded-lg border">
              <h3 className="text-lg font-semibold mb-4">Request Preview</h3>
              <p className="text-sm text-muted-foreground mb-4">
                Please review your request details before submission. Make sure all information is correct.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Shop Information</h4>
                  <div className="bg-background p-3 rounded-md">
                    <p className="text-sm"><span className="font-medium">Area:</span> {availableAreas.find(a => a._id === areaId)?.name || 'Not selected'}</p>
                    <p className="text-sm"><span className="font-medium">Shop:</span> {availableShops.find(s => s._id === shopId)?.name || 'Not selected'}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Request Information</h4>
                  <div className="bg-background p-3 rounded-md">
                    <p className="text-sm"><span className="font-medium">Title:</span> Request #{ticketId}</p>
                    <p className="text-sm"><span className="font-medium">Ticket ID:</span> {ticketId}</p>
                    <p className="text-sm"><span className="font-medium">Product:</span> {PRODUCTS.find(p => p.id === productId)?.name || 'Not selected'}</p>
                    <p className="text-sm"><span className="font-medium">Amount:</span> ¢{parseFloat(amount).toLocaleString()}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Payment Information</h4>
                  <div className="bg-background p-3 rounded-md">
                    <p className="text-sm"><span className="font-medium">Payment Method:</span> {paymentMethod === 'mobile_money' ? 'Mobile Money' : 'Bank Transfer'}</p>
                    {paymentMethod === 'mobile_money' ? (
                      <>
                        <p className="text-sm"><span className="font-medium">Customer Name:</span> {customerName}</p>
                        <p className="text-sm"><span className="font-medium">Mobile Number:</span> {customerContact}</p>
                        <p className="text-sm"><span className="font-medium">Network:</span> {networkProvider || 'Not selected'}</p>
                      </>
                    ) : (
                      <>
                        <p className="text-sm"><span className="font-medium">Bank:</span> {bankName}</p>
                        <p className="text-sm"><span className="font-medium">Account Number:</span> {accountNumber}</p>
                        <p className="text-sm"><span className="font-medium">Account Holder:</span> {accountHolderName}</p>
                      </>
                    )}
                  </div>
                </div>

                {imagePreview && (
                  <div className="space-y-2">
                    <h4 className="text-sm(font-medium)">Ticket Image</h4>
                    <div className="bg-background p-3 rounded-md">
                      <img
                        src={imagePreview}
                        alt="Ticket preview"
                        className="max-h-[150px] object-contain mx-auto"
                      />
                    </div>
                  </div>
                )}
              </div>

              {comments && (
                <div className="mt-4">
                  <h4 className="text-sm font-medium">Additional Comments</h4>
                  <div className="bg-background p-3 rounded-md mt-2">
                    <p className="text-sm">{comments}</p>
                  </div>
                </div>
              )}
            </div>

            <div className="flex items-center p-4 border rounded-lg bg-amber-50 border-amber-200">
              <div className="mr-3 text-amber-500">
                <AlertCircle className="h-5 w-5" />
              </div>
              <div>
                <h4 className="text-sm font-semibold text-amber-800">Please verify all information</h4>
                <p className="text-xs text-amber-700 mt-1">
                  Once submitted, you may not be able to edit certain details. Make sure all information is accurate.
                </p>
              </div>
            </div>
          </div>
        );

      case 5:
        return (
          <div className="space-y-4">
            {isResubmission && (
              <div className="space-y-2">
                <Label htmlFor="changeNotes">
                  Changes Made <span className="text-destructive">*</span>
                </Label>
                <Textarea
                  id="changeNotes"
                  placeholder="Explain what changes you made to address the rejection reason..."
                  value={changeNotes}
                  onChange={(e) => setChangeNotes(e.target.value)}
                  disabled={isLoading}
                  className="min-h-[100px]"
                />
              </div>
            )}

            <div className="space-y-2">
              <Label>Confirmations</Label>
              <div className="space-y-4">
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="customerAgreement"
                    checked={customerAgreement}
                    onCheckedChange={(checked) => setCustomerAgreement(checked === true)}
                    disabled={isLoading}
                  />
                  <div>
                    <Label
                      htmlFor="customerAgreement"  
                      className="text-sm font-normal leading-tight cursor-pointer flex items-center"
                    >
                      I confirm that the customer has agreed to this payout request.
                      <span className="text-destructive ml-1">*</span>
                    </Label>
                    {!customerAgreement && formError === 'Please confirm customer agreement' && (
                      <p className="text-xs text-destructive mt-1">
                        Please confirm customer agreement
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-start space-x-2">
                  <Checkbox
                    id="officerConsent"
                    checked={officerConsent}
                    onCheckedChange={(checked) => {
                      setOfficerConsent(checked === true);
                      setFormError(null);
                    }}
                    disabled={isLoading}
                  />
                  <div>
                    <Label
                      htmlFor="officerConsent"  
                      className="text-sm font-normal leading-tight cursor-pointer flex items-center"
                    >
                      I confirm that the Assurance Officer has verified and approved this request.
                      <span className="text-destructive ml-1">*</span>
                    </Label>
                    {!officerConsent && formError === 'Please confirm officer consent' && (
                      <p className="text-xs text-destructive mt-1">
                        Please confirm officer consent
                      </p>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };


  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;

    const sanitizedValue = value.replace(/[^0-9.]/g, '');

    const parts = sanitizedValue.split('.');
    const formattedValue = parts.length > 2
      ? `${parts[0]}.${parts.slice(1).join('')}`
      : sanitizedValue;

    setAmount(formattedValue);

    if (formError === 'Please enter a valid amount') {
      setFormError(null);
    }
  };

  // Ghana mobile number validation
  const validateGhanaMobileNumber = (number: string): { isValid: boolean; message: string; network?: string } => {
    // Remove any non-digit characters
    const cleanNumber = number.replace(/[^0-9]/g, '');
    
    // Check if number starts with 0 and has 10 digits total
    if (!cleanNumber.startsWith('0') || cleanNumber.length !== 10) {
      return {
        isValid: false,
        message: 'Ghana mobile numbers must start with 0 and have 10 digits (e.g., 0241234567)'
      };
    }

    // Extract the prefix (first 3 digits)
    const prefix = cleanNumber.substring(0, 3);
    
    // Define Ghana network prefixes
    const networkPrefixes = {
      'MTN': ['024', '054', '055', '059'],
      'Telecel': ['020', '050'],
      'AT': ['026', '056', '027', '057']
    };

    // Check which network the prefix belongs to
    for (const [network, prefixes] of Object.entries(networkPrefixes)) {
      if (prefixes.includes(prefix)) {
        return {
          isValid: true,
          message: `Valid ${network} number`,
          network: network
        };
      }
    }

    return {
      isValid: false,
      message: `Invalid network prefix. Valid prefixes: MTN (024, 054, 055, 059), Telecel (020, 050), AT (026, 056, 027, 057)`
    };
  };

  const [customerContactValidation, setCustomerContactValidation] = useState<{
    isValid: boolean;
    message: string;
    network?: string;
  } | null>(null);

  const handleCustomerContactChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    setCustomerContact(value);
    
    // Validate in real-time if there's input
    if (value.length > 0) {
      const validation = validateGhanaMobileNumber(value);
      setCustomerContactValidation(validation);
      
      // Auto-select network provider if valid
      if (validation.isValid && validation.network && validation.network !== networkProvider) {
        setNetworkProvider(validation.network);
      }
    } else {
      setCustomerContactValidation(null);
    }
  };
    

  return (
    <Card className="w-full max-w-3xl mx-auto glass-panel animate-scale-in">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl">
          {isResubmission
            ? 'Resubmit Request'
            : existingRequest
              ? 'Edit Request'
              : 'New Cash Request'
          }
        </CardTitle>
        <CardDescription>
          {isResubmission
            ? 'Make corrections and resubmit your request for approval'
            : existingRequest
              ? 'Update your cash request'
              : 'Fill out the form to request cash for your shop'
          }
        </CardDescription>

        <div className="flex justify-between items-center pt-2">
          {Array.from({ length: totalSteps }).map((_, index) => (
            <div
              key={index}
              className={`h-1 rounded-full flex-1 mx-1 ${
                index + 1 <= currentStep ? 'bg-primary' : 'bg-muted'
              }`}
            />
          ))}
        </div>
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>Shop Details</span>
          <span>Request Details</span>
          <span>Payment</span>
          <span>Preview</span>
          <span>Confirm</span>
        </div>
      </CardHeader>

      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4">
          {formError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{formError}</AlertDescription>
            </Alert>
          )}

          {renderStepContent()}
        </CardContent>

        <CardFooter className="flex justify-between">
          {currentStep > 1 ? (
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={isLoading}
            >
              <ChevronLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          ) : (
            <Button
              type="button"
              variant="outline"
              onClick={() => navigate('/requests')}
              disabled={isLoading}
            >
              Cancel
            </Button>
          )}

          {currentStep < totalSteps ? (
            <Button
              type="button"
              onClick={nextStep}
              disabled={isLoading}
            >
              Next
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          ) : (
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-primary hover:bg-primary-dark text-white"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {existingRequest
                    ? isResubmission ? 'Resubmitting...' : 'Updating...'
                    : 'Submitting...'}
                </>
              ) : (
                existingRequest
                  ? isResubmission ? 'Resubmit Request' : 'Update Request'
                  : 'Submit Request'
              )}
            </Button>
          )}
        </CardFooter>
      </form>
    </Card>
  );
};

const compressImage = async (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = (event) => {
      const img = new Image();
      img.src = event.target?.result as string;
      img.onload = () => {
        const canvas = document.createElement('canvas');
        let width = img.width;
        let height = img.height;

        const MAX_WIDTH = 1920;
        const MAX_HEIGHT = 1080;

        if (width > height) {
          if (width > MAX_WIDTH) {
            height *= MAX_WIDTH / width;
            width = MAX_WIDTH;
          }
        } else {
          if (height > MAX_HEIGHT) {
            width *= MAX_HEIGHT / height;
            height = MAX_HEIGHT;
          }
        }

        canvas.width = width;
        canvas.height = height;

        const ctx = canvas.getContext('2d');
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Canvas to Blob conversion failed'));
              return;
            }
            const compressedFile = new File([blob], file.name, {
              type: 'image/jpeg',
              lastModified: Date.now(),
            });
            resolve(compressedFile);
          },
          'image/jpeg',
          0.7
        );
      };
      img.onerror = (error) => reject(error);
    };
    reader.onerror = (error) => reject(error);
  });
};

const createSyntheticEvent = (file: File): React.ChangeEvent<HTMLInputElement> => {
  const event = new Event('change', { bubbles: true });
  Object.defineProperty(event, 'target', {
    writable: false,
    value: {
      files: [file],
      value: ''
    }
  });
  return event as React.ChangeEvent<HTMLInputElement>;
};

export default RequestForm;
