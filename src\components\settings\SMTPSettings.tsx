import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Mail, Send, CheckCircle, XCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useMutation, useQuery } from 'convex/react';
import { api } from '../../../convex/_generated/api';
import { useAuth } from '@/lib/auth-context';

const smtpSchema = z.object({
  smtp_enabled: z.boolean(),
  smtp_host: z.string().min(1, 'SMTP host is required'),
  smtp_port: z.number().min(1).max(65535, 'Port must be between 1 and 65535'),
  smtp_secure: z.boolean(),
  smtp_user: z.string().min(1, 'SMTP username is required'),
  smtp_password: z.string().min(1, 'SMTP password is required'),
  smtp_from_email: z.string().email('Valid email address is required'),
  smtp_from_name: z.string().min(1, 'From name is required'),
  smtp_use_env: z.boolean(),
});

type SMTPFormData = z.infer<typeof smtpSchema>;

export function SMTPSettings() {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);

  // Get current settings
  const settings = useQuery(
    api.settings.getSettings,
    user?.id ? { workosUserId: user.id } : "skip"
  );

  const updateSettings = useMutation(api.settings.updateSettings);
  const testSMTPConnection = useMutation(api.realSMTPService.testRealSMTPConnection);

  const form = useForm<SMTPFormData>({
    resolver: zodResolver(smtpSchema),
    defaultValues: {
      smtp_enabled: false,
      smtp_host: '',
      smtp_port: 587,
      smtp_secure: true,
      smtp_user: '',
      smtp_password: '',
      smtp_from_email: '',
      smtp_from_name: 'Request Management System',
      smtp_use_env: false,
    },
  });

  // Update form when settings load
  React.useEffect(() => {
    if (settings) {
      form.reset({
        smtp_enabled: settings.smtp_enabled || false,
        smtp_host: settings.smtp_host || '',
        smtp_port: settings.smtp_port || 587,
        smtp_secure: settings.smtp_secure !== undefined ? settings.smtp_secure : true,
        smtp_user: settings.smtp_user || '',
        smtp_password: settings.smtp_password || '',
        smtp_from_email: settings.smtp_from_email || '',
        smtp_from_name: settings.smtp_from_name || 'Request Management System',
        smtp_use_env: settings.smtp_use_env || false,
      });
    }
  }, [settings, form]);

  const onSubmit = async (data: SMTPFormData) => {
    try {
      await updateSettings({ 
        ...data,
        workosUserId: user?.id
      });
      toast({
        title: 'SMTP Settings Updated',
        description: 'Your SMTP configuration has been saved successfully.',
      });
      setTestResult(null); // Clear any previous test results
    } catch (error) {
      console.error('Error updating SMTP settings:', error);
      toast({
        title: 'Error',
        description: 'Failed to update SMTP settings. Please try again.',
        variant: 'destructive',
      });
    }
  };

  const handleTestConnection = async () => {
    const formData = form.getValues();
    
    if (!formData.smtp_host || !formData.smtp_user || !formData.smtp_password || !formData.smtp_from_email) {
      toast({
        title: 'Missing Configuration',
        description: 'Please fill in all required SMTP fields before testing.',
        variant: 'destructive',
      });
      return;
    }

    setIsTestingConnection(true);
    setTestResult(null);

    try {
      // Note: testSMTPConnection is an Action, but we're calling it as a Mutation
      // This will be fixed by changing it to a Mutation in the backend
      const result = await testSMTPConnection({
        host: formData.smtp_host,
        port: formData.smtp_port,
        secure: formData.smtp_secure,
        user: formData.smtp_user,
        password: formData.smtp_password,
        fromEmail: formData.smtp_from_email,
        fromName: formData.smtp_from_name,
        testEmail: user?.email || formData.smtp_from_email,
      });

      setTestResult(result);
      
      if (result.success) {
        toast({
          title: 'Test Successful',
          description: result.message,
        });
      } else {
        toast({
          title: 'Test Failed',
          description: result.error || 'Failed to send test email.',
          variant: 'destructive',
        });
      }
    } catch (error) {
      console.error('Error testing SMTP connection:', error);
      setTestResult({
        success: false,
        message: 'Failed to test SMTP connection. Please check your configuration.',
      });
      toast({
        title: 'Test Failed',
        description: 'Failed to test SMTP connection. Please check your configuration.',
        variant: 'destructive',
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const useEnvVars = form.watch('smtp_use_env');

  if (!settings) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-medium">SMTP Configuration</h3>
        <p className="text-sm text-muted-foreground">
          Configure SMTP settings to send email notifications for request updates.
        </p>
      </div>

      <Separator />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Enable SMTP */}
          <FormField
            control={form.control}
            name="smtp_enabled"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Enable Email Notifications</FormLabel>
                  <FormDescription>
                    Send email notifications for request status changes
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {/* Use Environment Variables */}
          <FormField
            control={form.control}
            name="smtp_use_env"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Use Environment Variables</FormLabel>
                  <FormDescription>
                    Use SMTP configuration from environment variables instead of database
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {!useEnvVars && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Mail className="h-5 w-5" />
                  SMTP Server Configuration
                </CardTitle>
                <CardDescription>
                  Configure your SMTP server settings for sending emails
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* SMTP Host */}
                <FormField
                  control={form.control}
                  name="smtp_host"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SMTP Host</FormLabel>
                      <FormControl>
                        <Input placeholder="smtp.gmail.com" {...field} />
                      </FormControl>
                      <FormDescription>
                        Your SMTP server hostname
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* SMTP Port and Security */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="smtp_port"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Port</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            placeholder="587"
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 587)}
                          />
                        </FormControl>
                        <FormDescription>
                          Usually 587 (TLS) or 465 (SSL)
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="smtp_secure"
                    render={({ field }) => (
                      <FormItem className="flex flex-col justify-end">
                        <div className="flex items-center space-x-2">
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <FormLabel>Use TLS/SSL</FormLabel>
                        </div>
                        <FormDescription>
                          Enable secure connection
                        </FormDescription>
                      </FormItem>
                    )}
                  />
                </div>

                {/* Authentication */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="smtp_user"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormDescription>
                          SMTP authentication username
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="smtp_password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input type="password" placeholder="••••••••" {...field} />
                        </FormControl>
                        <FormDescription>
                          SMTP authentication password
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* From Email Settings */}
                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="smtp_from_email"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>From Email</FormLabel>
                        <FormControl>
                          <Input placeholder="<EMAIL>" {...field} />
                        </FormControl>
                        <FormDescription>
                          Email address for outgoing messages
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="smtp_from_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>From Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Request Management System" {...field} />
                        </FormControl>
                        <FormDescription>
                          Display name for outgoing messages
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          )}

          {useEnvVars && (
            <Alert>
              <AlertDescription>
                SMTP configuration will be read from environment variables:
                <br />
                <code className="text-xs">
                  SMTP_ENABLED, SMTP_HOST, SMTP_PORT, SMTP_SECURE, SMTP_USER, SMTP_PASSWORD, SMTP_FROM_EMAIL, SMTP_FROM_NAME
                </code>
              </AlertDescription>
            </Alert>
          )}

          {/* Test Result */}
          {testResult && (
            <Alert variant={testResult.success ? "default" : "destructive"}>
              {testResult.success ? (
                <CheckCircle className="h-4 w-4" />
              ) : (
                <XCircle className="h-4 w-4" />
              )}
              <AlertDescription>
                {testResult.message}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button type="submit">
              Save SMTP Settings
            </Button>
            
            {!useEnvVars && (
              <Button
                type="button"
                variant="outline"
                onClick={handleTestConnection}
                disabled={isTestingConnection}
              >
                {isTestingConnection ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Testing...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    Test Connection
                  </>
                )}
              </Button>
            )}
          </div>
        </form>
      </Form>
    </div>
  );
}