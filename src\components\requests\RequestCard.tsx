
import React from 'react';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Request } from '@/lib/types';
import StatusBadge from '@/components/ui/StatusBadge';
import { Eye, BanknoteIcon, ArrowUpRight, Check, X } from 'lucide-react';
import { Link } from 'react-router-dom';
import { formatDistanceToNow } from 'date-fns';
import { canApproveRequest } from '@/lib/auth';
import { Badge } from '@/components/ui/badge';

interface RequestCardProps {
  request: Request;
  user?: any; // User object passed as prop
  momoThreshold?: number; // Settings passed as prop
}

const RequestCard: React.FC<RequestCardProps> = ({ request, user, momoThreshold = 5000 }) => {

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 2,
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const canApprove = user ? canApproveRequest(
    user.role,
    request.paymentMethod,
    request.amount
  ) : false;

  const renderPaymentMethodBadge = () => {
    if (request.paymentMethod === 'mobile_money') {
      return (
        <Badge variant="outline">
          Mobile Money {request.amount > momoThreshold && '(Exceeds Limit)'}
        </Badge>
      );
    }
    return <Badge variant="outline">Bank Transfer</Badge>;
  };

  return (
    <Card className="overflow-hidden transition-all duration-200 hover:shadow-md">
      <CardContent className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div>
            <h3 className="font-medium text-lg">{request.title}</h3>
            <p className="text-sm text-muted-foreground">{request.shopName}</p>
          </div>
          <StatusBadge status={request.status} />
        </div>

        <div className="flex items-center gap-2 mb-4">
          <BanknoteIcon className="h-4 w-4 text-muted-foreground" />
          <span className="font-semibold">{formatAmount(request.amount)}</span>
          <span className="text-sm text-muted-foreground capitalize">
            via {request.paymentMethod.replace('_', ' ')}
          </span>
        </div>

        <div className="text-sm text-muted-foreground mb-4">
          Created {formatDistanceToNow(new Date(request.createdAt), { addSuffix: true })}
        </div>

        <div className="flex flex-col gap-2 mt-4">
          <Button variant="ghost" size="sm" asChild className="w-full">
            <Link to={`/requests/${request._id}`} className="flex items-center justify-center gap-1">
              <Eye className="h-4 w-4" />
              <span>View Details</span>
            </Link>
          </Button>

          {canApprove && request.status === 'pending' && (
            <div className="flex gap-2">
              <Button variant="default" size="sm" asChild className="flex-1">
                <Link to={`/requests/${request._id}`} className="flex items-center justify-center gap-1">
                  <Check className="h-4 w-4" />
                  <span>Approve</span>
                </Link>
              </Button>

              <Button variant="outline" size="sm" asChild className="flex-1">
                <Link to={`/requests/${request._id}`} className="flex items-center justify-center gap-1">
                  <X className="h-4 w-4" />
                  <span>Reject</span>
                </Link>
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default RequestCard;
