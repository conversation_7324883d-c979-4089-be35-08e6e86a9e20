import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Quick fix for ebenezer.eshun user name
export const fixEbenezerName = mutation({
  args: {},
  handler: async (ctx) => {
    // Find the user by email
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .unique();
    
    if (!user) {
      throw new Error("User <EMAIL> not found");
    }
    
    console.log("Current user data:", {
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      currentInitials: user.firstName 
        ? (user.firstName + (user.lastName || '')).substring(0, 2).toUpperCase()
        : user.email?.substring(0, 2).toUpperCase()
    });
    
    // Update the user with correct name and remove profile picture
    await ctx.db.patch(user._id, {
      firstName: "Ebenezer",
      lastName: "Eshun",
      profilePicture: undefined, // Remove the profile picture to show initials
      updatedAt: Date.now(),
    });
    
    return {
      success: true,
      message: "Fixed user name and removed profile <NAME_EMAIL>",
      oldData: {
        firstName: user.firstName,
        lastName: user.lastName,
        profilePicture: user.profilePicture,
      },
      newData: {
        firstName: "Ebenezer",
        lastName: "Eshun",
        profilePicture: "removed",
      },
      newInitials: "EB",
      note: "Profile picture removed - avatar will now show text initials 'EB'"
    };
  },
});
