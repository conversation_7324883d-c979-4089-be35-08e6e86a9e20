import { create } from 'zustand';
import { DashboardStats } from '@/lib/types';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { convex } from '@/lib/convex';

interface StatsState {
  stats: DashboardStats;
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchStats: (force?: boolean) => Promise<void>;
  fetchStatsByDateRange: (startDate: Date, endDate: Date) => Promise<void>;
  fetchAreaStats: (areaId: string, startDate?: Date, endDate?: Date) => Promise<DashboardStats>;
  fetchShopStats: (shopId: string, startDate?: Date, endDate?: Date) => Promise<DashboardStats>;
}

// Cache duration: 1 minute for stats (they change frequently)
const STATS_CACHE_DURATION = 1 * 60 * 1000;

// Helper function to combine stats from different sources
function combineStats(requestStats: any, shopStats: any, areaStats: any): DashboardStats {
  return {
    totalAmount: requestStats?.totalAmount || 0,
    totalRequests: requestStats?.total || 0,
    pendingRequests: requestStats?.pending || 0,
    approvedRequests: requestStats?.approved || 0,
    rejectedRequests: requestStats?.rejected || 0,
    resubmittedRequests: requestStats?.resubmitted || 0,
    mobileMoneyAmount: requestStats?.mobileMoneyAmount || 0,
    bankTransferAmount: requestStats?.bankTransferAmount || 0,
    uniqueAreas: areaStats?.total || 0,
    uniqueShops: shopStats?.total || 0,
    monthlyStats: requestStats?.monthlyStats || [],
  };
}

export const useStatsStore = create<StatsState>((set, get) => ({
  stats: {
    totalAmount: 0,
    totalRequests: 0,
    pendingRequests: 0,
    approvedRequests: 0,
    rejectedRequests: 0,
    resubmittedRequests: 0,
    mobileMoneyAmount: 0,
    bankTransferAmount: 0,
    uniqueAreas: 0,
    uniqueShops: 0,
    monthlyStats: []
  },
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchStats: async (force = false) => {
    // Skip store queries when using WorkOS AuthKit (use public queries instead)
    console.log('fetchStats called - skipping for WorkOS AuthKit mode');
    return;

    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < STATS_CACHE_DURATION) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      console.log('Store: Fetching dashboard statistics');

      // Fetch statistics from all sources in parallel
      const [requestStats, shopStats, areaStats] = await Promise.all([
        convex.query(api.requests.getRequestStats, {}),
        convex.query(api.shops.getShopStats, {}),
        convex.query(api.areas.getAreaStats, {})
      ]);

      const combinedStats = combineStats(requestStats, shopStats, areaStats);

      console.log('Store: Dashboard statistics fetched successfully', combinedStats);

      set({ 
        stats: combinedStats, 
        isLoading: false, 
        lastFetch: now 
      });
    } catch (error: any) {
      console.error('Store: Error fetching stats', error);
      set({
        error: error.message || 'Failed to fetch stats',
        isLoading: false
      });
    }
  },

  fetchStatsByDateRange: async (startDate: Date, endDate: Date) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Fetching stats by date range', startDate, endDate);

      // Fetch statistics for the specified date range
      const [requestStats, shopStats, areaStats] = await Promise.all([
        convex.query(api.requests.getRequestStats, {
          startDate: startDate.getTime(),
          endDate: endDate.getTime(),
        }),
        convex.query(api.shops.getShopStats, {
          startDate: startDate.getTime(),
          endDate: endDate.getTime(),
        }),
        convex.query(api.areas.getAreaStats, {
          startDate: startDate.getTime(),
          endDate: endDate.getTime(),
        })
      ]);

      const combinedStats = combineStats(requestStats, shopStats, areaStats);

      console.log('Store: Date range statistics fetched successfully', combinedStats);

      set({ 
        stats: combinedStats, 
        isLoading: false 
      });
    } catch (error: any) {
      console.error('Store: Error fetching stats by date range', error);
      set({
        error: error.message || 'Failed to fetch stats for date range',
        isLoading: false
      });
    }
  },

  fetchAreaStats: async (areaId: string, startDate?: Date, endDate?: Date) => {
    try {
      console.log('Store: Fetching area stats', areaId);

      // Fetch statistics for the specific area
      const [requestStats, shopStats] = await Promise.all([
        convex.query(api.requests.getRequestStats, {
          areaId: areaId as Id<"areas">,
          startDate: startDate?.getTime(),
          endDate: endDate?.getTime(),
        }),
        convex.query(api.shops.getShopStats, {
          areaId: areaId as Id<"areas">,
          startDate: startDate?.getTime(),
          endDate: endDate?.getTime(),
        })
      ]);

      const areaStats = {
        totalAmount: requestStats?.totalAmount || 0,
        totalRequests: requestStats?.total || 0,
        pendingRequests: requestStats?.pending || 0,
        approvedRequests: requestStats?.approved || 0,
        rejectedRequests: requestStats?.rejected || 0,
        resubmittedRequests: requestStats?.resubmitted || 0,
        mobileMoneyAmount: requestStats?.mobileMoneyAmount || 0,
        bankTransferAmount: requestStats?.bankTransferAmount || 0,
        uniqueAreas: 1, // Single area
        uniqueShops: shopStats?.total || 0,
        monthlyStats: requestStats?.monthlyStats || [],
      };

      console.log('Store: Area statistics fetched successfully', areaStats);
      return areaStats;
    } catch (error: any) {
      console.error('Store: Error fetching area stats', error);
      throw error;
    }
  },

  fetchShopStats: async (shopId: string, startDate?: Date, endDate?: Date) => {
    try {
      console.log('Store: Fetching shop stats', shopId);

      // Fetch statistics for the specific shop
      const requestStats = await convex.query(api.requests.getRequestStats, {
        shopId: shopId as Id<"shops">,
        startDate: startDate?.getTime(),
        endDate: endDate?.getTime(),
      });

      const shopStats = {
        totalAmount: requestStats?.totalAmount || 0,
        totalRequests: requestStats?.total || 0,
        pendingRequests: requestStats?.pending || 0,
        approvedRequests: requestStats?.approved || 0,
        rejectedRequests: requestStats?.rejected || 0,
        resubmittedRequests: requestStats?.resubmitted || 0,
        mobileMoneyAmount: requestStats?.mobileMoneyAmount || 0,
        bankTransferAmount: requestStats?.bankTransferAmount || 0,
        uniqueAreas: 1, // Shop belongs to one area
        uniqueShops: 1, // Single shop
        monthlyStats: requestStats?.monthlyStats || [],
      };

      console.log('Store: Shop statistics fetched successfully', shopStats);
      return shopStats;
    } catch (error: any) {
      console.error('Store: Error fetching shop stats', error);
      throw error;
    }
  }
}));

// React hooks for components using Convex real-time queries
export const useStatsQuery = () => {
  const requestStats = useQuery(api.requests.getRequestStats, {});
  const shopStats = useQuery(api.shops.getShopStats, {});
  const areaStats = useQuery(api.areas.getAreaStats, {});

  if (!requestStats || !shopStats || !areaStats) {
    return undefined;
  }

  return combineStats(requestStats, shopStats, areaStats);
};

export const useStatsByDateRangeQuery = (startDate: Date, endDate: Date) => {
  const requestStats = useQuery(api.requests.getRequestStats, {
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
  });
  const shopStats = useQuery(api.shops.getShopStats, {
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
  });
  const areaStats = useQuery(api.areas.getAreaStats, {
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
  });

  if (!requestStats || !shopStats || !areaStats) {
    return undefined;
  }

  return combineStats(requestStats, shopStats, areaStats);
};

export const useAreaStatsQuery = (areaId: string, startDate?: Date, endDate?: Date) => {
  return useQuery(api.requests.getRequestStats, {
    areaId: areaId as Id<"areas">,
    startDate: startDate?.getTime(),
    endDate: endDate?.getTime(),
  });
};

export const useShopStatsQuery = (shopId: string, startDate?: Date, endDate?: Date) => {
  return useQuery(api.requests.getRequestStats, {
    shopId: shopId as Id<"shops">,
    startDate: startDate?.getTime(),
    endDate: endDate?.getTime(),
  });
};

export const useRequestStatsQuery = (filters?: {
  areaId?: string;
  shopId?: string;
  startDate?: Date;
  endDate?: Date;
}) => {
  return useQuery(api.requests.getRequestStats, {
    areaId: filters?.areaId as Id<"areas"> | undefined,
    shopId: filters?.shopId as Id<"shops"> | undefined,
    startDate: filters?.startDate?.getTime(),
    endDate: filters?.endDate?.getTime(),
  });
};
