/**
 * Centralized currency formatting utilities
 */

export const formatAmount = (amount: number): string => {
  return new Intl.NumberFormat('en-GH', {
    style: 'currency',
    currency: 'GHS',
    minimumFractionDigits: 2,
    currencyDisplay: 'symbol'
  }).format(amount).replace(/^\$/, 'GH₵');
};

export const formatAmountShort = (amount: number): string => {
  return new Intl.NumberFormat('en-GH', {
    style: 'currency',
    currency: 'GHS',
    minimumFractionDigits: 0,
    currencyDisplay: 'symbol'
  }).format(amount).replace(/^\$/, 'GH₵');
};

export const formatYAxisValue = (value: number): string => {
  return `GH₵${(value / 1000).toFixed(1)}K`;
};
