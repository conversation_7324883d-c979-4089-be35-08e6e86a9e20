#!/usr/bin/env node

/**
 * Test script for Convex settings implementation
 * 
 * This script tests the settings functions to ensure they work correctly.
 * Run with: node scripts/test-settings.js
 */

const { ConvexHttpClient } = require("convex/browser");

// Get Convex URL from environment
const CONVEX_URL = process.env.VITE_CONVEX_URL;

if (!CONVEX_URL) {
  console.error("❌ VITE_CONVEX_URL environment variable is required");
  process.exit(1);
}

const client = new ConvexHttpClient(CONVEX_URL);

async function testSettings() {
  console.log("🧪 Testing Convex Settings Implementation");
  console.log("=" .repeat(50));

  try {
    // Test 1: Check if settings functions exist
    console.log("\n1. Testing settings API availability...");
    
    // This will fail if the settings module doesn't exist
    const settings = await client.query("settings:getSettings", {});
    console.log("✅ Settings API is available");
    console.log("📊 Current settings:", JSON.stringify(settings, null, 2));

    // Test 2: Test system initialization check
    console.log("\n2. Checking system initialization...");
    
    const initStatus = await client.query("init:checkSystemInitialization", {});
    console.log("📋 Initialization status:", JSON.stringify(initStatus, null, 2));

    if (!initStatus.isInitialized) {
      console.log("⚠️  System not fully initialized");
      console.log("Missing roles:", initStatus.roles.missing);
      console.log("Missing settings:", initStatus.settings.missing);
    } else {
      console.log("✅ System is fully initialized");
    }

    // Test 3: Get system status
    console.log("\n3. Getting system status...");
    
    const systemStatus = await client.query("init:getSystemStatus", {});
    console.log("📈 System status:", JSON.stringify(systemStatus, null, 2));

    console.log("\n" + "=" .repeat(50));
    console.log("🎉 All tests completed successfully!");
    console.log("✅ Settings implementation is working correctly");

  } catch (error) {
    console.error("\n❌ Test failed:", error.message);
    
    if (error.message.includes("settings:getSettings")) {
      console.log("\n💡 This likely means:");
      console.log("   1. The Convex dev server is not running");
      console.log("   2. The settings.ts file hasn't been deployed yet");
      console.log("   3. Run 'npx convex dev' to start the development server");
    }
    
    process.exit(1);
  }
}

// Run the test
testSettings();