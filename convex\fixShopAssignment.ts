import { mutation } from "./_generated/server";
import { v } from "convex/values";

/**
 * Fix missing shop manager assignments
 * This is a one-time fix for users who didn't get properly assigned to shops during invitation
 */
export const fixMissingShopAssignment = mutation({
  args: {
    userId: v.id("users"),
    shopId: v.id("shops"),
  },
  handler: async (ctx, args) => {
    console.log(`Fixing shop assignment: User ${args.userId} -> Shop ${args.shopId}`);
    
    // Verify user exists
    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }
    
    // Verify shop exists
    const shop = await ctx.db.get(args.shopId);
    if (!shop) {
      throw new Error("Shop not found");
    }
    
    // Check if shop already has a manager
    if (shop.managerId) {
      console.log(`Shop ${args.shopId} already has manager ${shop.managerId}`);
      return { success: false, reason: "Shop already has a manager" };
    }
    
    // Assign the manager
    const now = Date.now();
    await ctx.db.patch(args.shopId, {
      managerId: args.userId,
      updatedAt: now,
    });
    
    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "shop_manager_assigned_fix",
      entityType: "shop",
      entityId: args.shopId,
      userId: args.userId,
      metadata: { 
        source: "manual_fix",
        originalUser: user.email,
        shopName: shop.name
      },
      timestamp: now,
    });
    
    console.log(`Successfully assigned user ${user.email} to shop ${shop.name}`);
    
    return { 
      success: true, 
      userEmail: user.email, 
      shopName: shop.name,
      shopId: args.shopId,
      userId: args.userId
    };
  },
});