@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 20% 98%; /* #F8F9FA */
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 152 100% 30%;  /* #009848 */
    --primary-light: 152 59% 93%;  /* #e6f5ed */
    --primary-dark: 152 100% 24%;  /* #007a3a */
    --primary-foreground: 0 0% 100%;  /* #ffffff */

    --secondary: 215 67% 41%;  /* #2266af */
    --secondary-light: 214 59% 95%;  /* #e8f0f8 */
    --secondary-dark: 215 68% 33%;  /* #1a508c */
    --secondary-foreground: 0 0% 100%;  /* #ffffff */

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;  /* #229A16 */
    --success-foreground: 0 0% 100%;

    --warning: 36 100% 36%;  /* #B76E00 */
    --warning-foreground: 0 0% 100%;

    --info: 205 100% 36%;  /* #0078D7 */
    --info-foreground: 0 0% 100%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 152 100% 30%;  /* #009848 */
    --primary-light: 152 59% 15%;  /* darker for dark mode */
    --primary-dark: 152 100% 35%;  /* lighter for dark mode */
    --primary-foreground: 0 0% 100%;  /* #ffffff */

    --secondary: 215 67% 41%;  /* #2266af */
    --secondary-light: 214 59% 20%;  /* darker for dark mode */
    --secondary-dark: 215 68% 50%;  /* lighter for dark mode */
    --secondary-foreground: 0 0% 100%;  /* #ffffff */

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --success: 142 76% 36%;  /* #229A16 */
    --success-foreground: 0 0% 100%;

    --warning: 36 100% 36%;  /* #B76E00 */
    --warning-foreground: 0 0% 100%;

    --info: 205 100% 36%;  /* #0078D7 */
    --info-foreground: 0 0% 100%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-app-background text-foreground;
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .card-hover {
    @apply transition-all hover:shadow-md hover:-translate-y-1;
  }

  .glass-card {
    @apply bg-background/70 backdrop-blur-sm border shadow-sm;
  }

  .glass-panel {
    @apply bg-background/70 backdrop-blur-md shadow-md border;
  }

  .primary-gradient {
    @apply bg-gradient-to-r from-primary-dark via-primary to-primary-light;
  }

  .secondary-gradient {
    @apply bg-gradient-to-r from-secondary-dark via-secondary to-secondary-light;
  }

  /* Status colors with exact backgrounds */
  .bg-status-pending {
    background-color: rgba(255, 171, 0, 0.12);
    color: #B76E00;
  }

  .bg-status-approved {
    background-color: rgba(84, 214, 44, 0.12);
    color: #229A16;
  }

  .bg-status-rejected {
    background-color: rgba(255, 72, 66, 0.12);
    color: #B72136;
  }

  .bg-status-resubmitted {
    background-color: rgba(0, 120, 215, 0.12);
    color: #0078D7;
  }

  /* Line clamp utilities */
  .line-clamp-1 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }

  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }
}
