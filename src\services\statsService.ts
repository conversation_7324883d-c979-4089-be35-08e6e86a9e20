import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { useQuery } from 'convex/react';
import { convex } from '@/lib/convex';
import { DashboardStats } from '@/lib/types';

/**
 * Fetch dashboard statistics
 */
export async function getStats(): Promise<DashboardStats> {
  try {
    // Get request statistics
    const requestStats = await convex.query(api.requests.getRequestStats, {});
    
    // Get shop statistics
    const shopStats = await convex.query(api.shops.getShopStats, {});
    
    // Get area statistics
    const areaStats = await convex.query(api.areas.getAreaStats, {});

    // Combine all statistics into dashboard format
    return {
      totalRequests: requestStats.total || 0,
      pendingRequests: requestStats.pending || 0,
      approvedRequests: requestStats.approved || 0,
      rejectedRequests: requestStats.rejected || 0,
      paidRequests: requestStats.paid || 0,
      totalAmount: requestStats.totalAmount || 0,
      totalShops: shopStats.total || 0,
      activeShops: shopStats.active || 0,
      totalAreas: areaStats.total || 0,
      activeAreas: areaStats.active || 0,
      // Additional derived statistics
      approvalRate: requestStats.total > 0 
        ? Math.round((requestStats.approved / requestStats.total) * 100) 
        : 0,
      averageAmount: requestStats.total > 0 
        ? Math.round(requestStats.totalAmount / requestStats.total) 
        : 0,
      shopsWithManager: shopStats.withManager || 0,
      shopsWithoutManager: shopStats.withoutManager || 0,
    };
  } catch (error) {
    console.error('Error fetching stats:', error);
    throw error;
  }
}

/**
 * Get statistics for a specific time period
 */
export async function getStatsByDateRange(startDate: Date, endDate: Date): Promise<DashboardStats> {
  try {
    // Get request statistics for date range
    const requestStats = await convex.query(api.requests.getRequestStats, {
      startDate: startDate.getTime(),
      endDate: endDate.getTime(),
    });
    
    // Get shop statistics for date range
    const shopStats = await convex.query(api.shops.getShopStats, {
      startDate: startDate.getTime(),
      endDate: endDate.getTime(),
    });
    
    // Get area statistics for date range
    const areaStats = await convex.query(api.areas.getAreaStats, {
      startDate: startDate.getTime(),
      endDate: endDate.getTime(),
    });

    // Combine all statistics into dashboard format
    return {
      totalRequests: requestStats.total || 0,
      pendingRequests: requestStats.pending || 0,
      approvedRequests: requestStats.approved || 0,
      rejectedRequests: requestStats.rejected || 0,
      paidRequests: requestStats.paid || 0,
      totalAmount: requestStats.totalAmount || 0,
      totalShops: shopStats.total || 0,
      activeShops: shopStats.active || 0,
      totalAreas: areaStats.total || 0,
      activeAreas: areaStats.active || 0,
      // Additional derived statistics
      approvalRate: requestStats.total > 0 
        ? Math.round((requestStats.approved / requestStats.total) * 100) 
        : 0,
      averageAmount: requestStats.total > 0 
        ? Math.round(requestStats.totalAmount / requestStats.total) 
        : 0,
      shopsWithManager: shopStats.withManager || 0,
      shopsWithoutManager: shopStats.withoutManager || 0,
    };
  } catch (error) {
    console.error('Error fetching stats by date range:', error);
    throw error;
  }
}

/**
 * Get statistics for a specific area
 */
export async function getAreaStats(areaId: string, startDate?: Date, endDate?: Date): Promise<DashboardStats> {
  try {
    // Get request statistics for area
    const requestStats = await convex.query(api.requests.getRequestStats, {
      areaId: areaId as Id<"areas">,
      startDate: startDate?.getTime(),
      endDate: endDate?.getTime(),
    });
    
    // Get shop statistics for area
    const shopStats = await convex.query(api.shops.getShopStats, {
      areaId: areaId as Id<"areas">,
      startDate: startDate?.getTime(),
      endDate: endDate?.getTime(),
    });

    // Combine statistics
    return {
      totalRequests: requestStats.total || 0,
      pendingRequests: requestStats.pending || 0,
      approvedRequests: requestStats.approved || 0,
      rejectedRequests: requestStats.rejected || 0,
      paidRequests: requestStats.paid || 0,
      totalAmount: requestStats.totalAmount || 0,
      totalShops: shopStats.total || 0,
      activeShops: shopStats.active || 0,
      totalAreas: 1, // Single area
      activeAreas: 1, // Single area
      // Additional derived statistics
      approvalRate: requestStats.total > 0 
        ? Math.round((requestStats.approved / requestStats.total) * 100) 
        : 0,
      averageAmount: requestStats.total > 0 
        ? Math.round(requestStats.totalAmount / requestStats.total) 
        : 0,
      shopsWithManager: shopStats.withManager || 0,
      shopsWithoutManager: shopStats.withoutManager || 0,
    };
  } catch (error) {
    console.error('Error fetching area stats:', error);
    throw error;
  }
}

/**
 * Get statistics for a specific shop
 */
export async function getShopStats(shopId: string, startDate?: Date, endDate?: Date): Promise<DashboardStats> {
  try {
    // Get request statistics for shop
    const requestStats = await convex.query(api.requests.getRequestStats, {
      shopId: shopId as Id<"shops">,
      startDate: startDate?.getTime(),
      endDate: endDate?.getTime(),
    });

    // Combine statistics
    return {
      totalRequests: requestStats.total || 0,
      pendingRequests: requestStats.pending || 0,
      approvedRequests: requestStats.approved || 0,
      rejectedRequests: requestStats.rejected || 0,
      paidRequests: requestStats.paid || 0,
      totalAmount: requestStats.totalAmount || 0,
      totalShops: 1, // Single shop
      activeShops: 1, // Single shop
      totalAreas: 1, // Shop belongs to one area
      activeAreas: 1, // Shop belongs to one area
      // Additional derived statistics
      approvalRate: requestStats.total > 0 
        ? Math.round((requestStats.approved / requestStats.total) * 100) 
        : 0,
      averageAmount: requestStats.total > 0 
        ? Math.round(requestStats.totalAmount / requestStats.total) 
        : 0,
      shopsWithManager: 1, // Assuming shop has manager if we're getting stats
      shopsWithoutManager: 0,
    };
  } catch (error) {
    console.error('Error fetching shop stats:', error);
    throw error;
  }
}

// React hooks for components (these provide real-time updates)
export const useStats = () => {
  const requestStats = useQuery(api.requests.getRequestStats, {});
  const shopStats = useQuery(api.shops.getShopStats, {});
  const areaStats = useQuery(api.areas.getAreaStats, {});

  if (!requestStats || !shopStats || !areaStats) {
    return undefined;
  }

  return {
    totalRequests: requestStats.total || 0,
    pendingRequests: requestStats.pending || 0,
    approvedRequests: requestStats.approved || 0,
    rejectedRequests: requestStats.rejected || 0,
    paidRequests: requestStats.paid || 0,
    totalAmount: requestStats.totalAmount || 0,
    totalShops: shopStats.total || 0,
    activeShops: shopStats.active || 0,
    totalAreas: areaStats.total || 0,
    activeAreas: areaStats.active || 0,
    approvalRate: requestStats.total > 0 
      ? Math.round((requestStats.approved / requestStats.total) * 100) 
      : 0,
    averageAmount: requestStats.total > 0 
      ? Math.round(requestStats.totalAmount / requestStats.total) 
      : 0,
    shopsWithManager: shopStats.withManager || 0,
    shopsWithoutManager: shopStats.withoutManager || 0,
  };
};

export const useStatsByDateRange = (startDate: Date, endDate: Date) => {
  const requestStats = useQuery(api.requests.getRequestStats, {
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
  });
  const shopStats = useQuery(api.shops.getShopStats, {
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
  });
  const areaStats = useQuery(api.areas.getAreaStats, {
    startDate: startDate.getTime(),
    endDate: endDate.getTime(),
  });

  if (!requestStats || !shopStats || !areaStats) {
    return undefined;
  }

  return {
    totalRequests: requestStats.total || 0,
    pendingRequests: requestStats.pending || 0,
    approvedRequests: requestStats.approved || 0,
    rejectedRequests: requestStats.rejected || 0,
    paidRequests: requestStats.paid || 0,
    totalAmount: requestStats.totalAmount || 0,
    totalShops: shopStats.total || 0,
    activeShops: shopStats.active || 0,
    totalAreas: areaStats.total || 0,
    activeAreas: areaStats.active || 0,
    approvalRate: requestStats.total > 0 
      ? Math.round((requestStats.approved / requestStats.total) * 100) 
      : 0,
    averageAmount: requestStats.total > 0 
      ? Math.round(requestStats.totalAmount / requestStats.total) 
      : 0,
    shopsWithManager: shopStats.withManager || 0,
    shopsWithoutManager: shopStats.withoutManager || 0,
  };
};

export const useAreaStats = (areaId: string, startDate?: Date, endDate?: Date) => {
  return useQuery(api.requests.getRequestStats, {
    areaId: areaId as Id<"areas">,
    startDate: startDate?.getTime(),
    endDate: endDate?.getTime(),
  });
};

export const useShopStats = (shopId: string, startDate?: Date, endDate?: Date) => {
  return useQuery(api.requests.getRequestStats, {
    shopId: shopId as Id<"shops">,
    startDate: startDate?.getTime(),
    endDate: endDate?.getTime(),
  });
};
