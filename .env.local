# This file serves as an example for the required environment variables.
# It documents variables for both the frontend (Vite) and the backend (Convex).

# -----------------------------------------------------------------------------
# Frontend Environment Variables (for Vite)
# -----------------------------------------------------------------------------
# Copy the variables in this section to a new file named `.env.local` in the
# project root, and fill in the values.
# DO NOT commit .env.local to version control.

# The URL of your Convex deployment.
# Found in your project settings on the Convex dashboard.
VITE_CONVEX_URL=https://efficient-toucan-547.convex.cloud

# The public API Key for your WorkOS project.
# Found in your project settings on the WorkOS dashboard.
VITE_WORKOS_API_KEY=sk_test_a2V5XzAxSk0wODZOQjk5WUQwMjkwMzFTSk1aWVo4LDFvTVcwbWNrdGVEVEZnZUdBNmVjZDBKcWs

# The public Client ID for your WorkOS project.
# Found in your project settings on the WorkOS dashboard.
VITE_WORKOS_CLIENT_ID=client_01JM086NRHNV0Z3VKC748YHVHV

# The redirect URI configured in your WorkOS project.
# This must match the value in the WorkOS dashboard exactly.
VITE_WORKOS_REDIRECT_URI=http://localhost:8080/auth/callback

# The base URL of your frontend application.
VITE_APP_URL=http://localhost:8080


# -----------------------------------------------------------------------------
# Backend Environment Variables (for Convex)
# -----------------------------------------------------------------------------
# The variables below must be set in your Convex project's dashboard under
# Settings > Environment Variables.
# They are NOT used by the frontend and should NOT be in your .env.local file.

# The secret API key for your WorkOS project.
# Found in your project settings on the WorkOS dashboard.
# This is a secret and should be handled securely.
WORKOS_API_KEY=sk_test_a2V5XzAxSk0wODZOQjk5WUQwMjkwMzFTSk1aWVo4LDFvTVcwbWNrdGVEVEZnZUdBNmVjZDBKcWs

# The webhook secret for verifying WorkOS webhook signatures.
# Found in your WorkOS dashboard under Webhooks settings.
# This is used to verify that webhooks are actually from WorkOS.
WORKOS_WEBHOOK_SECRET=your_webhook_secret_here

# The public Client ID for your WorkOS project.
# This is the same value as VITE_WORKOS_CLIENT_ID.
WORKOS_CLIENT_ID=client_01JM086NRHNV0Z3VKC748YHVHV

# Your WorkOS project's domain.
# Found in your project settings on the WorkOS dashboard.
WORKOS_DOMAIN=http://localhost:8080

# The ID of the specific WorkOS connection you want to use (e.g., for Google SSO).
# Found in your WorkOS dashboard under SSO connections.
WORKOS_CONNECTION_ID=conn_01JM086NKNBN1BA149GH83ZJ1H

# Deployment used by `npx convex dev`
CONVEX_DEPLOYMENT=dev:efficient-toucan-547 # team: ebenezer-eshun-fc244, project: cash-injection
