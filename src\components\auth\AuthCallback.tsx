import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/lib/auth-context';
import { handleAuthCallback } from '@/lib/workos-auth';
import { toast } from '@/components/ui/use-toast';
import {
  Card,
  CardContent,
} from '@/components/ui/card';

const AuthCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { user, isLoading } = useAuth();
  const [isProcessing, setIsProcessing] = useState(true);

  useEffect(() => {
    const processCallback = async () => {
      try {
        setIsProcessing(true);
        
        // Check for error parameters first
        const error = searchParams.get('error');
        const errorDescription = searchParams.get('error_description');
        
        if (error) {
          console.error('Authentication error:', error, errorDescription);
          toast({
            title: "Authentication Error",
            description: errorDescription || error,
            variant: "destructive",
          });
          navigate('/login', { replace: true });
          return;
        }

        // Handle the WorkOS callback
        console.log('Processing WorkOS callback...');
        const session = await handleAuthCallback();
        
        if (session) {
          console.log('Authentication successful:', session.user.email);
          toast({
            title: "Welcome!",
            description: `Successfully signed in as ${session.user.email}`,
          });
          
          // Redirect to dashboard or intended destination
          const redirectTo = searchParams.get('redirect') || '/dashboard';
          navigate(redirectTo, { replace: true });
        } else {
          console.error('No session returned from callback');
          toast({
            title: "Authentication Failed",
            description: "Unable to complete sign-in. Please try again.",
            variant: "destructive",
          });
          navigate('/login', { replace: true });
        }
      } catch (error) {
        console.error('Callback processing error:', error);
        toast({
          title: "Authentication Error",
          description: "An error occurred during sign-in. Please try again.",
          variant: "destructive",
        });
        navigate('/login', { replace: true });
      } finally {
        setIsProcessing(false);
      }
    };

    // Only process if we haven't already and we're not loading
    if (isProcessing && !isLoading) {
      processCallback();
    }
  }, [navigate, searchParams, isLoading, isProcessing]);

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 bg-gray-100">
      <Card className="w-[calc(100%-2rem)] sm:w-full max-w-[480px] mx-auto shadow-md border-gray-200 bg-white rounded-lg p-4 sm:p-6">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <h2 className="text-lg font-semibold text-gray-800">Completing Sign In</h2>
            <p className="text-sm text-gray-500">
              Please wait while we complete your authentication...
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthCallback;