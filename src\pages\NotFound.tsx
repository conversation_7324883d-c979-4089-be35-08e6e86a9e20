
import React from 'react';
import { useLocation, Link } from "react-router-dom";
import { Button } from '@/components/ui/button';
import { HomeIcon, ArrowLeft } from 'lucide-react';
import PageTransition from '@/components/common/PageTransition';

const NotFound = () => {
  const location = useLocation();

  return (
    <PageTransition>
      <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-background to-muted/40 p-4">
        <div className="text-center animate-fade-in">
          <h1 className="text-6xl font-bold mb-4">404</h1>
          <p className="text-xl text-muted-foreground mb-8">
            Oops! We couldn't find the page you're looking for
          </p>
          <div className="space-y-3">
            <Button asChild>
              <Link to="/" className="flex items-center gap-2">
                <HomeIcon size={16} />
                Return to Home
              </Link>
            </Button>
            <div>
              <Button variant="ghost" onClick={() => window.history.back()}>
                <ArrowLeft size={16} className="mr-2" />
                Go Back
              </Button>
            </div>
          </div>
        </div>
      </div>
    </PageTransition>
  );
};

export default NotFound;
