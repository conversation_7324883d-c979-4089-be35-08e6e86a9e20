#!/usr/bin/env node

/**
 * Direct test of settings implementation
 * This script tests the settings functions using the Convex HTTP client
 */

import { ConvexHttpClient } from "convex/browser";
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Read environment variables
const envLocalPath = path.join(__dirname, '.env.local');
const envPath = path.join(__dirname, '.env');
let CONVEX_URL = process.env.VITE_CONVEX_URL;

// Try .env first (production), then .env.local (development)
if (!CONVEX_URL && fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const match = envContent.match(/VITE_CONVEX_URL=(.+)/);
  if (match) {
    CONVEX_URL = match[1].trim();
  }
}

if (!CONVEX_URL && fs.existsSync(envLocalPath)) {
  const envContent = fs.readFileSync(envLocalPath, 'utf8');
  const match = envContent.match(/VITE_CONVEX_URL=(.+)/);
  if (match) {
    CONVEX_URL = match[1].trim();
  }
}

if (!CONVEX_URL) {
  console.error("❌ VITE_CONVEX_URL not found in environment or .env.local");
  process.exit(1);
}

console.log("🔗 Using Convex URL:", CONVEX_URL);

const client = new ConvexHttpClient(CONVEX_URL);

async function testSettings() {
  console.log("\n🧪 Testing Convex Settings Implementation");
  console.log("=" .repeat(60));

  try {
    // Test 1: Check if we can connect to Convex
    console.log("\n1. Testing Convex connection...");
    
    // Try a simple auth query first
    try {
      const authResult = await client.query("auth:getSessionInfo", {});
      console.log("✅ Convex connection successful");
      console.log("🔐 Auth status:", authResult ? "Authenticated" : "Not authenticated");
    } catch (authError) {
      console.log("⚠️  Auth query failed (expected if not logged in):", authError.message);
    }

    // Test 2: Check if settings module exists
    console.log("\n2. Testing settings module availability...");
    
    try {
      const settings = await client.query("settings:getSettings", {});
      console.log("✅ Settings module is available!");
      console.log("📊 Settings retrieved:", JSON.stringify(settings, null, 2));
    } catch (settingsError) {
      console.log("⚠️  Settings query failed:", settingsError.message);
      
      if (settingsError.message.includes("Authentication required")) {
        console.log("🔐 Settings module exists but requires authentication");
        console.log("✅ This is expected behavior - settings require login");
        
        // Test if the module exists by checking the error type
        console.log("✅ Settings module is properly deployed!");
        
      } else if (settingsError.message.includes("settings:getSettings")) {
        console.log("❌ Settings module not found in API");
        console.log("💡 This means the Convex dev server needs to be running to regenerate the API");
        return false;
      } else {
        console.log("❌ Unexpected error with settings module");
        return false;
      }
    }

    // Test 3: Test initialization module
    console.log("\n3. Testing initialization module...");
    
    try {
      const initStatus = await client.query("init:checkSystemInitialization", {});
      console.log("✅ Initialization module available!");
      console.log("📋 System status:", JSON.stringify(initStatus, null, 2));
    } catch (initError) {
      console.log("❌ Initialization module error:", initError.message);
    }

    console.log("\n" + "=" .repeat(60));
    console.log("🎉 Settings implementation test completed!");
    return true;

  } catch (error) {
    console.error("\n❌ Test failed with error:", error.message);
    console.error("Stack:", error.stack);
    return false;
  }
}

// Run the test
testSettings().then(success => {
  if (success) {
    console.log("\n✅ All tests passed! Settings implementation is working.");
    process.exit(0);
  } else {
    console.log("\n❌ Tests failed. Check the Convex dev server and try again.");
    console.log("\n💡 To fix this:");
    console.log("   1. Run 'npm run convex:dev' in another terminal");
    console.log("   2. Wait for the server to start and regenerate the API");
    console.log("   3. Run this test again");
    process.exit(1);
  }
}).catch(error => {
  console.error("❌ Unexpected error:", error);
  process.exit(1);
});