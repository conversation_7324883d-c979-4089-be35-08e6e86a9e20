// Debug script to test resubmit button logic
console.log("=== RESUBMIT BUTTON DEBUG ===");

// Test the logic from RequestDetails.tsx
function testResubmitLogic(request, workosUser, userWithRoles) {
  console.log("Request data:", {
    id: request?._id,
    status: request?.status,
    workosUserId: request?.workosUserId,
    requestedBy: request?.requestedBy
  });
  
  console.log("User data:", {
    workosUserId: workosUser?.id,
    convexUserId: userWithRoles?.user?._id
  });
  
  // Check ownership: prefer workosUserId (new requests), fallback to requestedBy (existing requests)
  const isCreatedByCurrentUser = request?.workosUserId 
    ? workosUser?.id === request.workosUserId  // New requests with workosUserId
    : userWithRoles?.user?._id === request?.requestedBy;  // Existing requests without workosUserId
  
  console.log("Ownership check:", {
    hasWorkosUserId: !!request?.workosUserId,
    workosUserMatch: workosUser?.id === request?.workosUserId,
    convexUserMatch: userWithRoles?.user?._id === request?.requestedBy,
    isCreatedByCurrentUser
  });

  const canResubmit = isCreatedByCurrentUser && request?.status === 'rejected';
  
  console.log("Final result:", {
    canResubmit,
    isCreatedByCurrentUser,
    statusIsRejected: request?.status === 'rejected'
  });
  
  return canResubmit;
}

// Export for testing
if (typeof module !== 'undefined') {
  module.exports = { testResubmitLogic };
}