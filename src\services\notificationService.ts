import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { useQuery, useMutation } from 'convex/react';
import { convex } from '@/lib/convex';
import { AppNotification } from '@/lib/types';

/**
 * Fetch notifications for the current user
 */
export async function getNotifications(): Promise<AppNotification[]> {
  try {
    const notifications = await convex.query(api.notifications.getUserNotifications, {});
    return notifications.map(mapConvexNotificationToNotification);
  } catch (error) {
    console.error('Error fetching notifications:', error);
    throw error;
  }
}

/**
 * Fetch unread notifications count for the current user
 */
export async function getUnreadNotificationsCount(): Promise<number> {
  try {
    return await convex.query(api.notifications.getUnreadCount, {});
  } catch (error) {
    console.error('Error fetching unread notifications count:', error);
    return 0;
  }
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(id: string): Promise<void> {
  try {
    await convex.mutation(api.notifications.markAsRead, {
      notificationId: id as Id<"notifications">,
    });
  } catch (error) {
    console.error('Error marking notification as read:', error);
    throw error;
  }
}

/**
 * Mark all notifications as read
 */
export async function markAllNotificationsAsRead(): Promise<void> {
  try {
    await convex.mutation(api.notifications.markAllAsRead, {});
  } catch (error) {
    console.error('Error marking all notifications as read:', error);
    throw error;
  }
}

/**
 * Create a notification
 */
export async function createNotification(notification: {
  title: string;
  message: string;
  type: string;
  recipientId: string;
  requestId?: string;
}): Promise<AppNotification> {
  try {
    // Note: This would typically be done server-side
    // For now, we'll use the system notification creation
    const notificationIds = await convex.mutation(api.notifications.createSystemNotification, {
      title: notification.title,
      message: notification.message,
      targetUserIds: [notification.recipientId as Id<"users">],
    });

    if (notificationIds.length === 0) {
      throw new Error('Failed to create notification');
    }

    const createdNotification = await convex.query(api.notifications.getNotification, {
      notificationId: notificationIds[0],
    });

    if (!createdNotification) {
      throw new Error('Failed to retrieve created notification');
    }

    return mapConvexNotificationToNotification(createdNotification);
  } catch (error) {
    console.error('Error creating notification:', error);
    throw error;
  }
}

/**
 * Delete a notification
 */
export async function deleteNotification(id: string): Promise<void> {
  try {
    await convex.mutation(api.notifications.deleteNotification, {
      notificationId: id as Id<"notifications">,
    });
  } catch (error) {
    console.error('Error deleting notification:', error);
    throw error;
  }
}

/**
 * Get notifications for a specific entity
 */
export async function getEntityNotifications(
  entityType: string,
  entityId: string
): Promise<AppNotification[]> {
  try {
    const notifications = await convex.query(api.notifications.getEntityNotifications, {
      entityType,
      entityId,
    });
    return notifications.map(mapConvexNotificationToNotification);
  } catch (error) {
    console.error('Error fetching entity notifications:', error);
    throw error;
  }
}

/**
 * Helper function to map a Convex notification to an AppNotification object
 */
function mapConvexNotificationToNotification(convexNotification: any): AppNotification {
  return {
    id: convexNotification._id,
    title: convexNotification.title,
    message: convexNotification.message,
    type: convexNotification.type,
    recipientId: convexNotification.userId,
    requestId: convexNotification.relatedEntityType === 'request' ? convexNotification.relatedEntityId : undefined,
    createdAt: new Date(convexNotification.createdAt),
    isRead: convexNotification.isRead,
  };
}

// React hooks for components (these provide real-time updates)
export const useNotifications = (limit?: number) => {
  return useQuery(api.notifications.getUserNotifications, { limit });
};

export const useUnreadNotificationsCount = () => {
  return useQuery(api.notifications.getUnreadCount, {});
};

export const useNotification = (notificationId: string) => {
  return useQuery(api.notifications.getNotification, {
    notificationId: notificationId as Id<"notifications">,
  });
};

export const useEntityNotifications = (entityType: string, entityId: string) => {
  return useQuery(api.notifications.getEntityNotifications, {
    entityType,
    entityId,
  });
};

export const useMarkAsRead = () => {
  return useMutation(api.notifications.markAsRead);
};

export const useMarkAllAsRead = () => {
  return useMutation(api.notifications.markAllAsRead);
};

export const useDeleteNotification = () => {
  return useMutation(api.notifications.deleteNotification);
};

export const useCreateSystemNotification = () => {
  return useMutation(api.notifications.createSystemNotification);
};
