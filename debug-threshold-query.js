/**
 * Debug script to query threshold settings from browser console
 * 
 * Instructions:
 * 1. Open your application in the browser
 * 2. Open Developer Tools (F12)
 * 3. Go to Console tab
 * 4. Copy and paste this code
 * 5. Press Enter to execute
 */

// Method 1: Using the global convex client (if available)
if (window.convex) {
  console.log('🔍 Querying threshold settings...');
  
  window.convex.query('debugQueries:getThresholdSettings', {})
    .then(result => {
      console.log('📊 THRESHOLD SETTINGS:', result);
      console.table(result.thresholdSettings);
      console.log('📋 SUMMARY:', result.summary);
    })
    .catch(error => {
      console.error('❌ Error querying thresholds:', error);
    });
} else {
  console.log('❌ Convex client not found in window object');
}

// Method 2: Using React DevTools (if you have React DevTools installed)
console.log(`
🔧 Alternative methods to view thresholds:

1. **Convex Dashboard:**
   - Go to https://dashboard.convex.dev
   - Select your project
   - Click "Data" → "settings" table
   - Look for keys: mobile_money_approval_threshold, bank_transfer_approval_threshold

2. **Settings Page:**
   - Navigate to /settings in your app
   - View "Approval Thresholds" section

3. **Database Query:**
   - Use the debug query: api.debugQueries.getThresholdSettings

4. **Direct Table View:**
   Table: settings
   Key: mobile_money_approval_threshold
   Key: bank_transfer_approval_threshold
`);
