import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient("https://efficient-toucan-547.convex.cloud");

async function initializeSystem() {
  console.log("🚀 Initializing MyBet Africa system...");
  
  try {
    // First, let's check what we can do without auth
    const health = await client.query("health:healthCheck", {});
    console.log("✅ System health:", health.status);
    
    const settingsCheck = await client.query("health:checkSettingsTable", {});
    console.log("📊 Settings table:", settingsCheck.settingsCount, "settings found");
    
    // Try to initialize default settings (this will require auth)
    console.log("\n🔧 Attempting to initialize default settings...");
    
    try {
      const initResult = await client.mutation("simpleInit:initializeDefaultSettings", {});
      console.log("✅ System initialized successfully!");
      console.log("📋 Initialization result:", initResult);
    } catch (authError) {
      if (authError.message.includes("Authentication required")) {
        console.log("🔐 Authentication required for initialization");
        console.log("💡 This is correct - only authenticated users can initialize the system");
        console.log("\n📝 Next steps:");
        console.log("   1. Set up authentication in your frontend");
        console.log("   2. Login as an admin user");
        console.log("   3. Run the initialization from the frontend");
      } else {
        console.log("❌ Initialization error:", authError.message);
      }
    }
    
    console.log("\n✅ System check completed!");
    return true;
    
  } catch (error) {
    console.log("❌ System check failed:", error.message);
    return false;
  }
}

initializeSystem();