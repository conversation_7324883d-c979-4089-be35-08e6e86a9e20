import { query } from "./_generated/server";
import { v } from "convex/values";
import { getCurrentUserWithPermissions } from "./permissions";

// Public queries that don't require Convex Auth (for WorkOS AuthKit integration)

export const getRequestsForUser = query({
  args: {
    workosUserId: v.string(),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
    if (!userWithPermissions) {
      return [];
    }

    // Get requests based on user permissions
    const hasViewAll = userWithPermissions.permissions.includes("request:view:all");
    const hasViewArea = userWithPermissions.permissions.includes("request:view:area");
    const hasViewOwn = userWithPermissions.permissions.includes("request:view:own");

    let requests = [];

    if (hasViewAll) {
      // Can view all requests
      requests = await ctx.db
        .query("requests")
        .order("desc")
        .take(args.limit || 50);
    } else if (hasViewArea) {
      // Can view requests in assigned areas
      const accessibleAreaIds = userWithPermissions.areaIds;
      if (accessibleAreaIds.length > 0) {
        requests = await ctx.db
          .query("requests")
          .filter((q) => {
            return q.or(
              ...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId))
            );
          })
          .order("desc")
          .take(args.limit || 50);
      }
    } else if (hasViewOwn) {
      // Can only view own requests
      requests = await ctx.db
        .query("requests")
        .withIndex("by_requested_by", (q) => q.eq("requestedBy", userWithPermissions.user._id))
        .order("desc")
        .take(args.limit || 50);
    }

    // Enrich requests with shop and area names
    const enrichedRequests = await Promise.all(
      requests.map(async (request) => {
        const shop = await ctx.db.get(request.shopId);
        const area = await ctx.db.get(request.areaId);
        
        return {
          ...request,
          shopName: shop?.name || 'Unknown Shop',
          areaName: area?.name || 'Unknown Area',
        };
      })
    );

    return enrichedRequests;
  },
});

export const getRequestStatsForUser = query({
  args: {
    workosUserId: v.string(),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
    if (!userWithPermissions) {
      return {
        total: 0,
        pending: 0,
        approved: 0,
        rejected: 0,
        paid: 0,
      };
    }

    // Get all requests the user can view
    const hasViewAll = userWithPermissions.permissions.includes("request:view:all");
    const hasViewArea = userWithPermissions.permissions.includes("request:view:area");
    const hasViewOwn = userWithPermissions.permissions.includes("request:view:own");

    let requests = [];

    if (hasViewAll) {
      requests = await ctx.db.query("requests").collect();
    } else if (hasViewArea) {
      const accessibleAreaIds = userWithPermissions.areaIds;
      if (accessibleAreaIds.length > 0) {
        requests = await ctx.db
          .query("requests")
          .filter((q) => {
            return q.or(
              ...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId))
            );
          })
          .collect();
      }
    } else if (hasViewOwn) {
      requests = await ctx.db
        .query("requests")
        .withIndex("by_requested_by", (q) => q.eq("requestedBy", userWithPermissions.user._id))
        .collect();
    }

    // Calculate stats
    const stats = {
      total: requests.length,
      pending: requests.filter(r => r.status === "pending").length,
      approved: requests.filter(r => r.status === "approved").length,
      rejected: requests.filter(r => r.status === "rejected").length,
      paid: requests.filter(r => r.status === "paid").length,
    };

    return stats;
  },
});

export const getShopsForUser = query({
  args: {
    workosUserId: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
    if (!userWithPermissions) {
      return [];
    }

    const hasShopView = userWithPermissions.permissions.includes("shop:view");
    if (!hasShopView) {
      return [];
    }

    // Get shops based on user permissions
    const hasViewAll = userWithPermissions.roles.some(role => role.name === "accounts");
    
    if (hasViewAll) {
      // Accounts can see all shops
      return await ctx.db
        .query("shops")
        .withIndex("by_active", (q) => q.eq("isActive", true))
        .take(args.limit || 50);
    } else {
      // Others can only see shops in their assigned areas
      const accessibleAreaIds = userWithPermissions.areaIds;
      if (accessibleAreaIds.length > 0) {
        return await ctx.db
          .query("shops")
          .filter((q) => {
            return q.and(
              q.eq(q.field("isActive"), true),
              q.or(
                ...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId))
              )
            );
          })
          .take(args.limit || 50);
      }
    }

    return [];
  },
});

export const getAreasForUser = query({
  args: {
    workosUserId: v.string(),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
    if (!userWithPermissions) {
      return [];
    }

    const hasAreaView = userWithPermissions.permissions.includes("area:view");
    if (!hasAreaView) {
      return [];
    }

    // Get areas based on user permissions
    const hasViewAll = userWithPermissions.roles.some(role => role.name === "accounts");
    
    if (hasViewAll) {
      // Accounts can see all areas
      return await ctx.db
        .query("areas")
        .withIndex("by_active", (q) => q.eq("isActive", true))
        .collect();
    } else {
      // Others can only see their assigned areas
      return userWithPermissions.areas;
    }
  },
});