
import React, { useEffect, Suspense } from 'react';
import { BrowserRouter as Router, Route, Routes, Navigate, Outlet } from 'react-router-dom';
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from './lib/auth-context';
// Removed old store imports - now using Convex queries that auto-fetch
import InvitationHandler from './components/auth/InvitationHandler';
import ProtectedRoute from './components/auth/ProtectedRoute';
import AuthCallback from './components/auth/AuthCallback';

// Lazy load components for better performance
const Login = React.lazy(() => import('./pages/Login'));
const CustomLogin = React.lazy(() => import('./pages/CustomLogin'));
const Dashboard = React.lazy(() => import('./pages/Dashboard'));
const Requests = React.lazy(() => import('./pages/Requests'));
const CreateRequest = React.lazy(() => import('./pages/CreateRequest'));
const RequestDetails = React.lazy(() => import('./pages/RequestDetails'));
const ResubmitRequest = React.lazy(() => import('./pages/ResubmitRequest'));
const EditRequest = React.lazy(() => import('./pages/EditRequest'));
const ShopsManagement = React.lazy(() => import('./pages/ShopsManagement'));
const UserManagement = React.lazy(() => import('./pages/UserManagement'));
const Settings = React.lazy(() => import('./pages/Settings'));
const Reports = React.lazy(() => import('./pages/Reports'));
const ShopManagerReport = React.lazy(() => import('./pages/ShopManagerReport'));
const SyncManagement = React.lazy(() => import('./pages/SyncManagement'));
const Profile = React.lazy(() => import('./pages/Profile'));
const PasswordReset = React.lazy(() => import('./pages/PasswordReset'));
const SetPassword = React.lazy(() => import('./pages/SetPassword'));
const Index = React.lazy(() => import('./pages/Index'));
const Register = React.lazy(() => import('./pages/Register'));
const TestPage = React.lazy(() => import('./pages/TestPage'));

// Loading component for suspense fallback
const PageLoader = () => (
  <div className="flex items-center justify-center h-screen">
    <div className="text-center">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
      <p className="text-muted-foreground">Loading...</p>
    </div>
  </div>
);

// Layout for authenticated users
function ProtectedLayout() {
  return (
    <ProtectedRoute>
      <Outlet />
    </ProtectedRoute>
  );
}

function AppRoutes() {
  return (
    <Suspense fallback={<PageLoader />}>
      <Routes>
        {/* Public routes */}
        <Route path="/login" element={<CustomLogin />} />
        <Route path="/login-old" element={<Login />} />
        <Route path="/register" element={<Register />} />
        <Route path="/password-reset" element={<PasswordReset />} />
        <Route path="/set-password/:token?" element={<SetPassword />} />
        <Route path="/auth/callback" element={<AuthCallback />} />
        <Route path="/index" element={<Index />} />
        <Route path="/test" element={<TestPage />} />
        <Route path="/" element={<Navigate to="/login" />} />

        {/* Protected routes */}
        <Route element={<ProtectedLayout />}>
          <Route path="/dashboard" element={<Dashboard />} />
          <Route path="/requests" element={<Requests />} />
          <Route path="/create-request" element={<CreateRequest />} />
          <Route path="/requests/:id" element={<RequestDetails />} />
          <Route path="/resubmit-request/:id" element={<ResubmitRequest />} />
          <Route path="/requests/:id/edit" element={<EditRequest />} />
          <Route path="/shops-management" element={<ShopsManagement />} />
          <Route path="/user-management" element={<UserManagement />} />
          <Route path="/settings" element={<Settings />} />
          <Route path="/reports" element={<Reports />} />
          <Route path="/shop-manager-report" element={<ShopManagerReport />} />
          <Route path="/sync-management" element={<SyncManagement />} />
          <Route path="/profile" element={<Profile />} />
        </Route>
      </Routes>
    </Suspense>
  );
}

// StoreInitializer removed - Convex queries automatically handle data fetching

function App() {
  return (
    <Router>
      <AuthProvider>
        <InvitationHandler />
        <AppRoutes />
        <Toaster />
      </AuthProvider>
    </Router>
  );
}

export default App;
