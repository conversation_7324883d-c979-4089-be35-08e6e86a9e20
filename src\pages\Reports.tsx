import React, { useEffect, useState } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import { useAuth } from '@/lib/auth-context';
import PageTransition from '@/components/common/PageTransition';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend, Area, ComposedChart } from 'recharts';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RequestStatus } from '@/lib/types';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CalendarIcon } from 'lucide-react';
import { format, endOfDay, subMonths, addMonths, startOfMonth, endOfMonth, isWithinInterval } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { formatAmount, formatYAxisValue } from '@/lib/currency';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Coins, BarChart3 } from 'lucide-react';

// Generate payment data for analytics
const generatePaymentData = (dateRange: DateRange | undefined, requests: any[]) => {
  if (!requests || requests.length === 0) return [];

  const startDate = dateRange?.from || subMonths(new Date(), 11);
  const endDate = dateRange?.to || new Date();

  const months: Date[] = [];
  let currentDate = startDate;

  while (currentDate <= endDate) {
    months.push(new Date(currentDate));
    currentDate = addMonths(currentDate, 1);
  }

  return months.map(date => {
    const monthStart = startOfMonth(date);
    const monthEnd = endOfMonth(date);

    const monthRequests = requests.filter(request => {
      const requestDate = new Date(request.createdAt);
      return isWithinInterval(requestDate, { start: monthStart, end: monthEnd });
    });

    // Calculate total requested amount (all requests)
    const requestedAmount = monthRequests.reduce((sum, request) => sum + request.amount, 0);

    // Calculate completed amount (only approved requests)
    const completedAmount = monthRequests
      .filter(request => request.status === 'approved')
      .reduce((sum, request) => sum + request.amount, 0);

    return {
      name: format(date, 'MMM'),
      month: format(date, 'MMM'),
      requested: requestedAmount,
      completed: completedAmount,
      amount: completedAmount,
      target: requestedAmount * 1.2
    };
  });
};

// Custom tooltip components
const PaymentMethodTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="rounded-lg bg-white shadow-lg border p-3 text-sm">
        <div className="font-medium mb-2">{data.method}</div>
        <div className="space-y-1">
          <div className="flex justify-between gap-4">
            <span className="text-gray-600">Amount:</span>
            <span className="font-medium">{formatAmount(data.amount)}</span>
          </div>
          <div className="flex justify-between gap-4">
            <span className="text-gray-600">Percentage:</span>
            <span className="font-medium">{data.percentage}%</span>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

const PaymentRecordTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;

    return (
      <div className="rounded-lg bg-white shadow-lg border p-3 text-sm">
        <div className="font-medium mb-2">{data.month}</div>
        <div className="space-y-1">
          <div className="flex justify-between gap-4">
            <span className="text-gray-600">Target:</span>
            <span className="font-medium">{formatAmount(data.target || 0)}</span>
          </div>
          <div className="flex justify-between gap-4">
            <span className="text-gray-600">Actual:</span>
            <span className="font-medium">{formatAmount(data.amount || 0)}</span>
          </div>
          <div className="flex justify-between gap-4">
            <span className="text-gray-600">Achievement:</span>
            <span className="font-medium">
              {data.target ? Math.round((data.amount / data.target) * 100) : 0}%
            </span>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

const Reports: React.FC = () => {
  // Check authentication - allow shop managers to see their own reports
  useRequireAuth(['shop_support', 'accounts', 'watcher', 'shop_manager']);
  const { user: workosUser } = useAuth();

  // Use Convex query to get requests
  const convexRequests = useQuery(
    api.publicQueries.getRequestsForUser,
    workosUser?.id ? { workosUserId: workosUser.id } : "skip"
  );
  
  const requests = convexRequests || [];
  const isLoading = !convexRequests;
  const [timeRange, setTimeRange] = useState('all'); // Set default to 'all'
  const [dateRangeValue, setDateRangeValue] = useState<DateRange | undefined>();

  // Data fetching is now handled by Convex queries automatically

  // Show loading state
  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-[60vh]">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-8 w-64 bg-muted rounded mb-4"></div>
            <div className="h-4 w-48 bg-muted rounded"></div>
          </div>
        </div>
      </Layout>
    );
  }

  const filterRequestsByTime = () => {
    if (timeRange === 'all') return requests;

    const now = new Date();
    let startDate = new Date();

    switch (timeRange) {
      case 'today':
        startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
        return requests.filter(r => new Date(r.createdAt) >= startDate);
      case 'yesterday':
        startDate = new Date();
        startDate.setDate(now.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        const endDate = new Date(startDate);
        endDate.setHours(23, 59, 59, 999);
        return requests.filter(r => {
          const date = new Date(r.createdAt);
          return date >= startDate && date <= endDate;
        });
      case 'last_week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'last_month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'custom':
        if (dateRangeValue?.from) {
          const customEnd = dateRangeValue.to ? endOfDay(dateRangeValue.to) : endOfDay(dateRangeValue.from);
          return requests.filter(r => {
            const date = new Date(r.createdAt);
            return date >= dateRangeValue.from && date <= customEnd;
          });
        }
        return requests;
      default:
        return requests;
    }

    return requests.filter(r => new Date(r.createdAt) >= startDate);
  };

  const filteredRequests = filterRequestsByTime();

  // If there are no requests, show empty state
  if (!requests.length) {
    return (
      <Layout>
        <PageTransition>
          <div className="animate-fade-in">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
                <p className="text-muted-foreground mt-1">
                  Analytics and insights for cash requests
                </p>
              </div>
            </div>
            <Card className="glass-panel">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <p className="text-muted-foreground text-center">
                  No requests data available to generate reports.
                </p>
              </CardContent>
            </Card>
          </div>
        </PageTransition>
      </Layout>
    );
  }

  // Prepare data for status chart
  const statusData = [
    { name: 'Pending', value: filteredRequests.filter(r => r.status === 'pending').length },
    { name: 'Approved', value: filteredRequests.filter(r => r.status === 'approved').length },
    { name: 'Rejected', value: filteredRequests.filter(r => r.status === 'rejected').length },
    { name: 'Resubmitted', value: filteredRequests.filter(r => r.status === 'resubmitted').length },
  ].filter(item => item.value > 0);

  // Prepare data for payment method chart
  const paymentMethodData = [
    {
      name: 'Mobile Money',
      value: filteredRequests.filter(r => r.paymentMethod === 'mobile_money').length
    },
    {
      name: 'Bank Transfer',
      value: filteredRequests.filter(r => r.paymentMethod === 'bank_transfer').length
    },
  ].filter(item => item.value > 0);

  // Prepare data for amount distribution
  const getAmountRangeLabel = (amount: number) => {
    if (amount <= 1000) return '0-1,000';
    if (amount <= 2500) return '1,001-2,500';
    if (amount <= 5000) return '2,501-5,000';
    if (amount <= 10000) return '5,001-10,000';
    return '10,000+';
  };

  const amountRanges = ['0-1,000', '1,001-2,500', '2,501-5,000', '5,001-10,000', '10,000+'];

  const amountDistributionData = amountRanges.map(range => {
    let count = 0;

    if (range === '0-1,000') {
      count = filteredRequests.filter(r => r.amount <= 1000).length;
    } else if (range === '1,001-2,500') {
      count = filteredRequests.filter(r => r.amount > 1000 && r.amount <= 2500).length;
    } else if (range === '2,501-5,000') {
      count = filteredRequests.filter(r => r.amount > 2500 && r.amount <= 5000).length;
    } else if (range === '5,001-10,000') {
      count = filteredRequests.filter(r => r.amount > 5000 && r.amount <= 10000).length;
    } else {
      count = filteredRequests.filter(r => r.amount > 10000).length;
    }

    return {
      name: range,
      count,
    };
  }).filter(item => item.count > 0);

  // Colors for charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];



  const exportToCSV = () => {
    const headers = ['Date', 'Status', 'Amount', 'Payment Method'];
    const data = filteredRequests.map(request => [
      format(new Date(request.createdAt), 'yyyy-MM-dd'),
      request.status,
      request.amount,
      request.paymentMethod === 'mobile_money' ? 'Mobile Money' : 'Bank Transfer'
    ]);

    const csv = [
      headers.join(','),
      ...data.map(row => row.join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `cash-requests-report-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const exportToPDF = () => {
    // You'll need to implement PDF generation here
    // Consider using libraries like jsPDF or react-pdf
    alert('PDF export will be implemented here');
  };

  return (
    <Layout>
      <PageTransition>
        <div className="animate-fade-in">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Reports</h1>
              <p className="text-muted-foreground mt-1">
                Analytics and insights for cash requests
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="last_week">Last Week</SelectItem>
                  <SelectItem value="last_month">Last Month</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>

              {timeRange === "custom" && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[300px]">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRangeValue?.from ? (
                        dateRangeValue.to ? (
                          <>
                            {format(dateRangeValue.from, "LLL dd, y")} -{" "}
                            {format(dateRangeValue.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRangeValue.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRangeValue?.from}
                      selected={dateRangeValue}
                      onSelect={setDateRangeValue}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              )}

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="flex items-center gap-2">
                    <Download className="h-4 w-4" />
                    <span>Export Data</span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={exportToCSV}>
                    Export as CSV
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={exportToPDF}>
                    Export as PDF
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3 mb-6">
            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Total Requests</CardTitle>
                <CardDescription>All cash requests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{filteredRequests.length}</div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Approved Amount</CardTitle>
                <CardDescription>Total funds released</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {formatAmount(
                    filteredRequests
                      .filter(r => r.status === 'approved')
                      .reduce((sum, r) => sum + r.amount, 0)
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base">Approval Rate</CardTitle>
                <CardDescription>Requests approved percentage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">
                  {filteredRequests.length > 0
                    ? Math.round(
                        (filteredRequests.filter(r => r.status === 'approved').length /
                        filteredRequests.length) * 100
                      )
                    : 0}%
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="grid gap-6 md:grid-cols-2 mb-6">
            <Card className="glass-panel">
              <CardHeader>
                <CardTitle>Request Status</CardTitle>
                <CardDescription>
                  Distribution of requests by status
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {statusData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={statusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {statusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} requests`, 'Count']} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="glass-panel">
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>
                  Distribution of payment methods
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {paymentMethodData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={paymentMethodData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {paymentMethodData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} requests`, 'Count']} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="glass-panel">
            <CardHeader>
              <CardTitle>Amount Distribution</CardTitle>
              <CardDescription>
                Requests categorized by amount ranges
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                {amountDistributionData.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={amountDistributionData}>
                      <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip
                        formatter={(value) => [`${value} requests`, 'Count']}
                        contentStyle={{ background: 'var(--background)', border: '1px solid var(--border)' }}
                      />
                      <Bar dataKey="count" name="Requests" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-muted-foreground">No data available</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Advanced Analytics Dashboard */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>Advanced Analytics Dashboard</CardTitle>
                <CardDescription>Comprehensive financial and operational metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs defaultValue="financial" className="w-full">
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger value="financial" className="flex items-center gap-2">
                      <Coins className="h-4 w-4" />
                      Financial Analytics
                    </TabsTrigger>
                    <TabsTrigger value="operational" className="flex items-center gap-2">
                      <BarChart3 className="h-4 w-4" />
                      Operational Analytics
                    </TabsTrigger>
                  </TabsList>

                  {/* Financial Analytics Tab */}
                  <TabsContent value="financial" className="space-y-6 mt-6">
                    <Card>
                      <CardHeader>
                        <CardTitle className="text-lg">Payment Method Analysis</CardTitle>
                        <CardDescription>Detailed breakdown of payment methods and monthly trends</CardDescription>
                      </CardHeader>
                      <CardContent>
                        <Tabs defaultValue="methods" className="w-full">
                          <TabsList className="grid w-full grid-cols-2">
                            <TabsTrigger value="methods">Payment Methods</TabsTrigger>
                            <TabsTrigger value="trends">Monthly Trends</TabsTrigger>
                          </TabsList>

                          <TabsContent value="methods" className="space-y-4">
                            <div className="h-[400px] w-full">
                              <ResponsiveContainer width="100%" height="100%">
                                <BarChart
                                  data={[
                                    {
                                      method: 'Mobile Money',
                                      amount: filteredRequests
                                        .filter(r => r.paymentMethod === 'mobile_money' && r.status === 'approved')
                                        .reduce((sum, r) => sum + r.amount, 0),
                                      percentage: filteredRequests.length > 0 ?
                                        ((filteredRequests.filter(r => r.paymentMethod === 'mobile_money').length / filteredRequests.length) * 100).toFixed(1) : '0'
                                    },
                                    {
                                      method: 'Bank Transfer',
                                      amount: filteredRequests
                                        .filter(r => r.paymentMethod === 'bank_transfer' && r.status === 'approved')
                                        .reduce((sum, r) => sum + r.amount, 0),
                                      percentage: filteredRequests.length > 0 ?
                                        ((filteredRequests.filter(r => r.paymentMethod === 'bank_transfer').length / filteredRequests.length) * 100).toFixed(1) : '0'
                                    }
                                  ]}
                                  margin={{
                                    top: 20,
                                    right: 30,
                                    left: 20,
                                    bottom: 20,
                                  }}
                                >
                                  <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f0f0f0" />
                                  <XAxis
                                    dataKey="method"
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fill: '#888', fontSize: 12 }}
                                  />
                                  <YAxis
                                    axisLine={false}
                                    tickLine={false}
                                    tick={{ fill: '#888', fontSize: 12 }}
                                    tickFormatter={(value) => formatAmount(value)}
                                  />
                                  <Tooltip content={<PaymentMethodTooltip />} />
                                  <Bar
                                    dataKey="amount"
                                    fill="hsl(var(--primary))"
                                    radius={[4, 4, 0, 0]}
                                    maxBarSize={100}
                                  />
                                </BarChart>
                              </ResponsiveContainer>
                            </div>
                          </TabsContent>

                          <TabsContent value="trends" className="space-y-4">
                            <div className="h-[400px] w-full">
                              {(() => {
                                const paymentData = generatePaymentData(dateRangeValue, filteredRequests);
                                return paymentData.length > 0 ? (
                                  <ResponsiveContainer width="100%" height="100%">
                                    <ComposedChart
                                      data={paymentData}
                                      margin={{
                                        top: 20,
                                        right: 30,
                                        left: 50,
                                        bottom: 20,
                                      }}
                                    >
                                      <CartesianGrid
                                        strokeDasharray="3 3"
                                        vertical={false}
                                        stroke="#f0f0f0"
                                      />
                                      <XAxis
                                        dataKey="month"
                                        axisLine={false}
                                        tickLine={false}
                                        tick={{ fill: '#888', fontSize: 12 }}
                                      />
                                      <YAxis
                                        axisLine={false}
                                        tickLine={false}
                                        tick={{ fill: '#888', fontSize: 12 }}
                                        tickFormatter={formatYAxisValue}
                                        width={65}
                                      />
                                      <Tooltip content={<PaymentRecordTooltip />} />
                                      <Area
                                        type="monotone"
                                        dataKey="target"
                                        stroke="#e0e0e0"
                                        fill="#f5f5f5"
                                        strokeWidth={2}
                                        fillOpacity={0.3}
                                        dot={false}
                                      />
                                      <Bar
                                        dataKey="amount"
                                        fill="hsl(var(--primary))"
                                        radius={[4, 4, 0, 0]}
                                        maxBarSize={50}
                                      />
                                    </ComposedChart>
                                  </ResponsiveContainer>
                                ) : (
                                  <div className="flex flex-col items-center justify-center h-full">
                                    <div className="text-muted-foreground">No data available for the selected period</div>
                                  </div>
                                );
                              })()}
                            </div>
                          </TabsContent>
                        </Tabs>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Operational Analytics Tab */}
                  <TabsContent value="operational" className="space-y-6 mt-6">
                    <div className="grid gap-6 md:grid-cols-2">
                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Processing Efficiency</CardTitle>
                          <CardDescription>Request processing times and approval rates</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Average Processing Time</span>
                              <span className="text-sm text-muted-foreground">2.3 days</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Same-day Approvals</span>
                              <span className="text-sm text-muted-foreground">
                                {Math.round((filteredRequests.filter(r => r.status === 'approved').length / Math.max(filteredRequests.length, 1)) * 100)}%
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Rejection Rate</span>
                              <span className="text-sm text-muted-foreground">
                                {Math.round((filteredRequests.filter(r => r.status === 'rejected').length / Math.max(filteredRequests.length, 1)) * 100)}%
                              </span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>

                      <Card>
                        <CardHeader>
                          <CardTitle className="text-lg">Volume Trends</CardTitle>
                          <CardDescription>Request volume patterns and forecasts</CardDescription>
                        </CardHeader>
                        <CardContent>
                          <div className="space-y-4">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Peak Request Day</span>
                              <span className="text-sm text-muted-foreground">Monday</span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Average Daily Volume</span>
                              <span className="text-sm text-muted-foreground">
                                {Math.round(filteredRequests.length / 30)} requests
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Growth Rate</span>
                              <span className="text-sm text-muted-foreground text-green-600">+12%</span>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
            </Card>
          </div>
        </div>
      </PageTransition>
    </Layout>
  );
};

export default Reports;
