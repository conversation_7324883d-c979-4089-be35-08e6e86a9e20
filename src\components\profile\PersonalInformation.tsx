import React, { useState, useEffect } from 'react';
import { Camera } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { useAuth } from '@/lib/auth-context';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { toast } from '@/components/ui/use-toast';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

const PersonalInformation: React.FC = () => {
  const { user, userWithRoles, appUser } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const updateProfileMutation = useMutation(api.users.updateCurrentUserProfile);

  // Debug logging
  console.log('PersonalInformation Debug:', {
    user: !!user,
    userWithRoles: !!userWithRoles,
    appUser: !!appUser,
    appUserData: appUser,
    workosUserId: user?.id
  });

  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    jobTitle: '',
    avatar: ''
  });

  // Update form data when user data changes
  useEffect(() => {
    if (appUser) {
      setFormData({
        firstName: appUser.firstName || '',
        lastName: appUser.lastName || '',
        email: appUser.email || '',
        phone: appUser.phone || '',
        jobTitle: userWithRoles?.roles?.[0]?.name || '',
        avatar: appUser.profilePicture || ''
      });
    }
  }, [appUser, userWithRoles]);

  // Show loading state if user data isn't available yet
  if (!user) {
    console.log('PersonalInformation: No user, showing loading');
    return (
      <div className="flex items-center justify-center h-32">
        <div className="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-primary"></div>
        <span className="ml-2">Loading user data...</span>
      </div>
    );
  }

  console.log('PersonalInformation: Rendering form');

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 5MB.",
          variant: "destructive"
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file.",
          variant: "destructive"
        });
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setFormData(prev => ({ ...prev, avatar: result }));
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Prepare updates - only include changed fields
      const updates: any = {};

      if (formData.firstName !== (appUser?.firstName || '')) {
        updates.firstName = formData.firstName;
      }
      if (formData.lastName !== (appUser?.lastName || '')) {
        updates.lastName = formData.lastName;
      }
      if (formData.phone !== (appUser?.phone || '')) {
        updates.phone = formData.phone;
      }
      if (formData.avatar !== (appUser?.profilePicture || '')) {
        updates.profilePicture = formData.avatar;
      }

      // Add WorkOS user ID for authentication
      if (user?.id) {
        updates.workosUserId = user.id;
      }

      if (Object.keys(updates).length === 0) {
        toast({
          title: "No changes",
          description: "No changes were made to your profile.",
        });
        return;
      }

      const result = await updateProfileMutation(updates);

      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully",
      });

      console.log('Profile update result:', result);
    } catch (error: any) {
      console.error('Profile update error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update profile. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  try {
    return (
      <form onSubmit={handleSubmit} className="space-y-6">
      <div className="flex justify-center">
        <div className="relative group">
          <Avatar className="h-32 w-32">
            <AvatarImage src={formData.avatar} />
            <AvatarFallback className="text-2xl">
              {formData.firstName.charAt(0)}{formData.lastName.charAt(0)}
            </AvatarFallback>
          </Avatar>
          <label
            htmlFor="avatar-upload"
            className="absolute inset-0 flex items-center justify-center bg-black/50 rounded-full opacity-0 group-hover:opacity-100 cursor-pointer transition-opacity"
          >
            <Camera className="h-8 w-8 text-white" />
          </label>
          <input
            id="avatar-upload"
            type="file"
            accept="image/*"
            className="hidden"
            onChange={handleImageUpload}
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            value={formData.firstName}
            onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            value={formData.lastName}
            onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
            disabled={isLoading}
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="email">Email</Label>
        <Input
          id="email"
          type="email"
          value={formData.email}
          onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
          disabled={isLoading}
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <Input
            id="phone"
            value={formData.phone}
            onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
            disabled={isLoading}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="jobTitle">Job Title</Label>
          <Input
            id="jobTitle"
            value={formData.jobTitle}
            disabled={true}
          />
        </div>
      </div>

      <Button
        type="submit"
        className="w-full sm:w-auto"
        disabled={isLoading}
      >
        Save Changes
      </Button>
    </form>
    );
  } catch (error) {
    console.error('PersonalInformation render error:', error);
    return (
      <div className="flex items-center justify-center h-32">
        <div className="text-center">
          <p className="text-red-500">Error loading personal information</p>
          <p className="text-sm text-muted-foreground mt-2">{String(error)}</p>
        </div>
      </div>
    );
  }
};

export default PersonalInformation;