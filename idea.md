#### Key Features:
- Shop Manager:
	- Create new requests.
	- Resubmit requests.
	- View own submissions.
	- View and export reports and analytics.
- Shop Support:
	- View all requests from shop managers.
	- Approve or reject requests within a specified threshold.
	- Approve requests only with Momo payment method.
	- View and export reports and analytics.

- Watchers:
	- View all submissions.
	- View and export reports and analytics.
	- No submission or approval rights.
- Administrator (Super User):
	- System-wide access with no limitations.

#### 1. Notifications:
- In-app and email notifications for significant events (e.g., approval, rejection, additional information requests).
- Define which user groups receive notifications for specific events.
- Default to both in-app and email notifications. Users should be able to select their preferred notification method in their profile settings.

#### 2. Approval Workflow:
- Approval Form Fields:
	- Ticket Verification:
		- Ticket Status Confirmation (checkbox: confirm ticket status is "Won" or "Payout for Virtuals").
		- Manager Verification (checkbox: confirm shop manager verification).
	- Customer Mobile Money Verification:
		- Verify But<PERSON>: Confirm customer's MoMo account name with provider.
		- Verified MoMo/Bank Name (text input). Displays the verified Momo/Bank name when the Verified button is clicked.
	- Approval Actions:
		- Reject <PERSON> (red): Requires a reason for rejection before submission.
		- Approve <PERSON> (green): Optional comment for approval.
- Resubmission Process:
	- Track original submission and changes made with resubmission.
	- Include change notes from shop manager.
	- Clear communication of required additional information for resubmission.
- Rejection Process:
	- Must provide a reason for rejection before submission.
	- Communicate required additional information for resubmission.

    ### 3. Request Submission and Workflow:
- New Request Page:
	- Form fields:
		- Shop Details:
			- Area Name (dropdown, pre-populated based on authenticated user's area).
			- Shop Name (dropdown, pre-populated based on authenticated user's shop).
		- Confirmation Checklist:
			- Customer Agreement Confirmation (checkbox).
			- Assurance Officer Consent Confirmation (checkbox).
		- Customer Details:
			- Ticket ID (text).
			- Product (dropdown: Sportbook, Golden Race, IBet).
			- Payment Method (radio button: Momo, Bank Account).
			- Requesting Amount (numeric, validation based on threshold settings).
			- Customer MoMo Number (text, format validation, if Momo is selected).
			- Customer MoMo Full Name (text, if Momo is selected).
			- Network Provider (dropdown: MTN, Telecel, AT, if Momo is selected).
			- Customer Account Number (text, format validation, if Bank Account is selected).
			- Customer Account Full Name (text, if Bank Account is selected).
			- Bank (dropdown: STANDARD CHARTERED BANK, ABSA BANK GHANA LIMITED, GCB BANK LIMITED, NATIONAL INVESTMENT BANK, ARB APEX BANK LIMITED, AGRICULTURAL DEVELOPMENT BANK, UNIVERSAL MERCHANT BANK, REPUBLIC BANK LIMITED, ZENITH BANK GHANA LTD, ECOBANK GHANA LTD, CAL BANK LIMITED, FIRST ATLANTIC BANK, PRUDENTIAL BANK LTD, STANBIC BANK, FIRST BANK OF NIGERIA, BANK OF AFRICA, GUARANTY TRUST BANK, FIDELITY BANK LIMITED, SAHEL - SAHARA BANK (BSIC), UNITED BANK OF AFRICA, ACCESS BANK LTD, CONSOLIDATED BANK GHANA, FIRST NATIONAL BANK, GHL BANK), if Bank Account is selected.
			- Image attachment (optional).
				- Enforce file size and type restrictions for image attachments in the request form. Only JPEG or PNG files with a maximum size of 5MB are allowed.
		- Additional Information:
			- Comments (text area, optional).
	- Submit Request Button (green, completes form submission).
	- Validations:
		- All required fields must be filled.
		- Format validation for mobile number and account number fields.
- Recent Requests Page:
	- Show recent requests made by shop managers.
	- Filter by status: Pending, Resubmitted, Rejected (no approved status in history).
- Review Requests Page:
	- Show all pending and resubmitted requests with no approved status in history.
	- Advance filter options: Shop Name, Area Name, Network Provider, Date Range.
- All Requests Page:
	- Show each request with chronological history in a timeline.
	- Advance filter options: Status, Ticket ID, Customer MoMo Number, Customer MoMo Name, Date Range.

#### 4. User Management:
- Adminstrator Access:
	- Add new users.
	- Invite new users via email.
	- Remove users.
	- Assign users to groups.
	- Define access policies.
	- Disable/enable users.
	- Create new user groups.
	- Add access rights to groups.
	- There are no limits on user management or other functionalities unless specified.
	- Administrators should be able to manage access with an expiry date, allowing them to set the validity period for user roles and permissions.

- Sign-In:
	- Login with allowable email addresses.
	- Password reset functionality.
	- Remember me functionality.

    #### 6. Reports and Analytics:
- Provide insights into request metrics, review times, approval rates, and user activity.
- Export request data as CSV or PDF for external analysis.
- Provide predefined reports for now, offering insights into request metrics, review times, approval rates, and user activity. These reports should be exportable as CSV or PDF.

#### 7. Profile Settings:
- Allow users to select their notification preferences (in-app, email, or both) in their profile settings.
