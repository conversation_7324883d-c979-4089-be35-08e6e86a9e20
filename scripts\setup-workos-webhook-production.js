#!/usr/bin/env node

/**
 * WorkOS Webhook Setup Script for Production
 * 
 * This script helps configure the WorkOS webhook endpoint properly
 * and provides instructions for manual setup in the WorkOS dashboard.
 */

const https = require('https');

// Configuration
const WEBHOOK_URL = 'https://efficient-toucan-547.convex.cloud/http/workos-webhook';
const REQUIRED_EVENTS = [
  'user.created',
  'user.updated', 
  'user.deleted',
  'organization.created',
  'organization.updated',
  'organization.deleted',
  'organization_membership.created',
  'organization_membership.updated',
  'organization_membership.deleted'
];

console.log('🔧 WorkOS Webhook Setup for Production');
console.log('=====================================\n');

console.log('📋 **MANUAL SETUP REQUIRED**');
console.log('Please follow these steps in the WorkOS Dashboard:\n');

console.log('1. 🌐 **Go to WorkOS Dashboard**');
console.log('   → https://dashboard.workos.com/\n');

console.log('2. 📡 **Navigate to Webhooks**');
console.log('   → Click on "Webhooks" in the sidebar\n');

console.log('3. ➕ **Create New Webhook**');
console.log('   → Click "Add Endpoint" or "Create Webhook"\n');

console.log('4. 🔗 **Configure Webhook URL**');
console.log(`   → URL: ${WEBHOOK_URL}`);
console.log('   → ⚠️  IMPORTANT: Include the /http/ prefix!\n');

console.log('5. 📅 **Select Events**');
console.log('   → Subscribe to these events:');
REQUIRED_EVENTS.forEach(event => {
  console.log(`     ✅ ${event}`);
});
console.log('');

console.log('6. 🔐 **Copy Webhook Secret**');
console.log('   → After creating the webhook, copy the generated secret');
console.log('   → Run this command to set it in Convex:');
console.log('   → npx convex env set WORKOS_WEBHOOK_SECRET "your_webhook_secret_here"\n');

console.log('7. 🧪 **Test the Webhook**');
console.log('   → Use the "Send test event" button in WorkOS Dashboard');
console.log('   → Send a test "user.created" event');
console.log('   → Verify it appears in Convex logs: npx convex logs\n');

console.log('🔍 **Verification Steps**');
console.log('========================\n');

console.log('After setup, verify the webhook is working:');
console.log('');
console.log('1. Check webhook endpoint is accessible:');
console.log(`   curl -X GET ${WEBHOOK_URL}`);
console.log('');
console.log('2. Check Convex environment variables:');
console.log('   npx convex env list');
console.log('');
console.log('3. Monitor webhook deliveries:');
console.log('   npx convex logs --tail');
console.log('');
console.log('4. Test organization membership sync:');
console.log('   → Create a user in WorkOS with a role');
console.log('   → Verify the role appears in Convex');
console.log('');

// Test webhook endpoint accessibility
console.log('🌐 **Testing Webhook Endpoint Accessibility**');
console.log('==============================================\n');

const url = new URL(WEBHOOK_URL);
const options = {
  hostname: url.hostname,
  port: 443,
  path: url.pathname,
  method: 'GET',
  timeout: 5000
};

const req = https.request(options, (res) => {
  console.log(`✅ Webhook endpoint is accessible`);
  console.log(`   Status: ${res.statusCode}`);
  console.log(`   Headers: ${JSON.stringify(res.headers, null, 2)}`);
  
  if (res.statusCode === 405) {
    console.log('   ✅ Expected 405 (Method Not Allowed) for GET request');
    console.log('   ✅ Endpoint is properly configured for POST requests');
  }
});

req.on('error', (error) => {
  console.log(`❌ Webhook endpoint test failed: ${error.message}`);
  console.log('   Please check your Convex deployment and HTTP routes');
});

req.on('timeout', () => {
  console.log(`❌ Webhook endpoint test timed out`);
  console.log('   Please check your Convex deployment');
  req.destroy();
});

req.end();

console.log('\n🎯 **Next Steps**');
console.log('================\n');
console.log('1. Complete the manual setup in WorkOS Dashboard');
console.log('2. Set the webhook secret in Convex environment');
console.log('3. Test with a real user invitation');
console.log('4. Monitor the sync process');
console.log('');
console.log('📚 For more details, see:');
console.log('   → WEBHOOK_SETUP_INSTRUCTIONS.md');
console.log('   → WORKOS_WEBHOOK_CONFIGURATION.md');