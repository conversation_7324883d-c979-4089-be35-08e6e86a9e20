
import React from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import RequestForm from '@/components/requests/RequestForm';
import PageTransition from '@/components/common/PageTransition';

const CreateRequest: React.FC = () => {
  // STEP 2: Restore role restriction now that we fixed the role detection
  const { user, isAuthorized, isLoading } = useRequireAuth(['shop_manager']);
  
  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p>Loading...</p>
          </div>
        </div>
      </Layout>
    );
  }
  
  if (!isAuthorized) {
    return (
      <Layout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <p>You don't have permission to access this page.</p>
          </div>
        </div>
      </Layout>
    );
  }
  
  return (
    <Layout>
      <PageTransition>
        <RequestForm />
      </PageTransition>
    </Layout>
  );
};

export default CreateRequest;
