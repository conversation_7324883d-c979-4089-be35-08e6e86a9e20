import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { useQuery, useMutation } from 'convex/react';
import { convex } from '@/lib/convex';
import { Request, RequestStatus, PaymentMethod } from '@/lib/types';

/**
 * Fetch all requests
 * Filters based on user role and permissions
 */
export async function getRequests(): Promise<Request[]> {
  try {
    const requests = await convex.query(api.requests.getRequests, {});
    return requests.map(mapConvexRequestToRequest);
  } catch (error) {
    console.error('Error fetching requests:', error);
    throw error;
  }
}

/**
 * Fetch a request by ID
 */
export async function getRequestById(id: string): Promise<Request | null> {
  try {
    const request = await convex.query(api.requests.getRequest, { 
      requestId: id as Id<"requests"> 
    });
    
    if (!request) {
      return null;
    }
    
    return mapConvexRequestToRequest(request);
  } catch (error) {
    console.error('Error fetching request by ID:', error);
    throw error;
  }
}

/**
 * Create a new request
 */
export async function createRequest(requestData: {
  title: string;
  shopId: string;
  shopName: string;
  amount: number;
  paymentMethod: PaymentMethod;
  customerName?: string;
  customerContact?: string;
  ticketImage?: string;
  comments?: string;
  areaId?: string;
  ticketId?: string;
  productId?: string;
  networkProvider?: string;
  bankName?: string;
  accountNumber?: string;
  accountHolderName?: string;
}): Promise<Request> {
  try {
    const requestId = await convex.mutation(api.requests.createRequest, {
      ticketNumber: requestData.ticketId || `TKT-${Date.now()}`,
      amount: requestData.amount,
      paymentMethod: requestData.paymentMethod === 'mobile_money' ? 'mobile_money' : 'bank_transfer',
      priority: 'medium',
      customerName: requestData.customerName || '',
      customerPhone: requestData.customerContact || '',
      mobileMoneyNumber: requestData.paymentMethod === 'mobile_money' ? requestData.customerContact : undefined,
      mobileMoneyProvider: requestData.networkProvider,
      bankName: requestData.bankName,
      accountNumber: requestData.accountNumber,
      accountName: requestData.accountHolderName,
      shopId: requestData.shopId as Id<"shops">,
      notes: requestData.comments,
    });

    const createdRequest = await convex.query(api.requests.getRequest, { 
      requestId 
    });
    
    if (!createdRequest) {
      throw new Error('Failed to retrieve created request');
    }
    
    return mapConvexRequestToRequest(createdRequest);
  } catch (error) {
    console.error('Error creating request:', error);
    throw error;
  }
}

/**
 * Update a request
 */
export async function updateRequest(id: string, updates: Partial<Request>): Promise<Request> {
  try {
    // Note: Convex doesn't have a direct update mutation for requests
    // This would need to be implemented based on specific update requirements
    throw new Error('Request updates not implemented - use specific operations like approve/reject');
  } catch (error) {
    console.error('Error updating request:', error);
    throw error;
  }
}

/**
 * Approve a request
 */
export async function approveRequest(id: string, approvalReason: string): Promise<Request> {
  try {
    await convex.mutation(api.requests.approveRequest, {
      requestId: id as Id<"requests">,
      notes: approvalReason,
    });

    const updatedRequest = await convex.query(api.requests.getRequest, { 
      requestId: id as Id<"requests"> 
    });
    
    if (!updatedRequest) {
      throw new Error('Failed to retrieve approved request');
    }
    
    return mapConvexRequestToRequest(updatedRequest);
  } catch (error) {
    console.error('Error approving request:', error);
    throw error;
  }
}

/**
 * Reject a request
 */
export async function rejectRequest(id: string, rejectionReason: string): Promise<Request> {
  try {
    await convex.mutation(api.requests.rejectRequest, {
      requestId: id as Id<"requests">,
      reason: rejectionReason,
    });

    const updatedRequest = await convex.query(api.requests.getRequest, { 
      requestId: id as Id<"requests"> 
    });
    
    if (!updatedRequest) {
      throw new Error('Failed to retrieve rejected request');
    }
    
    return mapConvexRequestToRequest(updatedRequest);
  } catch (error) {
    console.error('Error rejecting request:', error);
    throw error;
  }
}

/**
 * Resubmit a rejected request
 */
export async function resubmitRequest(
  id: string,
  updates: Partial<Request>,
  notes: string
): Promise<Request> {
  try {
    const newRequestId = await convex.mutation(api.requests.resubmitRequest, {
      originalRequestId: id as Id<"requests">,
      ticketNumber: updates.ticketId || `TKT-${Date.now()}`,
      amount: updates.amount || 0,
      paymentMethod: updates.paymentMethod === 'mobile_money' ? 'mobile_money' : 'bank_transfer',
      priority: 'medium',
      customerName: updates.customerName || '',
      customerPhone: updates.customerContact || '',
      mobileMoneyNumber: updates.paymentMethod === 'mobile_money' ? updates.customerContact : undefined,
      mobileMoneyProvider: updates.networkProvider,
      bankName: updates.bankName,
      accountNumber: updates.accountNumber,
      accountName: updates.accountHolderName,
      notes: updates.comments,
      resubmissionReason: notes,
    });

    const resubmittedRequest = await convex.query(api.requests.getRequest, { 
      requestId: newRequestId 
    });
    
    if (!resubmittedRequest) {
      throw new Error('Failed to retrieve resubmitted request');
    }
    
    return mapConvexRequestToRequest(resubmittedRequest);
  } catch (error) {
    console.error('Error resubmitting request:', error);
    throw error;
  }
}

/**
 * Delete a request
 */
export async function deleteRequest(id: string): Promise<void> {
  try {
    await convex.mutation(api.requests.cancelRequest, {
      requestId: id as Id<"requests">,
      reason: 'Request deleted by user',
    });
  } catch (error) {
    console.error('Error deleting request:', error);
    throw error;
  }
}

/**
 * Mark request as paid
 */
export async function markRequestAsPaid(id: string, notes?: string): Promise<Request> {
  try {
    await convex.mutation(api.requests.markAsPaid, {
      requestId: id as Id<"requests">,
      notes,
    });

    const updatedRequest = await convex.query(api.requests.getRequest, { 
      requestId: id as Id<"requests"> 
    });
    
    if (!updatedRequest) {
      throw new Error('Failed to retrieve paid request');
    }
    
    return mapConvexRequestToRequest(updatedRequest);
  } catch (error) {
    console.error('Error marking request as paid:', error);
    throw error;
  }
}

/**
 * Get request statistics
 */
export async function getRequestStats(filters?: {
  areaId?: string;
  shopId?: string;
  startDate?: Date;
  endDate?: Date;
}): Promise<any> {
  try {
    return await convex.query(api.requests.getRequestStats, {
      areaId: filters?.areaId as Id<"areas"> | undefined,
      shopId: filters?.shopId as Id<"shops"> | undefined,
      startDate: filters?.startDate?.getTime(),
      endDate: filters?.endDate?.getTime(),
    });
  } catch (error) {
    console.error('Error fetching request stats:', error);
    throw error;
  }
}

/**
 * Helper function to map a Convex request to a Request object
 */
function mapConvexRequestToRequest(convexRequest: any): Request {
  return {
    id: convexRequest._id,
    title: convexRequest.ticketNumber,
    shopId: convexRequest.shopId,
    shopName: convexRequest.shop?.name || '',
    amount: convexRequest.amount,
    paymentMethod: convexRequest.paymentMethod,
    status: convexRequest.status,
    createdBy: convexRequest.requestedBy,
    createdAt: new Date(convexRequest.createdAt),
    updatedAt: new Date(convexRequest.updatedAt),
    approvedBy: convexRequest.approvedBy,
    approvedAt: convexRequest.approvedAt ? new Date(convexRequest.approvedAt) : undefined,
    approvalReason: convexRequest.notes,
    rejectedBy: convexRequest.rejectedBy,
    rejectedAt: convexRequest.rejectedAt ? new Date(convexRequest.rejectedAt) : undefined,
    rejectionReason: convexRequest.rejectionReason,
    customerName: convexRequest.customerName,
    customerContact: convexRequest.customerPhone,
    ticketImage: convexRequest.ticketImageId,
    comments: convexRequest.notes,
    areaId: convexRequest.areaId,
    ticketId: convexRequest.ticketNumber,
    productId: undefined, // Not mapped in Convex schema
    networkProvider: convexRequest.mobileMoneyProvider,
    bankName: convexRequest.bankName,
    accountNumber: convexRequest.accountNumber,
    accountHolderName: convexRequest.accountName,
    resubmissionHistory: convexRequest.resubmissionHistory?.map((entry: any) => ({
      timestamp: new Date(entry.resubmittedAt),
      changes: {}, // Would need to be implemented based on requirements
      notes: entry.reason,
      previousRejectionReason: entry.previousStatus,
    })),
  };
}

// React hooks for components
export const useRequests = (filters?: any) => {
  return useQuery(api.requests.getRequests, filters || {});
};

export const useRequest = (requestId: string) => {
  return useQuery(api.requests.getRequest, { 
    requestId: requestId as Id<"requests"> 
  });
};

export const useCreateRequest = () => {
  return useMutation(api.requests.createRequest);
};

export const useApproveRequest = () => {
  return useMutation(api.requests.approveRequest);
};

export const useRejectRequest = () => {
  return useMutation(api.requests.rejectRequest);
};
