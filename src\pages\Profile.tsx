import React, { useState } from 'react';
import Layout from '@/components/layout/Layout';
import { useAuth } from '@/lib/auth-context';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import PersonalInformation from '@/components/profile/PersonalInformation';
import ChangePassword from '@/components/profile/ChangePassword';

const Profile: React.FC = () => {
  const { user, isLoading, appUser } = useAuth();

  // Debug logging
  console.log('Profile Component Debug:', {
    user: !!user,
    isLoading,
    appUser: !!appUser,
    userEmail: user?.email
  });

  // Show loading state while auth is being determined
  if (isLoading) {
    console.log('Profile: Showing loading state');
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div>
          <span className="ml-2">Loading profile...</span>
        </div>
      </Layout>
    );
  }

  // If no user after loading, show a message instead of returning null
  if (!user) {
    console.log('Profile: No user found');
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-muted-foreground">No user session found</p>
            <p className="text-sm text-muted-foreground mt-2">Please try logging in again</p>
          </div>
        </div>
      </Layout>
    );
  }

  console.log('Profile: Rendering main content');

  try {
    return (
      <Layout>
        <div className="max-w-3xl mx-auto">
            <h1 className="text-2xl font-semibold mb-2">Profile</h1>
            <p className="text-muted-foreground mb-6">
              Manage your account settings and preferences
            </p>

            <Tabs defaultValue="personal" className="w-full">
              <TabsList className="w-full border-b mb-6">
                <TabsTrigger
                  value="personal"
                  className="flex-1 text-base"
                >
                  PERSONAL INFORMATION
                </TabsTrigger>
                <TabsTrigger
                  value="password"
                  className="flex-1 text-base"
                >
                  CHANGE PASSWORD
                </TabsTrigger>
              </TabsList>

              <TabsContent value="personal">
                <PersonalInformation />
              </TabsContent>

              <TabsContent value="password">
                <ChangePassword />
              </TabsContent>
            </Tabs>
        </div>
      </Layout>
    );
  } catch (error) {
    console.error('Profile render error:', error);
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <p className="text-red-500">Error loading profile</p>
            <p className="text-sm text-muted-foreground mt-2">{String(error)}</p>
          </div>
        </div>
      </Layout>
    );
  }
};

export default Profile;
