import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Debug query to check user role assignments
export const getUserRoleAssignments = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const userRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .collect();

    const roleDetails = [];
    for (const userRole of userRoles) {
      const role = await ctx.db.get(userRole.roleId);
      roleDetails.push({
        userRoleId: userRole._id,
        roleId: userRole.roleId,
        roleName: role?.name,
        assignedAt: userRole.assignedAt,
        assignedBy: userRole.assignedBy,
        isActive: userRole.isActive
      });
    }

    return roleDetails;
  },
});

// Debug query to check all user roles in the system
export const getAllUserRoles = query({
  args: {},
  handler: async (ctx) => {
    const userRoles = await ctx.db.query("user_roles").collect();
    
    const roleDetails = [];
    for (const userRole of userRoles) {
      const user = await ctx.db.get(userRole.userId);
      const role = await ctx.db.get(userRole.roleId);
      roleDetails.push({
        userRoleId: userRole._id,
        userId: userRole.userId,
        userEmail: user?.email,
        roleId: userRole.roleId,
        roleName: role?.name,
        assignedAt: userRole.assignedAt,
        isActive: userRole.isActive
      });
    }

    return roleDetails;
  },
});

// Debug query to check all roles in the system
export const getAllRoles = query({
  args: {},
  handler: async (ctx) => {
    const roles = await ctx.db.query("roles").collect();
    return roles.map(role => ({
      id: role._id,
      name: role.name,
      description: role.description,
      permissions: role.permissions,
      isActive: role.isActive
    }));
  },
});

// Debug query to check invitations
export const getInvitations = query({
  args: { email: v.optional(v.string()) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("invitations");
    
    if (args.email) {
      query = query.withIndex("by_email", (q) => q.eq("email", args.email));
    }
    
    const invitations = await query.collect();
    
    const invitationDetails = [];
    for (const invitation of invitations) {
      const role = await ctx.db.get(invitation.roleId);
      const invitedBy = await ctx.db.get(invitation.invitedBy);
      
      invitationDetails.push({
        id: invitation._id,
        email: invitation.email,
        status: invitation.status,
        roleId: invitation.roleId,
        roleName: role?.name,
        invitedBy: invitedBy?.email,
        invitedAt: invitation.invitedAt,
        acceptedAt: invitation.acceptedAt,
        areaIds: invitation.areaIds
      });
    }
    
    return invitationDetails;
  },
});

// Debug query to find user by email
export const findUserByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (!user) return null;
    
    return {
      id: user._id,
      email: user.email,
      workosId: user.workosId,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      createdAt: user.createdAt
    };
  },
});

// Debug function to update role permissions
export const updateRolePermissions = mutation({
  args: {
    roleId: v.id("roles"),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.roleId, {
      permissions: args.permissions,
      updatedAt: Date.now(),
    });
    
    return { success: true, roleId: args.roleId };
  },
});

// Debug function to assign shop manager
export const assignShopManager = mutation({
  args: {
    shopId: v.id("shops"),
    managerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.shopId, {
      managerId: args.managerId,
      updatedAt: Date.now(),
    });
    
    return { success: true, shopId: args.shopId, managerId: args.managerId };
  },
});