import { query, mutation } from "./_generated/server";
import { v } from "convex/values";

// Debug query to view threshold settings
export const getThresholdSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("settings").collect();

    // Filter for threshold-related settings
    const thresholdSettings = settings.filter(setting =>
      setting.key.includes('threshold') || setting.category === 'approval_thresholds'
    );

    // Format for easy viewing
    const formattedSettings = thresholdSettings.map(setting => ({
      key: setting.key,
      value: setting.value,
      description: setting.description,
      category: setting.category,
      isSystem: setting.isSystem,
      updatedAt: new Date(setting.updatedAt).toISOString(),
      createdAt: new Date(setting.createdAt).toISOString()
    }));

    return {
      thresholdSettings: formattedSettings,
      summary: {
        mobileMoneyThreshold: settings.find(s => s.key === 'mobile_money_approval_threshold')?.value || 'Not set',
        bankTransferThreshold: settings.find(s => s.key === 'bank_transfer_approval_threshold')?.value || 'Not set',
        totalThresholdSettings: thresholdSettings.length
      }
    };
  },
});

// Debug query to view all settings
export const getAllSettings = query({
  args: {},
  handler: async (ctx) => {
    const settings = await ctx.db.query("settings").collect();

    return settings.map(setting => ({
      _id: setting._id,
      key: setting.key,
      value: setting.value,
      description: setting.description,
      category: setting.category,
      isSystem: setting.isSystem,
      updatedAt: new Date(setting.updatedAt).toISOString(),
      createdAt: new Date(setting.createdAt).toISOString()
    }));
  },
});

// Debug query to check user role assignments
export const getUserRoleAssignments = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    const userRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .collect();

    const roleDetails = [];
    for (const userRole of userRoles) {
      const role = await ctx.db.get(userRole.roleId);
      roleDetails.push({
        userRoleId: userRole._id,
        roleId: userRole.roleId,
        roleName: role?.name,
        assignedAt: userRole.assignedAt,
        assignedBy: userRole.assignedBy,
        isActive: userRole.isActive
      });
    }

    return roleDetails;
  },
});

// Debug query to check all user roles in the system
export const getAllUserRoles = query({
  args: {},
  handler: async (ctx) => {
    const userRoles = await ctx.db.query("user_roles").collect();
    
    const roleDetails = [];
    for (const userRole of userRoles) {
      const user = await ctx.db.get(userRole.userId);
      const role = await ctx.db.get(userRole.roleId);
      roleDetails.push({
        userRoleId: userRole._id,
        userId: userRole.userId,
        userEmail: user?.email,
        roleId: userRole.roleId,
        roleName: role?.name,
        assignedAt: userRole.assignedAt,
        isActive: userRole.isActive
      });
    }

    return roleDetails;
  },
});

// Debug query to check all roles in the system
export const getAllRoles = query({
  args: {},
  handler: async (ctx) => {
    const roles = await ctx.db.query("roles").collect();
    return roles.map(role => ({
      id: role._id,
      name: role.name,
      description: role.description,
      permissions: role.permissions,
      isActive: role.isActive
    }));
  },
});

// Debug query to check invitations
export const getInvitations = query({
  args: { email: v.optional(v.string()) },
  handler: async (ctx, args) => {
    let query = ctx.db.query("invitations");
    
    if (args.email) {
      query = query.withIndex("by_email", (q) => q.eq("email", args.email));
    }
    
    const invitations = await query.collect();
    
    const invitationDetails = [];
    for (const invitation of invitations) {
      const role = await ctx.db.get(invitation.roleId);
      const invitedBy = await ctx.db.get(invitation.invitedBy);
      
      invitationDetails.push({
        id: invitation._id,
        email: invitation.email,
        status: invitation.status,
        roleId: invitation.roleId,
        roleName: role?.name,
        invitedBy: invitedBy?.email,
        invitedAt: invitation.invitedAt,
        acceptedAt: invitation.acceptedAt,
        areaIds: invitation.areaIds
      });
    }
    
    return invitationDetails;
  },
});

// Debug query to find user by email
export const findUserByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (!user) return null;
    
    return {
      id: user._id,
      email: user.email,
      workosId: user.workosId,
      firstName: user.firstName,
      lastName: user.lastName,
      isActive: user.isActive,
      createdAt: user.createdAt
    };
  },
});

// Debug function to update role permissions
export const updateRolePermissions = mutation({
  args: {
    roleId: v.id("roles"),
    permissions: v.array(v.string()),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.roleId, {
      permissions: args.permissions,
      updatedAt: Date.now(),
    });
    
    return { success: true, roleId: args.roleId };
  },
});

// Debug function to assign shop manager
export const assignShopManager = mutation({
  args: {
    shopId: v.id("shops"),
    managerId: v.id("users"),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.shopId, {
      managerId: args.managerId,
      updatedAt: Date.now(),
    });
    
    return { success: true, shopId: args.shopId, managerId: args.managerId };
  },
});