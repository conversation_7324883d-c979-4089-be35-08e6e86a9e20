
import React, { useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import LoginForm from '@/components/auth/LoginForm';
import { useAuth } from '@/lib/auth-context';
import PageTransition from '@/components/common/PageTransition';

const Login: React.FC = () => {
  const { user, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // Simple check for token in URL for password reset
  useEffect(() => {
    // Check URL search parameters
    const searchParams = new URLSearchParams(location.search);
    const token = searchParams.get('token');

    if (token) {
      navigate(`/set-password?token=${token}`);
    }
  }, [location, navigate]);



  useEffect(() => {
    // Only redirect if we have a user and authentication is not in progress
    if (!isLoading && user) {
      navigate('/dashboard');
    }
  }, [user, isLoading, navigate]);

  return (
    <div className="min-h-screen flex items-center justify-center px-4 py-8 sm:p-4 bg-gray-100">
      <PageTransition>
        <LoginForm />
      </PageTransition>
    </div>
  );
};

export default Login;
