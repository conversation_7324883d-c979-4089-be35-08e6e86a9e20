/**
 * Complete CRUD test for shops
 */

import { authenticatedConvex } from './convex-auth';

export const testCompleteShopCRUD = async () => {
  try {
    console.log('🔍 Testing complete shop CRUD operations...');
    
    const testName = `Test Shop ${Date.now()}`;
    const testCode = `TS${Date.now()}`;
    const results = [];
    
    // First, get an active area to use
    console.log('\n📋 Step 0: Getting active area...');
    const areas = await authenticatedConvex.getAreas();
    if (areas.length === 0) {
      throw new Error('No areas available for testing');
    }
    const testAreaId = areas[0]._id;
    console.log('✅ Using area:', areas[0].name, testAreaId);
    
    // Test 1: Create Shop
    console.log('\n📋 Step 1: Creating shop...');
    const shopId = await authenticatedConvex.createShop(
      testName, 
      testCode, 
      testAreaId, 
      undefined, // managerId
      'Test Address',
      '123-456-7890'
    );
    console.log('✅ Shop created:', shopId);
    results.push({ operation: 'create', success: true, shopId });
    
    // Test 2: Get Shops (should include new shop)
    console.log('\n📋 Step 2: Fetching all shops...');
    const shops = await authenticatedConvex.getShops();
    const createdShop = shops.find(s => s._id === shopId);
    console.log(`✅ Found ${shops.length} shops, including our new shop:`, !!createdShop);
    results.push({ operation: 'list', success: !!createdShop, totalShops: shops.length });
    
    // Test 3: Get Single Shop
    console.log('\n📋 Step 3: Fetching single shop...');
    const singleShop = await authenticatedConvex.getShop(shopId);
    console.log('✅ Single shop fetched:', singleShop?.name);
    results.push({ operation: 'read', success: !!singleShop, shopName: singleShop?.name });
    
    // Test 4: Update Shop
    console.log('\n📋 Step 4: Updating shop...');
    const updatedName = `${testName} Updated`;
    await authenticatedConvex.updateShop(
      shopId, 
      updatedName, 
      undefined, // code
      undefined, // areaId
      undefined, // managerId
      'Updated Address'
    );
    
    // Verify update
    const updatedShop = await authenticatedConvex.getShop(shopId);
    const updateSuccess = updatedShop?.name === updatedName;
    console.log('✅ Shop updated:', updateSuccess, updatedShop?.name);
    results.push({ operation: 'update', success: updateSuccess, newName: updatedShop?.name });
    
    // Test 5: Delete Shop
    console.log('\n📋 Step 5: Deleting shop...');
    await authenticatedConvex.deleteShop(shopId);
    
    // Verify deletion (should not appear in active shops list)
    const shopsAfterDelete = await authenticatedConvex.getShops();
    const deletedShopStillVisible = shopsAfterDelete.find(s => s._id === shopId);
    const deleteSuccess = !deletedShopStillVisible;
    console.log('✅ Shop deleted (not in active list):', deleteSuccess);
    results.push({ operation: 'delete', success: deleteSuccess, stillVisible: !!deletedShopStillVisible });
    
    // Summary
    const successCount = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n=== Test Summary ===`);
    console.log(`✅ ${successCount}/${totalTests} operations successful`);
    
    return {
      success: successCount === totalTests,
      totalTests,
      successCount,
      results,
      message: successCount === totalTests ? 
        '🎉 All shop CRUD operations working perfectly!' : 
        `⚠️ ${totalTests - successCount} operation(s) failed`
    };
    
  } catch (error) {
    console.error('❌ Shop CRUD test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Shop CRUD test failed with error'
    };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testCompleteShopCRUD = testCompleteShopCRUD;
}