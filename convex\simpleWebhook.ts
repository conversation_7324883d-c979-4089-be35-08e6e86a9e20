import { httpAction } from "./_generated/server";

export const webhook = httpAction(async (ctx, request) => {
  console.log("Webhook called!");
  console.log("Method:", request.method);
  console.log("URL:", request.url);
  
  const body = await request.text();
  console.log("Body:", body);
  
  const headers = Object.fromEntries(request.headers.entries());
  console.log("Headers:", headers);
  
  return new Response("Webhook received!", {
    status: 200,
    headers: {
      "Content-Type": "text/plain",
    },
  });
});