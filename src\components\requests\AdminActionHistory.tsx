import React from 'react';
import { format } from 'date-fns';
import {
  Shield,
  CheckCircle,
  XCircle,
  MessageSquare,
  Eye,
  Clock,
  User
} from 'lucide-react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AdminAction } from '@/lib/types';

interface AdminActionHistoryProps {
  adminActions: AdminAction[];
  className?: string;
}

const AdminActionHistory: React.FC<AdminActionHistoryProps> = ({
  adminActions,
  className = ''
}) => {
  // Sort actions by timestamp (most recent first)
  const sortedActions = [...adminActions].sort(
    (a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  const getActionIcon = (type: AdminAction['type']) => {
    switch (type) {
      case 'approve':
        return <CheckCircle className="h-4 w-4 text-white" />;
      case 'reject':
        return <XCircle className="h-4 w-4 text-white" />;
      case 'comment':
        return <MessageSquare className="h-4 w-4 text-white" />;
      case 'review':
        return <Eye className="h-4 w-4 text-white" />;
      default:
        return <Shield className="h-4 w-4 text-white" />;
    }
  };

  const getActionColor = (type: AdminAction['type']) => {
    switch (type) {
      case 'approve':
        return 'border-green-200 bg-green-50';
      case 'reject':
        return 'border-red-200 bg-red-50';
      case 'comment':
        return 'border-blue-200 bg-blue-50';
      case 'review':
        return 'border-amber-200 bg-amber-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  const getIconBgColor = (type: AdminAction['type']) => {
    switch (type) {
      case 'approve':
        return 'bg-green-500';
      case 'reject':
        return 'bg-red-500';
      case 'comment':
        return 'bg-blue-500';
      case 'review':
        return 'bg-amber-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getCommentBgColor = (type: AdminAction['type']) => {
    switch (type) {
      case 'approve':
        return 'bg-green-50';
      case 'reject':
        return 'bg-red-50';
      case 'comment':
        return 'bg-blue-50';
      case 'review':
        return 'bg-amber-50';
      default:
        return 'bg-gray-50';
    }
  };

  const getActionTitle = (action: AdminAction) => {
    switch (action.type) {
      case 'approve':
        return 'Request Approved';
      case 'reject':
        return 'Request Rejected';
      case 'comment':
        return 'Admin Comment';
      case 'review':
        return 'Under Review';
      default:
        return 'Admin Action';
    }
  };

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'accounts':
        return 'default';
      case 'shop_support':
        return 'secondary';
      default:
        return 'outline';
    }
  };

  const getBorderColor = (type: AdminAction['type']) => {
    switch (type) {
      case 'approve':
        return 'border-green-200';
      case 'reject':
        return 'border-red-200';
      case 'comment':
        return 'border-blue-200';
      case 'review':
        return 'border-amber-200';
      default:
        return 'border-gray-200';
    }
  };

  const getActionTagColor = (type: AdminAction['type']) => {
    switch (type) {
      case 'approve':
        return 'bg-green-100 text-green-700';
      case 'reject':
        return 'bg-red-100 text-red-700';
      case 'comment':
        return 'bg-blue-100 text-blue-700';
      case 'review':
        return 'bg-amber-100 text-amber-700';
      default:
        return 'bg-gray-100 text-gray-700';
    }
  };

  if (!adminActions || adminActions.length === 0) {
    return (
      <Card className={`shadow-sm ${className}`}>
        <CardHeader className="pb-3">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Admin Action History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground">
            <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">No admin actions recorded yet</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`shadow-sm ${className}`}>
      <CardHeader className="pb-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Admin Action History
          <Badge variant="outline" className="ml-auto">
            {adminActions.length} {adminActions.length === 1 ? 'action' : 'actions'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="relative">
          {/* Continuous timeline line */}
          <div className="absolute left-[6px] top-0 bottom-0 w-0.5 bg-gray-200"></div>

          <div className="space-y-0">
            {sortedActions.map((action, index) => (
              <div key={action.id} className="relative flex items-start gap-4 pb-4">
                <div className={`relative z-10 w-3 h-3 rounded-full ${getIconBgColor(action.type)}`}></div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-900">{getActionTitle(action)}</span>
                    <span className="text-xs text-gray-400">{format(new Date(action.timestamp), 'MMM dd')}</span>
                  </div>
                  <p className="text-xs text-gray-500 mt-1">{action.adminName}</p>
                  {action.comment && (
                    <p className="text-xs text-gray-600 mt-2 italic">"{action.comment}"</p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminActionHistory;
