
import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import PageTransition from '@/components/common/PageTransition';
import { Button } from '@/components/ui/button';
import { Search, MoreVertical, Eye, ChevronLeft, ChevronRight, Calendar, Download, X } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Link, useNavigate, useSearchParams } from 'react-router-dom';
// Removed old store import - now using Convex queries
import StatusBadge from '@/components/ui/StatusBadge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { format, isEqual, isWithinInterval, startOfDay, endOfDay } from 'date-fns';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { useAuth } from '@/lib/auth-context';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
// Removed old auth store import - now using WorkOS auth context
import { Request } from '@/lib/types';
import { DateRange } from "react-day-picker";
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Pencil, Trash } from "lucide-react";

const Requests: React.FC = () => {
  const { user } = useRequireAuth();
  const { user: workosUser } = useAuth();
  const cancelRequestMutation = useMutation(api.requests.cancelRequest);
  const navigate = useNavigate();
  
  // Use WorkOS-compatible public query
  const convexRequests = useQuery(
    api.publicQueries.getRequestsForUser,
    workosUser?.id ? { workosUserId: workosUser.id } : "skip"
  );
  
  // Use convex requests if available, fallback to empty array
  const requests = convexRequests || [];
  
  // Debug logging to see what data we're getting
  console.log('Requests Page Debug:', {
    workosUser: workosUser?.id,
    convexRequests: convexRequests?.length,
    requests: requests.length,
    sampleRequest: requests[0]
  });
  const [searchParams] = useSearchParams();
  const statusFromUrl = searchParams.get('status');
  const areaFromUrl = searchParams.get('area');

  // Initialize filters with URL parameters if present
  const [statusFilter, setStatusFilter] = useState(statusFromUrl || 'all');
  const [areaFilter, setAreaFilter] = useState(areaFromUrl || 'all');
  const [shopFilter, setShopFilter] = useState('all');

  useEffect(() => {
    // Update filters when URL parameters change
    if (statusFromUrl) {
      setStatusFilter(statusFromUrl);
    }
    if (areaFromUrl) {
      setAreaFilter(areaFromUrl);
    }
  }, [statusFromUrl, areaFromUrl]);

  // Data is automatically fetched via useQuery hook

  // Filter states
  const [searchTerm, setSearchTerm] = useState('');
  const [paymentMethodFilter, setPaymentMethodFilter] = useState('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [tempDateRange, setTempDateRange] = useState<DateRange | undefined>();
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [rowsPerPage, setRowsPerPage] = useState('10');
  const [currentPage, setCurrentPage] = useState(1);

  // Add state for filtered requests if not already present
  const [filteredRequests, setFilteredRequests] = useState<Request[]>([]);

  // Add sorting states
  const [sortColumn, setSortColumn] = useState<string>('createdAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [requestToDelete, setRequestToDelete] = useState<Request | null>(null);

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range) return "Select date range";
    if (!range.from) return "Select date range";
    if (!range.to) return format(range.from, "MMM dd, yyyy");
    return `${format(range.from, "MMM dd, yyyy")} - ${format(range.to, "MMM dd, yyyy")}`;
  };

  const handleApplyDateFilter = () => {
    setDateRange(tempDateRange);
    setIsCalendarOpen(false);

    // Filter requests based on date range
    if (tempDateRange?.from) {
      const filtered = requests.filter(request => {
        const requestDate = new Date(request.createdAt);

        // If only "from" date is selected
        if (!tempDateRange.to) {
          return isEqual(startOfDay(requestDate), startOfDay(tempDateRange.from));
        }

        // If both "from" and "to" dates are selected
        return isWithinInterval(requestDate, {
          start: startOfDay(tempDateRange.from),
          end: endOfDay(tempDateRange.to)
        });
      });

      setFilteredRequests(filtered);
    } else {
      // If no date range is selected, show all requests
      setFilteredRequests(requests);
    }
  };

  const handleCancelDateFilter = () => {
    setDateRange(undefined);
    setTempDateRange(undefined);
    setIsCalendarOpen(false);
    setFilteredRequests(requests); // Reset to show all requests
  };

  // Sorting handler
  const handleSort = (column: string) => {
    if (sortColumn === column) {
      // If clicking the same column, toggle direction
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      // If clicking new column, set it with default desc order
      setSortColumn(column);
      setSortDirection('desc');
    }
  };

  // Sort function
  const sortRequests = (requests: Request[]) => {
    return [...requests].sort((a, b) => {
      const modifier = sortDirection === 'asc' ? 1 : -1;

      switch (sortColumn) {
        case 'createdAt':
          return (new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()) * modifier;
        case 'ticketId':
          return (a.ticketNumber || a.ticketId || '').localeCompare(b.ticketNumber || b.ticketId || '') * modifier;
        case 'customerName':
          return (a.customerName || '').localeCompare(b.customerName || '') * modifier;
        case 'shopName':
          return a.shopName.localeCompare(b.shopName) * modifier;
        case 'areaName':
          return (a.areaName || '').localeCompare(b.areaName || '') * modifier;
        case 'amount':
          return (a.amount - b.amount) * modifier;
        case 'paymentMethod':
          return a.paymentMethod.localeCompare(b.paymentMethod) * modifier;
        case 'status':
          return a.status.localeCompare(b.status) * modifier;
        default:
          return 0;
      }
    });
  };

  // Get unique areas and shops for filter dropdowns
  const uniqueAreas = Array.from(new Set(requests.map(request => request.areaName).filter(Boolean))).sort();
  const uniqueShops = Array.from(new Set(requests.map(request => request.shopName).filter(Boolean))).sort();

  // Apply all filters
  const getFilteredRequests = () => {
    return requests.filter(request => {
      // Search filter
      const searchFields = [
        request.ticketNumber || request.ticketId,
        request.customerName,
        request.shopName,
        request.areaName,
        request.amount.toString()
      ].map(field => field?.toLowerCase() || '');

      const searchMatch = searchTerm === '' || searchFields.some(field =>
        field.includes(searchTerm.toLowerCase())
      );

      // Status filter - handle 'pending' to include both 'pending' and 'resubmitted'
      let statusMatch = false;
      if (statusFilter === 'all') {
        statusMatch = true;
      } else if (statusFilter === 'pending') {
        // When filtering by 'pending', include both 'pending' and 'resubmitted' statuses
        statusMatch = request.status === 'pending' || request.status === 'resubmitted';
      } else {
        // For other statuses, exact match
        statusMatch = request.status === statusFilter;
      }

      // Area filter
      const areaMatch = areaFilter === 'all' || request.areaName === areaFilter;

      // Shop filter
      const shopMatch = shopFilter === 'all' || request.shopName === shopFilter;

      // Payment method filter
      const paymentMatch = paymentMethodFilter === 'all' || request.paymentMethod === paymentMethodFilter;

      // Date range filter
      let dateMatch = true;
      if (dateRange?.from && dateRange?.to) {
        const requestDate = new Date(request.createdAt);
        dateMatch = requestDate >= dateRange.from && requestDate <= dateRange.to;
      }

      return searchMatch && statusMatch && areaMatch && shopMatch && paymentMatch && dateMatch;
    });
  };

  // Apply sorting after filtering
  const sortedAndFilteredRequests = sortRequests(getFilteredRequests());

  // Pagination
  const totalPages = Math.ceil(sortedAndFilteredRequests.length / parseInt(rowsPerPage));
  const paginatedRequests = sortedAndFilteredRequests.slice(
    (currentPage - 1) * parseInt(rowsPerPage),
    currentPage * parseInt(rowsPerPage)
  );

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('en-GH', {
      style: 'currency',
      currency: 'GHS',
      minimumFractionDigits: 2,
      currencyDisplay: 'symbol'
    }).format(amount);
  };

  const handleDelete = async (request: Request) => {
    setRequestToDelete(request);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = async () => {
    if (!requestToDelete) return;

    try {
      await cancelRequestMutation({ 
        requestId: requestToDelete._id,
        workosUserId: workosUser?.id || ""
      });
      // Convex queries automatically refresh
      toast({
        title: "Success",
        description: "Request cancelled successfully",
      });
      setIsDeleteDialogOpen(false);
      setRequestToDelete(null);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete request",
        variant: "destructive",
      });
    }
  };

  const renderActions = (request: Request) => {
    const isCreator = workosUser?.id === request.requestedBy;
    const isShopManager = user?.role === 'shop_manager';

    return (
      <div className="flex justify-end gap-2">
        {/* View button always visible for all users */}
        <Button variant="ghost" size="icon" asChild>
          <Link to={`/requests/${request._id}`}>
            <Eye className="h-4 w-4" />
          </Link>
        </Button>

        {/* Edit/Delete options only for shop manager who created the request */}
        {isShopManager && isCreator && request.status === 'pending' && (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => navigate(`/requests/${request._id}/edit`)}>
                <Pencil className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-destructive"
                onClick={() => handleDelete(request)}
              >
                <Trash className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    );
  };

  const exportToCSV = () => {
    const filteredRequests = requests.filter(request => {
      const requestDate = new Date(request.createdAt);
      if (dateRange?.from && dateRange?.to) {
        return requestDate >= dateRange.from && requestDate <= dateRange.to;
      }
      if (dateRange?.from) {
        return requestDate >= dateRange.from;
      }
      if (dateRange?.to) {
        return requestDate <= dateRange.to;
      }
      return true;
    });

    const headers = [
      'Date',
      'Ticket ID',
      'Customer Name',
      'Shop Name',
      'Amount',
      'Payment Method',
      'Status'
    ];

    const data = filteredRequests.map(request => [
      format(new Date(request.createdAt), 'yyyy-MM-dd HH:mm:ss'),
      request.ticketNumber || request.ticketId || '',
      request.customerName || '',
      request.shopName,
      request.amount.toString(),
      request.paymentMethod,
      request.status
    ]);

    const csv = [
      headers.join(','),
      ...data.map(row => row.join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `requests-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  // Helper for sort direction icon
  const SortIcon = ({ column }: { column: string }) => {
    if (sortColumn !== column) return null;

    return (
      <span className="ml-2 inline-block">
        {sortDirection === 'desc' ? '↓' : '↑'}
      </span>
    );
  };

  return (
    <Layout>
      <PageTransition>
        {/* Header section with title and actions */}
        <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-6">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Request Overview</h1>
            <p className="text-muted-foreground mt-1">Manage and track all cash requests</p>
          </div>
          <div className="flex items-center gap-3">
            {/* Date Range Picker */}
            <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="w-[280px] pl-3 text-left font-normal"
                >
                  {formatDateRange(dateRange)}
                  <Calendar className="ml-auto h-4 w-4 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="end">
                <div className="p-3">
                  <CalendarComponent
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange?.from}
                    selected={tempDateRange}
                    onSelect={setTempDateRange}
                    numberOfMonths={2}
                  />
                  <div className="flex items-center justify-end gap-2 pt-4 border-t mt-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCancelDateFilter}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={handleApplyDateFilter}
                      disabled={!tempDateRange?.from}
                    >
                      Apply
                    </Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>

            {dateRange && (
              <Button
                variant="ghost"
                size="icon"
                onClick={() => {
                  setDateRange(undefined);
                  setTempDateRange(undefined);
                }}
                className="h-10 w-10"
              >
                <X className="h-4 w-4" />
              </Button>
            )}

            <Button
              onClick={exportToCSV}
              className="flex items-center gap-2"
              variant="outline"
            >
              <Download className="h-4 w-4" />
              <span>Export CSV</span>
            </Button>
          </div>
        </div>

        {/* Filters section - Professional layout */}
        <div className="space-y-4">
          <div className="bg-muted/30 rounded-lg p-4 border">
            <div className="flex flex-col gap-4">
              {/* Filter header */}
              <div className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <Search className="h-4 w-4" />
                <span>Filter & Search</span>
              </div>

              {/* First row - Search and primary filters */}
              <div className="flex flex-wrap gap-3 items-center">
                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search requests..."
                    className="pl-8 w-[280px] bg-background"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>

                {/* Status Filter */}
                <div className="min-w-[140px]">
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="bg-background">
                      <SelectValue placeholder="All Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Status</SelectItem>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="approved">Approved</SelectItem>
                      <SelectItem value="rejected">Rejected</SelectItem>
                      <SelectItem value="resubmitted">Resubmitted</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Area Filter */}
                <div className="min-w-[160px]">
                  <Select value={areaFilter} onValueChange={setAreaFilter}>
                    <SelectTrigger className="bg-background">
                      <SelectValue placeholder="All Areas" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Areas</SelectItem>
                      {uniqueAreas.map((area) => (
                        <SelectItem key={area} value={area}>
                          {area}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Shop Filter */}
                <div className="min-w-[180px]">
                  <Select value={shopFilter} onValueChange={setShopFilter}>
                    <SelectTrigger className="bg-background">
                      <SelectValue placeholder="All Shops" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Shops</SelectItem>
                      {uniqueShops.map((shop) => (
                        <SelectItem key={shop} value={shop}>
                          {shop}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Payment Method Filter */}
                <div className="min-w-[200px]">
                  <Select value={paymentMethodFilter} onValueChange={setPaymentMethodFilter}>
                    <SelectTrigger className="bg-background">
                      <SelectValue placeholder="All Payment Methods" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Payment Methods</SelectItem>
                      <SelectItem value="mobile_money">Mobile Money</SelectItem>
                      <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Results summary */}
              <div className="flex items-center justify-between text-sm text-muted-foreground border-t pt-3">
                <span>
                  Showing {getFilteredRequests().length} of {requests.length} requests
                  {(statusFilter !== 'all' || areaFilter !== 'all' || shopFilter !== 'all' || paymentMethodFilter !== 'all' || searchTerm || dateRange) && (
                    <span className="ml-2 text-primary font-medium">(filtered)</span>
                  )}
                </span>
                {(statusFilter !== 'all' || areaFilter !== 'all' || shopFilter !== 'all' || paymentMethodFilter !== 'all' || searchTerm || dateRange) && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      setStatusFilter('all');
                      setAreaFilter('all');
                      setShopFilter('all');
                      setPaymentMethodFilter('all');
                      setSearchTerm('');
                      setDateRange(undefined);
                      setTempDateRange(undefined);
                    }}
                    className="h-8 px-2 text-xs"
                  >
                    Clear all filters
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Table */}
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('createdAt')}
                  >
                    Date <SortIcon column="createdAt" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('ticketId')}
                  >
                    Ticket ID <SortIcon column="ticketId" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('customerName')}
                  >
                    Customer <SortIcon column="customerName" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('shopName')}
                  >
                    Shop <SortIcon column="shopName" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('areaName')}
                  >
                    Area <SortIcon column="areaName" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('amount')}
                  >
                    Amount <SortIcon column="amount" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('paymentMethod')}
                  >
                    Payment Method <SortIcon column="paymentMethod" />
                  </TableHead>
                  <TableHead
                    className="cursor-pointer hover:bg-muted/50"
                    onClick={() => handleSort('status')}
                  >
                    Status <SortIcon column="status" />
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {paginatedRequests.map((request) => (
                  <TableRow key={request._id}>
                    <TableCell>
                      <div>{format(new Date(request.createdAt), 'MMM dd, yyyy')}</div>
                      <div className="text-sm text-muted-foreground">
                        {format(new Date(request.createdAt), 'HH:mm a')}
                      </div>
                    </TableCell>
                    <TableCell>{request.ticketNumber || request.ticketId || '-'}</TableCell>
                    <TableCell>{request.customerName || '-'}</TableCell>
                    <TableCell>{request.shopName}</TableCell>
                    <TableCell>{request.areaName || '-'}</TableCell>
                    <TableCell>{formatAmount(request.amount)}</TableCell>
                    <TableCell className="capitalize">
                      {request.paymentMethod.replace('_', ' ')}
                    </TableCell>
                    <TableCell>
                      <StatusBadge status={request.status} />
                    </TableCell>
                    <TableCell className="text-right">
                      {renderActions(request)}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Select value={rowsPerPage} onValueChange={setRowsPerPage}>
                <SelectTrigger className="w-[70px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="20">20</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
              <span className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * parseInt(rowsPerPage)) + 1} to {Math.min(currentPage * parseInt(rowsPerPage), getFilteredRequests().length)} of {getFilteredRequests().length}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="icon"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Add Delete Confirmation Dialog */}
        <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                This action cannot be undone. This will permanently delete the request.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={() => setRequestToDelete(null)}>
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction onClick={confirmDelete}>
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </PageTransition>
    </Layout>
  );
};

export default Requests;
