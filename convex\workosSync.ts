import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Sync roles from WorkOS to Convex
export const syncRolesFromWorkOS = mutation({
  args: {
    roles: v.array(v.object({
      id: v.string(),
      name: v.string(),
      description: v.string(),
      permissions: v.array(v.string()),
    }))
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    for (const workosRole of args.roles) {
      // Check if role already exists
      const existingRole = await ctx.db
        .query("roles")
        .withIndex("by_name", (q) => q.eq("name", workosRole.name))
        .unique();
      
      if (existingRole) {
        // Update existing role
        await ctx.db.patch(existingRole._id, {
          description: workosRole.description,
          permissions: workosRole.permissions,
          updatedAt: now,
        });
      } else {
        // Create new role
        await ctx.db.insert("roles", {
          name: workosRole.name,
          description: workosRole.description,
          permissions: workosRole.permissions,
          isActive: true,
          createdAt: now,
          updatedAt: now,
        });
      }
    }
    
    return { success: true, syncedRoles: args.roles.length };
  },
});

// Sync user role assignments from WorkOS to Convex
export const syncUserRoleFromWorkOS = mutation({
  args: {
    workosUserId: v.string(),
    roleNames: v.array(v.string()),
    assignedBy: v.optional(v.id("users")),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    
    // Find the user in Convex
    const user = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", args.workosUserId))
      .unique();
    
    if (!user) {
      throw new Error(`User with WorkOS ID ${args.workosUserId} not found in Convex`);
    }
    
    // Get all roles that match the role names
    const roles = await ctx.db.query("roles").collect();
    const matchingRoles = roles.filter(role => 
      args.roleNames.includes(role.name) && role.isActive
    );
    
    if (matchingRoles.length === 0) {
      throw new Error(`No matching roles found for: ${args.roleNames.join(", ")}`);
    }
    
    // Deactivate existing role assignments for this user
    const existingUserRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", user._id).eq("isActive", true)
      )
      .collect();
    
    for (const userRole of existingUserRoles) {
      await ctx.db.patch(userRole._id, { isActive: false });
    }
    
    // Create new role assignments
    const assignedByUser = args.assignedBy || user._id; // Default to self-assignment
    
    for (const role of matchingRoles) {
      await ctx.db.insert("user_roles", {
        userId: user._id,
        roleId: role._id,
        assignedBy: assignedByUser,
        assignedAt: now,
        isActive: true,
      });
    }
    
    return { 
      success: true, 
      userId: user._id,
      assignedRoles: matchingRoles.length 
    };
  },
});

// Get WorkOS roles that need to be synced
export const getWorkOSRolesToSync = query({
  args: {},
  handler: async (ctx) => {
    // Return the expected roles structure for WorkOS sync
    return [
      {
        name: "shop_manager",
        description: "Manages individual betting shop operations and creates payment requests",
        permissions: [
          "request:create",
          "request:view:own", 
          "request:resubmit",
          "reports:view:own"
        ]
      },
      {
        name: "shop_support",
        description: "Regional support staff who can approve mobile money requests within limits",
        permissions: [
          "request:view:area",
          "request:approve:mobile",
          "request:reject",
          "reports:view:area",
          "shop:view"
        ]
      },
      {
        name: "accounts",
        description: "Full administrative access to all financial operations and user management",
        permissions: [
          "request:view:all",
          "request:approve:all",
          "request:reject",
          "user:view",
          "user:create",
          "user:update",
          "user:invite",
          "user:assign:role",
          "user:assign:area",
          "shop:view",
          "shop:create",
          "shop:update",
          "shop:delete",
          "shop:assign:manager",
          "area:view",
          "area:create",
          "area:update",
          "area:delete",
          "settings:view",
          "settings:update",
          "reports:view:all",
          "reports:export"
        ]
      },
      {
        name: "watcher",
        description: "Read-only access to reports and analytics across all operations",
        permissions: [
          "reports:view:all"
        ]
      }
    ];
  },
});

// Initialize roles in Convex database
export const initializeRoles = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const rolesToCreate = [
      {
        name: "shop_manager",
        description: "Manages individual betting shop operations and creates payment requests",
        permissions: [
          "request:create",
          "request:view:own", 
          "request:resubmit",
          "reports:view:own"
        ]
      },
      {
        name: "shop_support",
        description: "Regional support staff who can approve mobile money requests within limits",
        permissions: [
          "request:view:area",
          "request:approve:mobile",
          "request:reject",
          "reports:view:area",
          "shop:view"
        ]
      },
      {
        name: "accounts",
        description: "Full administrative access to all financial operations and user management",
        permissions: [
          "request:view:all",
          "request:approve:all",
          "request:reject",
          "user:view",
          "user:create",
          "user:update",
          "user:invite",
          "user:assign:role",
          "user:assign:area",
          "shop:view",
          "shop:create",
          "shop:update",
          "shop:delete",
          "shop:assign:manager",
          "area:view",
          "area:create",
          "area:update",
          "area:delete",
          "settings:view",
          "settings:update",
          "reports:view:all",
          "reports:export"
        ]
      },
      {
        name: "watcher",
        description: "Read-only access to reports and analytics across all operations",
        permissions: [
          "reports:view:all"
        ]
      }
    ];
    
    let createdCount = 0;
    
    for (const roleData of rolesToCreate) {
      // Check if role already exists
      const existingRole = await ctx.db
        .query("roles")
        .withIndex("by_name", (q) => q.eq("name", roleData.name))
        .unique();
      
      if (!existingRole) {
        await ctx.db.insert("roles", {
          name: roleData.name,
          description: roleData.description,
          permissions: roleData.permissions,
          isActive: true,
          createdAt: now,
          updatedAt: now,
        });
        createdCount++;
      }
    }
    
    return { success: true, createdRoles: createdCount };
  },
});