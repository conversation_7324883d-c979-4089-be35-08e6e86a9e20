// Test WorkOS authentication in browser console
// Run this in the browser console to test WorkOS auth

async function testWorkOSAuth() {
  console.log('=== Testing WorkOS Authentication ===');
  
  try {
    // Import the WorkOS auth functions
    const { getSession, getCurrentWorkOSUserId } = await import('./src/lib/workos-auth.js');
    const { getCurrentWorkOSUserId: getConvexWorkOSUserId } = await import('./src/lib/convex-auth.js');
    
    console.log('1. Testing WorkOS getSession...');
    const session = await getSession();
    console.log('WorkOS session:', session);
    
    console.log('2. Testing getCurrentWorkOSUserId...');
    const userId = await getConvexWorkOSUserId();
    console.log('WorkOS user ID:', userId);
    
    if (!session) {
      console.error('❌ No WorkOS session found!');
      console.log('🔧 User needs to log in with WorkOS AuthKit');
      return false;
    }
    
    if (!userId) {
      console.error('❌ No WorkOS user ID found!');
      console.log('🔧 Session exists but user ID is missing');
      return false;
    }
    
    console.log('✅ WorkOS authentication is working!');
    console.log('User ID:', userId);
    console.log('User email:', session.user?.email);
    
    return true;
    
  } catch (error) {
    console.error('❌ Error testing WorkOS auth:', error);
    return false;
  }
}

// Run the test
testWorkOSAuth().then(success => {
  if (success) {
    console.log('🎉 WorkOS authentication test passed!');
  } else {
    console.log('🚨 WorkOS authentication test failed!');
    console.log('💡 Try logging out and logging back in with WorkOS AuthKit');
  }
});