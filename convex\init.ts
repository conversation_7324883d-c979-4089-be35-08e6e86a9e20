import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import {
  requireAuth,
  requirePermission,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  ROLES,
} from "./permissions";

// Initialize the entire system with default data
export const initializeSystem = mutation({
  args: {},
  handler: async (ctx) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.SETTINGS_UPDATE);

    const now = Date.now();
    const results = {
      rolesCreated: [] as string[],
      settingsCreated: [] as string[],
      errors: [] as string[],
    };

    try {
      // 1. Create default roles if they don't exist
      const defaultRoles = [
        {
          name: "shop_manager",
          description: "Shop Manager - Can create and manage requests for their shop",
          permissions: [
            PERMISSIONS.REQUEST_CREATE,
            PERMISSIONS.REQUEST_VIEW_OWN,
            PERMISSIONS.REQUEST_VIEW_SHOP,
            PERMISSIONS.REQUEST_RESUBMIT,
            PERMISSIONS.SHOP_VIEW,
            PERMISSIONS.AREA_VIEW,
            PERMISSIONS.REPORTS_VIEW_OWN,
            PERMISSIONS.REPORTS_VIEW_SHOP,
          ],
        },
        {
          name: "shop_support",
          description: "Shop Support - Can approve mobile money requests within threshold",
          permissions: [
            PERMISSIONS.REQUEST_VIEW_AREA,
            PERMISSIONS.REQUEST_APPROVE_MOBILE,
            PERMISSIONS.REQUEST_REJECT,
            PERMISSIONS.SHOP_VIEW,
            PERMISSIONS.AREA_VIEW,
            PERMISSIONS.USER_VIEW,
            PERMISSIONS.REPORTS_VIEW_AREA,
            PERMISSIONS.REPORTS_EXPORT,
          ],
        },
        {
          name: "accounts",
          description: "Accounts Team - Full system access for financial oversight",
          permissions: [
            PERMISSIONS.REQUEST_VIEW_ALL,
            PERMISSIONS.REQUEST_APPROVE_ALL,
            PERMISSIONS.REQUEST_REJECT,
            PERMISSIONS.SHOP_VIEW,
            PERMISSIONS.SHOP_CREATE,
            PERMISSIONS.SHOP_UPDATE,
            PERMISSIONS.SHOP_DELETE,
            PERMISSIONS.SHOP_ASSIGN_MANAGER,
            PERMISSIONS.AREA_VIEW,
            PERMISSIONS.AREA_CREATE,
            PERMISSIONS.AREA_UPDATE,
            PERMISSIONS.AREA_DELETE,
            PERMISSIONS.USER_VIEW,
            PERMISSIONS.USER_CREATE,
            PERMISSIONS.USER_UPDATE,
            PERMISSIONS.USER_DELETE,
            PERMISSIONS.USER_INVITE,
            PERMISSIONS.USER_ASSIGN_ROLE,
            PERMISSIONS.USER_ASSIGN_AREA,
            PERMISSIONS.SETTINGS_VIEW,
            PERMISSIONS.SETTINGS_UPDATE,
            PERMISSIONS.REPORTS_VIEW_ALL,
            PERMISSIONS.REPORTS_EXPORT,
          ],
        },
        {
          name: "watcher",
          description: "Watcher - Read-only access for monitoring and reporting",
          permissions: [
            PERMISSIONS.REQUEST_VIEW_ALL,
            PERMISSIONS.SHOP_VIEW,
            PERMISSIONS.AREA_VIEW,
            PERMISSIONS.USER_VIEW,
            PERMISSIONS.SETTINGS_VIEW,
            PERMISSIONS.REPORTS_VIEW_ALL,
            PERMISSIONS.REPORTS_EXPORT,
          ],
        },
      ];

      for (const roleData of defaultRoles) {
        const existingRole = await ctx.db
          .query("roles")
          .withIndex("by_name", (q) => q.eq("name", roleData.name))
          .first();

        if (!existingRole) {
          await ctx.db.insert("roles", {
            ...roleData,
            isActive: true,
            createdAt: now,
            updatedAt: now,
          });
          results.rolesCreated.push(roleData.name);
        }
      }

      // 2. Create default settings
      const defaultSettings = [
        {
          key: "mobile_money_approval_threshold",
          value: 5000,
          description: "Maximum amount for mobile money requests that can be approved by shop support",
          category: "approval_thresholds",
        },
        {
          key: "bank_transfer_approval_threshold",
          value: 10000,
          description: "Maximum amount for bank transfer requests that can be approved by shop support",
          category: "approval_thresholds",
        },
        {
          key: "whitelisted_domains",
          value: ["kmkentertainment.com", "mybet.africa"],
          description: "Email domains allowed for user registration",
          category: "security",
        },
        {
          key: "max_file_size",
          value: 10 * 1024 * 1024, // 10MB
          description: "Maximum file size for uploads in bytes",
          category: "file_management",
        },
        {
          key: "max_avatar_size",
          value: 2 * 1024 * 1024, // 2MB
          description: "Maximum avatar file size in bytes",
          category: "file_management",
        },
        {
          key: "max_ticket_image_size",
          value: 5 * 1024 * 1024, // 5MB
          description: "Maximum ticket image file size in bytes",
          category: "file_management",
        },
        {
          key: "notification_retention_days",
          value: 30,
          description: "Number of days to retain notifications",
          category: "notifications",
        },
        {
          key: "session_timeout_minutes",
          value: 480, // 8 hours
          description: "Session timeout in minutes",
          category: "security",
        },
        {
          key: "auto_logout_warning_minutes",
          value: 15,
          description: "Minutes before session timeout to show warning",
          category: "security",
        },
      ];

      for (const settingData of defaultSettings) {
        const existingSetting = await ctx.db
          .query("settings")
          .withIndex("by_key", (q) => q.eq("key", settingData.key))
          .first();

        if (!existingSetting) {
          await ctx.db.insert("settings", {
            ...settingData,
            isSystem: true,
            updatedBy: userWithPermissions.user._id,
            updatedAt: now,
            createdAt: now,
          });
          results.settingsCreated.push(settingData.key);
        }
      }

      // Log the initialization
      await ctx.db.insert("audit_logs", {
        action: "system_initialized",
        entityType: "system",
        entityId: "initialization",
        userId: userWithPermissions.user._id,
        newValues: results,
        timestamp: now,
      });

    } catch (error: any) {
      results.errors.push(error.message);
    }

    return results;
  },
});

// Check if system is initialized
export const checkSystemInitialization = query({
  args: {},
  handler: async (ctx) => {
    await requireAuth(ctx);

    // Check if roles exist
    const roles = await ctx.db.query("roles").collect();
    const expectedRoles = ["shop_manager", "shop_support", "accounts", "watcher"];
    const existingRoleNames = roles.map(r => r.name);
    const missingRoles = expectedRoles.filter(role => !existingRoleNames.includes(role));

    // Check if settings exist
    const settings = await ctx.db.query("settings").collect();
    const expectedSettings = [
      "mobile_money_approval_threshold",
      "bank_transfer_approval_threshold",
      "whitelisted_domains",
      "max_file_size",
      "max_avatar_size",
      "max_ticket_image_size",
      "notification_retention_days",
      "session_timeout_minutes",
      "auto_logout_warning_minutes",
    ];
    const existingSettingKeys = settings.map(s => s.key);
    const missingSettings = expectedSettings.filter(setting => !existingSettingKeys.includes(setting));

    return {
      isInitialized: missingRoles.length === 0 && missingSettings.length === 0,
      roles: {
        total: roles.length,
        expected: expectedRoles.length,
        missing: missingRoles,
      },
      settings: {
        total: settings.length,
        expected: expectedSettings.length,
        missing: missingSettings,
      },
    };
  },
});

// Get system status
export const getSystemStatus = query({
  args: {},
  handler: async (ctx) => {
    await requireAuth(ctx);

    const [users, roles, areas, shops, requests, notifications, settings] = await Promise.all([
      ctx.db.query("users").collect(),
      ctx.db.query("roles").collect(),
      ctx.db.query("areas").collect(),
      ctx.db.query("shops").collect(),
      ctx.db.query("requests").collect(),
      ctx.db.query("notifications").collect(),
      ctx.db.query("settings").collect(),
    ]);

    return {
      users: {
        total: users.length,
        active: users.filter(u => u.isActive).length,
      },
      roles: {
        total: roles.length,
        active: roles.filter(r => r.isActive).length,
      },
      areas: {
        total: areas.length,
        active: areas.filter(a => a.isActive).length,
      },
      shops: {
        total: shops.length,
        active: shops.filter(s => s.isActive).length,
      },
      requests: {
        total: requests.length,
        pending: requests.filter(r => r.status === "pending").length,
        approved: requests.filter(r => r.status === "approved").length,
        rejected: requests.filter(r => r.status === "rejected").length,
      },
      notifications: {
        total: notifications.length,
        unread: notifications.filter(n => !n.isRead).length,
      },
      settings: {
        total: settings.length,
        system: settings.filter(s => s.isSystem).length,
        custom: settings.filter(s => !s.isSystem).length,
      },
    };
  },
});

// Create a test user with accounts role (for development)
export const createTestAccountsUser = mutation({
  args: {
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // This should only be used in development
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const now = Date.now();

    // Create user
    const userId = await ctx.db.insert("users", {
      workosId: identity.subject,
      email: args.email,
      firstName: args.firstName || "Test",
      lastName: args.lastName || "Admin",
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    // Find accounts role
    const accountsRole = await ctx.db
      .query("roles")
      .withIndex("by_name", (q) => q.eq("name", "accounts"))
      .first();

    if (accountsRole) {
      // Assign accounts role
      await ctx.db.insert("user_roles", {
        userId,
        roleId: accountsRole._id,
        assignedBy: userId, // Self-assigned for initial setup
        assignedAt: now,
        isActive: true,
      });
    }

    return userId;
  },
});