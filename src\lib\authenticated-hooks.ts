/**
 * Authenticated React hooks for Convex queries
 * 
 * These hooks automatically include WorkOS authentication
 * and should be used instead of direct useQuery calls.
 */

import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { getCurrentWorkOSUserId } from './convex-auth';
import { useEffect, useState } from 'react';

/**
 * Hook to get current WorkOS user ID with improved reliability
 */
export const useWorkOSUserId = () => {
  const [workosUserId, setWorkosUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const getWorkOSUserId = async () => {
      try {
        console.log('[useWorkOSUserId] Starting to fetch WorkOS user ID...');
        setIsLoading(true);
        setError(null);
        
        const userId = await getCurrentWorkOSUserId();
        
        if (isMounted) {
          console.log('[useWorkOSUserId] Successfully got WorkOS user ID:', userId);
          setWorkosUserId(userId);
          setIsLoading(false);
        }
      } catch (error) {
        console.error('[useWorkOSUserId] Error getting WorkOS user ID:', error);
        if (isMounted) {
          setError(error instanceof Error ? error : new Error('Failed to get WorkOS user ID'));
          setWorkosUserId(null);
          setIsLoading(false);
        }
      }
    };

    getWorkOSUserId();

    return () => {
      isMounted = false;
    };
  }, []);

  console.log('[useWorkOSUserId] Current state:', { workosUserId, isLoading, error: error?.message });
  return { workosUserId, isLoading, error };
};

/**
 * Authenticated area hooks
 */
export const useAuthenticatedAreasQuery = (filters?: any) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.areas.getAreas, 
    workosUserId ? { 
      ...filters, 
      workosUserId,
      isActive: filters?.isActive !== undefined ? filters.isActive : true // Default to active only
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

export const useAuthenticatedAreaQuery = (areaId: string) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.areas.getArea, 
    workosUserId && areaId ? { 
      areaId: areaId as Id<"areas">,
      workosUserId 
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

export const useAuthenticatedAccessibleAreasQuery = () => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.areas.getAccessibleAreas, 
    workosUserId ? { workosUserId } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

export const useAuthenticatedAreaUsersQuery = (areaId: string) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.areas.getAreaUsers, 
    workosUserId && areaId ? { 
      areaId: areaId as Id<"areas">,
      workosUserId 
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

export const useAuthenticatedAreaShopsQuery = (areaId: string) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.areas.getAreaShops, 
    workosUserId && areaId ? { 
      areaId: areaId as Id<"areas">,
      workosUserId 
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

/**
 * Authenticated shop hooks
 */
export const useAuthenticatedShopsQuery = (filters?: any) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.shops.getShops, 
    workosUserId ? { 
      ...filters, 
      workosUserId,
      isActive: filters?.isActive !== undefined ? filters.isActive : true // Default to active only
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

export const useAuthenticatedShopQuery = (shopId: string) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.shops.getShop, 
    workosUserId && shopId ? { 
      shopId: shopId as Id<"shops">,
      workosUserId 
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

export const useAuthenticatedShopsByAreaQuery = (areaId: string) => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  const query = useQuery(
    api.shops.getShops, 
    workosUserId && areaId ? { 
      areaId: areaId as Id<"areas">,
      workosUserId,
      isActive: true // Default to active shops only
    } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
    error: userError || query?.error,
  };
};

/**
 * Shop manager specific hooks - for getting only their assigned shops/areas
 * These are the critical hooks for pre-population
 */
export const useAuthenticatedMyShopsQuery = () => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  console.log('[useAuthenticatedMyShopsQuery] Hook called with:', { workosUserId, userLoading, userError: userError?.message });
  
  const query = useQuery(
    api.shops.getMyShops, 
    workosUserId ? { workosUserId } : "skip"
  );
  
  const isLoading = userLoading || query === undefined;
  const hasError = userError || query?.error;
  const data = query && !hasError ? query : undefined;
  
  console.log('[useAuthenticatedMyShopsQuery] Query state:', {
    userLoading,
    queryUndefined: query === undefined,
    queryData: query,
    isLoading,
    hasError: !!hasError,
    errorMessage: hasError?.message || userError?.message,
    finalData: data
  });
  
  return {
    data,
    isLoading,
    error: hasError,
  };
};

export const useAuthenticatedMyAreasQuery = () => {
  const { workosUserId, isLoading: userLoading, error: userError } = useWorkOSUserId();
  
  console.log('[useAuthenticatedMyAreasQuery] Hook called with:', { workosUserId, userLoading, userError: userError?.message });
  
  const query = useQuery(
    api.areas.getMyAreas, 
    workosUserId ? { workosUserId } : "skip"
  );
  
  const isLoading = userLoading || query === undefined;
  const hasError = userError || query?.error;
  const data = query && !hasError ? query : undefined;
  
  console.log('[useAuthenticatedMyAreasQuery] Query state:', {
    userLoading,
    queryUndefined: query === undefined,
    queryData: query,
    isLoading,
    hasError: !!hasError,
    errorMessage: hasError?.message || userError?.message,
    finalData: data
  });
  
  return {
    data,
    isLoading,
    error: hasError,
  };
};