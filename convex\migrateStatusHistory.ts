import { mutation } from "./_generated/server";
import { v } from "convex/values";

// Migration script to populate status history for existing requests
export const migrateExistingRequestsToStatusHistory = mutation({
  args: {
    dryRun: v.optional(v.boolean()), // Set to true to see what would be migrated without actually doing it
  },
  handler: async (ctx, args) => {
    const dryRun = args.dryRun ?? true;
    
    console.log(`[Migration] Starting status history migration (dryRun: ${dryRun})`);
    
    // Get all existing requests
    const requests = await ctx.db.query("requests").collect();
    
    console.log(`[Migration] Found ${requests.length} requests to process`);
    
    let migratedCount = 0;
    let skippedCount = 0;
    
    for (const request of requests) {
      try {
        // Check if this request already has status history
        const existingHistory = await ctx.db
          .query("request_status_history")
          .withIndex("by_request", (q) => q.eq("requestId", request._id))
          .first();
        
        if (existingHistory) {
          console.log(`[Migration] Skipping request ${request._id} - already has status history`);
          skippedCount++;
          continue;
        }
        
        const statusEntries = [];
        
        // 1. Add initial creation status (pending)
        statusEntries.push({
          requestId: request._id,
          fromStatus: undefined,
          toStatus: "pending" as const,
          changedBy: request.requestedBy,
          changedAt: request.createdAt,
          reason: "Request created",
          metadata: {
            migrated: true,
            originalCreatedAt: request.createdAt,
          }
        });
        
        // 2. Add resubmission events if they exist
        if (request.resubmissionHistory && request.resubmissionHistory.length > 0) {
          for (const resubmission of request.resubmissionHistory) {
            statusEntries.push({
              requestId: request._id,
              fromStatus: resubmission.previousStatus as any,
              toStatus: "resubmitted" as const,
              changedBy: resubmission.resubmittedBy,
              changedAt: resubmission.resubmittedAt,
              reason: resubmission.reason || "Request resubmitted",
              metadata: {
                migrated: true,
                originalResubmissionData: resubmission,
              }
            });
          }
        }
        
        // 3. Add rejection event if it exists
        if (request.rejectedBy && request.rejectedAt) {
          statusEntries.push({
            requestId: request._id,
            fromStatus: "pending" as const, // Assume it was pending before rejection
            toStatus: "rejected" as const,
            changedBy: request.rejectedBy,
            changedAt: request.rejectedAt,
            reason: request.rejectionReason || "Request rejected",
            metadata: {
              migrated: true,
              originalRejectionReason: request.rejectionReason,
            }
          });
        }
        
        // 4. Add approval event if it exists
        if (request.approvedBy && request.approvedAt) {
          const fromStatus = request.rejectedBy ? "resubmitted" : "pending";
          statusEntries.push({
            requestId: request._id,
            fromStatus: fromStatus as any,
            toStatus: "approved" as const,
            changedBy: request.approvedBy,
            changedAt: request.approvedAt,
            reason: "Request approved",
            metadata: {
              migrated: true,
              originalApprovedAt: request.approvedAt,
            }
          });
        }
        
        // 5. Add final status if it's different from what we've recorded
        const lastEntry = statusEntries[statusEntries.length - 1];
        if (lastEntry.toStatus !== request.status) {
          // There's a final status we haven't accounted for
          statusEntries.push({
            requestId: request._id,
            fromStatus: lastEntry.toStatus,
            toStatus: request.status as any,
            changedBy: request.approvedBy || request.rejectedBy || request.requestedBy,
            changedAt: request.updatedAt,
            reason: `Request marked as ${request.status}`,
            metadata: {
              migrated: true,
              inferredStatusChange: true,
            }
          });
        }
        
        // Sort entries chronologically
        statusEntries.sort((a, b) => a.changedAt - b.changedAt);
        
        if (!dryRun) {
          // Insert all status history entries
          for (const entry of statusEntries) {
            await ctx.db.insert("request_status_history", entry);
          }
        }
        
        console.log(`[Migration] ${dryRun ? 'Would migrate' : 'Migrated'} request ${request.ticketNumber} with ${statusEntries.length} status entries`);
        migratedCount++;
        
      } catch (error) {
        console.error(`[Migration] Error processing request ${request._id}:`, error);
      }
    }
    
    const result = {
      totalRequests: requests.length,
      migratedCount,
      skippedCount,
      dryRun,
    };
    
    console.log(`[Migration] Complete:`, result);
    return result;
  },
});

// Helper function to check migration status
export const checkMigrationStatus = mutation({
  handler: async (ctx) => {
    const totalRequests = await ctx.db.query("requests").collect();
    const requestsWithHistory = await ctx.db.query("request_status_history").collect();
    
    // Group by request ID to count unique requests with history
    const uniqueRequestsWithHistory = new Set(requestsWithHistory.map(h => h.requestId));
    
    return {
      totalRequests: totalRequests.length,
      requestsWithHistory: uniqueRequestsWithHistory.size,
      totalStatusEntries: requestsWithHistory.length,
      migrationComplete: totalRequests.length === uniqueRequestsWithHistory.size,
    };
  },
});