import * as z from 'zod';

// Shop validation schema
export const shopSchema = z.object({
  name: z.string()
    .min(2, { message: 'Shop name must be at least 2 characters' })
    .max(100, { message: 'Shop name must be less than 100 characters' })
    .trim(),
  location: z.string()
    .min(2, { message: 'Location must be at least 2 characters' })
    .max(200, { message: 'Location must be less than 200 characters' })
    .trim(),
  areaId: z.string({
    required_error: 'Area is required',
    invalid_type_error: 'Area must be selected'
  })
});

// Area validation schema
export const areaSchema = z.object({
  name: z.string()
    .min(2, { message: 'Area name must be at least 2 characters' })
    .max(100, { message: 'Area name must be less than 100 characters' })
    .trim()
});

// Type definitions based on the schemas
export type ShopFormValues = z.infer<typeof shopSchema>;
export type AreaFormValues = z.infer<typeof areaSchema>;
