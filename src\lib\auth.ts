
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from './auth-context';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

// User role types
export type UserRole = 'shop_manager' | 'shop_support' | 'accounts' | 'watcher';

export const useRequireAuth = (allowedRoles?: UserRole[]) => {
  const { user, userWithRoles, isLoading } = useAuth();
  const [isAuthorized, setIsAuthorized] = useState<boolean>(false);
  const navigate = useNavigate();

  // Get user session info from Convex
  const sessionInfo = useQuery(api.auth.getSessionInfo);

  useEffect(() => {
    console.log('useRequireAuth Debug:', { 
      user, 
      sessionInfo, 
      userWithRoles,
      isLoading, 
      allowedRoles,
      sessionInfoRoles: sessionInfo?.roles?.map(role => role.name),
      userWithRolesRoles: userWithRoles?.roles?.map(role => role.name)
    });
    
    // Skip check during initial load
    if (isLoading || (user && !userWithRoles)) {
      console.log('useRequireAuth: Skipping check - loading or no userWithRoles');
      return;
    }

    // If no user, redirect to login
    if (!user || !userWithRoles) {
      console.log('useRequireAuth: No user or userWithRoles, redirecting to login');
      navigate('/login');
      return;
    }

    // If roles are specified, check if user has required role
    if (allowedRoles && allowedRoles.length > 0) {
      const userRoles = userWithRoles.roles?.map(role => role.name as UserRole) || [];
      const hasRequiredRole = allowedRoles.some(role => userRoles.includes(role));
      
      console.log('useRequireAuth: Role check:', { 
        userRoles, 
        allowedRoles, 
        hasRequiredRole 
      });

      if (!hasRequiredRole) {
        // User doesn't have required role
        console.log('useRequireAuth: User lacks required role, redirecting to dashboard');
        navigate('/dashboard');
        return;
      }
    }

    // User is authenticated and has required role (if specified) or no roles required
    console.log('useRequireAuth: Authorization successful');
    setIsAuthorized(true);
  }, [user, userWithRoles, isLoading, navigate, allowedRoles]);

  return { user, isAuthorized, isLoading };
};

// Function to check if a user can approve a request based on role and amount
export const canApproveRequest = (
  roles: string[],
  paymentMethod: string,
  amount: number,
  settings?: { momoThreshold?: number; bankThreshold?: number }
): boolean => {
  // Default thresholds
  const momoThreshold = settings?.momoThreshold || 5000;
  const bankThreshold = settings?.bankThreshold || 10000;

  // Check if user has accounts role
  if (roles.includes('accounts')) {
    // Account managers can approve any request
    return true;
  }

  // Check if user has shop_support role
  if (roles.includes('shop_support')) {
    // Shop support can only approve mobile money requests below threshold
    if (paymentMethod === 'mobile_money' && amount <= momoThreshold) {
      return true;
    }

    // Shop support cannot approve bank transfers
    return false;
  }

  // All other roles cannot approve
  return false;
};

// Function to check if a user can create requests based on role
export const canCreateRequests = (roles: string[]): boolean => {
  return roles.includes('shop_manager');
};

// Function to check if a user can manage other users based on role
export const canManageUsers = (roles: string[]): boolean => {
  return roles.includes('accounts');
};

// Function to check if a user can view reports based on role
export const canViewReports = (roles: string[]): boolean => {
  return roles.some(role => ['accounts', 'shop_support', 'watcher'].includes(role));
};

// Function to check if a user can manage shops based on role
export const canManageShops = (roles: string[]): boolean => {
  return roles.includes('accounts');
};

// Function to check if a user can manage areas based on role
export const canManageAreas = (roles: string[]): boolean => {
  return roles.includes('accounts');
};

// Function to get user permissions based on roles
export const getUserPermissions = (roles: string[]): string[] => {
  const permissions = new Set<string>();

  roles.forEach(role => {
    switch (role) {
      case 'shop_manager':
        permissions.add('request:create');
        permissions.add('request:view:own');
        permissions.add('request:resubmit');
        permissions.add('reports:view:own');
        break;

      case 'shop_support':
        permissions.add('request:view:area');
        permissions.add('request:approve:mobile');
        permissions.add('request:reject');
        permissions.add('reports:view:area');
        permissions.add('shop:view');
        break;

      case 'accounts':
        permissions.add('request:view:all');
        permissions.add('request:approve:all');
        permissions.add('request:reject');
        permissions.add('user:view');
        permissions.add('user:create');
        permissions.add('user:update');
        permissions.add('user:invite');
        permissions.add('user:assign:role');
        permissions.add('user:assign:area');
        permissions.add('shop:view');
        permissions.add('shop:create');
        permissions.add('shop:update');
        permissions.add('shop:delete');
        permissions.add('shop:assign:manager');
        permissions.add('area:view');
        permissions.add('area:create');
        permissions.add('area:update');
        permissions.add('area:delete');
        permissions.add('settings:view');
        permissions.add('settings:update');
        permissions.add('reports:view:all');
        permissions.add('reports:export');
        break;

      case 'watcher':
        permissions.add('reports:view:all');
        break;
    }
  });

  return Array.from(permissions);
};

// Hook to get current user's permissions
export const useUserPermissions = () => {
  const sessionInfo = useQuery(api.auth.getSessionInfo);
  
  if (!sessionInfo) {
    return [];
  }

  return sessionInfo.permissions || [];
};

// Hook to check if user has specific permission
export const useHasPermission = (permission: string) => {
  const permissions = useUserPermissions();
  return permissions.includes(permission);
};

// Hook to check if user has any of the given permissions
export const useHasAnyPermission = (permissionList: string[]) => {
  const permissions = useUserPermissions();
  return permissionList.some(permission => permissions.includes(permission));
};
