/**
 * WorkOS-Convex synchronization utilities
 * 
 * This module handles syncing roles and permissions between WorkOS and Convex
 */

import { useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';

// WorkOS API endpoints
const WORKOS_API_BASE = 'https://api.workos.com';

/**
 * Fetch user roles from WorkOS Directory API
 */
export const fetchUserRolesFromWorkOS = async (userId: string, accessToken: string): Promise<string[]> => {
  try {
    const response = await fetch(`${WORKOS_API_BASE}/user_management/users/${userId}`, {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Failed to fetch user from WorkOS:', response.status, response.statusText);
      return [];
    }

    const userData = await response.json();
    
    // Extract role names from the user data
    const roleNames = userData.role_names || [];
    console.log('Fetched roles from WorkOS:', roleNames);
    
    return roleNames;
  } catch (error) {
    console.error('Error fetching user roles from WorkOS:', error);
    return [];
  }
};

/**
 * Hook to sync user roles from WorkOS to Convex
 */
export const useWorkOSRoleSync = () => {
  const syncUserMutation = useMutation(api.auth.syncUser);
  const initializeRolesMutation = useMutation(api.workosSync.initializeRoles);

  const syncUserRoles = async (
    workosId: string, 
    email: string, 
    firstName?: string, 
    lastName?: string, 
    profilePictureUrl?: string,
    accessToken?: string
  ) => {
    try {
      // First, ensure roles are initialized in Convex
      await initializeRolesMutation({});

      // Try to fetch roles from WorkOS if we have an access token
      let roleNames: string[] = [];
      if (accessToken && workosId) {
        roleNames = await fetchUserRolesFromWorkOS(workosId, accessToken);
      }

      // Sync user data with roles to Convex
      const syncData: any = {
        workosId,
        email,
      };
      
      // Only include optional fields if they have values
      if (firstName) {
        syncData.firstName = firstName;
      }
      if (lastName) {
        syncData.lastName = lastName;
      }
      if (profilePictureUrl) {
        syncData.profilePicture = profilePictureUrl;
      }
      if (roleNames.length > 0) {
        syncData.roleNames = roleNames;
      }
      
      await syncUserMutation(syncData);
      
      return { success: true, roleNames };
    } catch (error) {
      console.error('Error syncing user roles:', error);
      return { success: false, error };
    }
  };

  return { syncUserRoles };
};

/**
 * Initialize roles in Convex database
 */
export const useInitializeRoles = () => {
  const initializeRolesMutation = useMutation(api.workosSync.initializeRoles);

  const initializeRoles = async () => {
    try {
      const result = await initializeRolesMutation({});
      console.log('Roles initialized:', result);
      return result;
    } catch (error) {
      console.error('Error initializing roles:', error);
      throw error;
    }
  };

  return { initializeRoles };
};

/**
 * Manual role assignment for testing purposes
 */
export const useManualRoleAssignment = () => {
  const syncUserRoleFromWorkOS = useMutation(api.workosSync.syncUserRoleFromWorkOS);

  const assignRoleToUser = async (workosUserId: string, roleNames: string[]) => {
    try {
      const result = await syncUserRoleFromWorkOS({
        workosUserId,
        roleNames,
      });
      console.log('Role assigned:', result);
      return result;
    } catch (error) {
      console.error('Error assigning role:', error);
      throw error;
    }
  };

  return { assignRoleToUser };
};