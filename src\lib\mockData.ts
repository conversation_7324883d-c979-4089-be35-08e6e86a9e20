/**
 * Mock Data Provider
 *
 * This file provides mock data for the application to use instead of Supabase.
 * It includes sample data for all entities and helper functions to simulate CRUD operations.
 */

import {
  User,
  Area,
  Shop,
  Request,
  RequestStatus,
  PaymentMethod,
  UserRole,
  AppNotification,
  DashboardStats
} from '@/lib/types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: 'user-1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'accounts',
    phone: '+233 20 123 4567',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'shop_manager',
    phone: '+233 20 765 4321',
    createdAt: new Date('2023-01-02'),
    updatedAt: new Date('2023-01-02')
  },
  {
    id: 'user-3',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'shop_support',
    phone: '+233 20 555 5555',
    createdAt: new Date('2023-01-03'),
    updatedAt: new Date('2023-01-03')
  },
  {
    id: 'user-4',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'accounts',
    phone: '+233 20 999 8888',
    createdAt: new Date('2023-01-04'),
    updatedAt: new Date('2023-01-04')
  },
  {
    id: 'user-5',
    name: 'Admin Manager',
    email: '<EMAIL>',
    role: 'accounts',
    phone: '+233 20 111 2222',
    createdAt: new Date('2023-01-05'),
    updatedAt: new Date('2023-01-05')
  },
  {
    id: 'user-6',
    name: 'Manager User',
    email: '<EMAIL>',
    role: 'shop_manager',
    phone: '+233 20 333 4444',
    createdAt: new Date('2023-01-06'),
    updatedAt: new Date('2023-01-06')
  }
];

// Mock Areas
export const mockAreas: Area[] = [
  {
    id: 'area-1',
    name: 'Accra Central',
    description: 'Central business district of Accra',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    id: 'area-2',
    name: 'Kumasi',
    description: 'Capital city of the Ashanti Region',
    createdAt: new Date('2023-01-02'),
    updatedAt: new Date('2023-01-02')
  },
  {
    id: 'area-3',
    name: 'Takoradi',
    description: 'Capital city of the Western Region',
    createdAt: new Date('2023-01-03'),
    updatedAt: new Date('2023-01-03')
  }
];

// Mock Shops
export const mockShops: Shop[] = [
  {
    id: 'shop-1',
    name: 'Makola Market Shop',
    location: 'Makola Market, Accra',
    areaId: 'area-1',
    areaName: 'Accra Central',
    managerId: 'user-2',
    managerName: 'Jane Smith',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-01-01')
  },
  {
    id: 'shop-2',
    name: 'Kejetia Market Shop',
    location: 'Kejetia Market, Kumasi',
    areaId: 'area-2',
    areaName: 'Kumasi',
    managerId: 'user-2',
    managerName: 'Jane Smith',
    createdAt: new Date('2023-01-02'),
    updatedAt: new Date('2023-01-02')
  },
  {
    id: 'shop-3',
    name: 'Takoradi Mall Shop',
    location: 'Takoradi Mall',
    areaId: 'area-3',
    areaName: 'Takoradi',
    managerId: 'user-2',
    managerName: 'Jane Smith',
    createdAt: new Date('2023-01-03'),
    updatedAt: new Date('2023-01-03')
  }
];

// Mock Requests
export const mockRequests: Request[] = [
  {
    id: 'req-1',
    title: 'Weekly Cash Request',
    amount: 5000,
    status: 'approved',
    paymentMethod: 'mobile_money',
    shopId: 'shop-1',
    shopName: 'Makola Market Shop',
    areaId: 'area-1',
    areaName: 'Accra Central',
    createdBy: 'user-2',
    createdByName: 'Jane Smith',
    approvedBy: 'user-1',
    approvedByName: 'Admin User',
    rejectedBy: undefined,
    rejectedByName: null,
    createdAt: new Date('2024-12-01'),
    updatedAt: new Date('2024-12-02'),
    approvedAt: new Date('2024-12-02'),
    rejectedAt: undefined,
    approvalReason: 'Approved for weekly operations',
    rejectionReason: undefined,
    comments: 'Weekly cash request for shop operations',
    attachments: [],
    adminActionHistory: [
      {
        id: 'action-1-1',
        type: 'review',
        timestamp: new Date('2024-12-01T10:30:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Request received and under review. Checking documentation and budget allocation.',
        previousStatus: 'pending',
        newStatus: 'pending'
      },
      {
        id: 'action-1-2',
        type: 'approve',
        timestamp: new Date('2024-12-02T09:15:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Approved for weekly operations. All documentation is in order and budget is available.',
        previousStatus: 'pending',
        newStatus: 'approved'
      }
    ]
  },
  {
    id: 'req-2',
    title: 'Emergency Repairs',
    amount: 8000,
    status: 'pending',
    paymentMethod: 'bank_transfer',
    shopId: 'shop-2',
    shopName: 'Kejetia Market Shop',
    areaId: 'area-2',
    areaName: 'Kumasi',
    createdBy: 'user-2',
    createdByName: 'Jane Smith',
    approvedBy: undefined,
    approvedByName: null,
    rejectedBy: undefined,
    rejectedByName: null,
    createdAt: new Date('2024-12-05'),
    updatedAt: new Date('2024-12-05'),
    approvedAt: undefined,
    rejectedAt: undefined,
    approvalReason: undefined,
    rejectionReason: undefined,
    comments: 'Funds needed for emergency shop repairs',
    attachments: []
  },
  {
    id: 'req-3',
    title: 'Inventory Restock',
    amount: 12000,
    status: 'rejected',
    paymentMethod: 'bank_transfer',
    shopId: 'shop-3',
    shopName: 'Takoradi Mall Shop',
    areaId: 'area-3',
    areaName: 'Takoradi',
    createdBy: 'user-2',
    createdByName: 'Jane Smith',
    approvedBy: undefined,
    approvedByName: null,
    rejectedBy: 'user-1',
    rejectedByName: 'Admin User',
    createdAt: new Date('2024-12-10'),
    updatedAt: new Date('2024-12-11'),
    approvedAt: undefined,
    rejectedAt: new Date('2024-12-11'),
    approvalReason: undefined,
    rejectionReason: 'Budget exceeded for this month',
    comments: 'Funds for restocking inventory',
    attachments: [],
    adminActionHistory: [
      {
        id: 'action-3-1',
        type: 'review',
        timestamp: new Date('2024-12-10T14:20:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Reviewing inventory restock request. Checking current month budget allocation.',
        previousStatus: 'pending',
        newStatus: 'pending'
      },
      {
        id: 'action-3-2',
        type: 'comment',
        timestamp: new Date('2024-12-11T08:45:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Amount requested exceeds monthly budget limit. Current available budget is GHS 8,000.',
        previousStatus: 'pending',
        newStatus: 'pending'
      },
      {
        id: 'action-3-3',
        type: 'reject',
        timestamp: new Date('2024-12-11T09:30:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Budget exceeded for this month. Please resubmit with reduced amount or wait for next month.',
        previousStatus: 'pending',
        newStatus: 'rejected'
      }
    ]
  },
  {
    id: 'req-4',
    title: 'Equipment Purchase',
    amount: 15000,
    status: 'resubmitted',
    paymentMethod: 'mobile_money',
    shopId: 'shop-1',
    shopName: 'Makola Market Shop',
    areaId: 'area-1',
    areaName: 'Accra Central',
    createdBy: 'user-2',
    createdByName: 'Jane Smith',
    approvedBy: undefined,
    approvedByName: null,
    rejectedBy: undefined,
    rejectedByName: null,
    createdAt: new Date('2024-12-15'),
    updatedAt: new Date('2024-12-18'),
    approvedAt: undefined,
    rejectedAt: undefined,
    approvalReason: undefined,
    rejectionReason: undefined,
    comments: 'New equipment needed for shop operations',
    attachments: [],
    adminActionHistory: [
      {
        id: 'action-4-1',
        type: 'review',
        timestamp: new Date('2024-12-15T11:00:00'),
        adminId: 'user-4',
        adminName: 'Support Admin',
        adminRole: 'shop_support',
        comment: 'Initial review of equipment purchase request. Checking equipment specifications and necessity.',
        previousStatus: 'pending',
        newStatus: 'pending'
      },
      {
        id: 'action-4-2',
        type: 'comment',
        timestamp: new Date('2024-12-16T09:30:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Amount of GHS 20,000 exceeds current budget allocation. Maximum available is GHS 15,000.',
        previousStatus: 'pending',
        newStatus: 'pending'
      },
      {
        id: 'action-4-3',
        type: 'reject',
        timestamp: new Date('2024-12-16T14:15:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Amount too high for current budget. Please reduce to GHS 15,000 or below and resubmit.',
        previousStatus: 'pending',
        newStatus: 'rejected'
      },
      {
        id: 'action-4-4',
        type: 'comment',
        timestamp: new Date('2024-12-18T10:45:00'),
        adminId: 'user-1',
        adminName: 'Admin User',
        adminRole: 'accounts',
        comment: 'Request has been resubmitted with reduced amount. Now reviewing updated submission.',
        previousStatus: 'rejected',
        newStatus: 'resubmitted'
      }
    ],
    resubmissionHistory: [
      {
        timestamp: new Date('2024-12-18'),
        changes: {
          amount: { from: 20000, to: 15000 },
          comments: { from: 'New equipment needed', to: 'New equipment needed for shop operations' }
        },
        notes: 'Reduced amount as requested by admin',
        previousRejectionReason: 'Amount too high for current budget',
        adminComments: [
          {
            id: 'action-4-4',
            type: 'comment',
            timestamp: new Date('2024-12-18T10:45:00'),
            adminId: 'user-1',
            adminName: 'Admin User',
            adminRole: 'accounts',
            comment: 'Request has been resubmitted with reduced amount. Now reviewing updated submission.',
            previousStatus: 'rejected',
            newStatus: 'resubmitted'
          }
        ]
      }
    ]
  }
];

// Mock Notifications
export const mockNotifications: AppNotification[] = [
  {
    id: 'notif-1',
    userId: 'user-1',
    title: 'New Request',
    message: 'A new request has been submitted for approval',
    isRead: false,
    createdAt: new Date('2023-02-05'),
    updatedAt: new Date('2023-02-05'),
    requestId: 'req-2'
  },
  {
    id: 'notif-2',
    userId: 'user-2',
    title: 'Request Approved',
    message: 'Your request has been approved',
    isRead: true,
    createdAt: new Date('2023-02-02'),
    updatedAt: new Date('2023-02-03'),
    requestId: 'req-1'
  },
  {
    id: 'notif-3',
    userId: 'user-2',
    title: 'Request Rejected',
    message: 'Your request has been rejected',
    isRead: false,
    createdAt: new Date('2023-02-11'),
    updatedAt: new Date('2023-02-11'),
    requestId: 'req-3'
  }
];

// Mock Settings
export const mockSettings = {
  id: 1,
  momoThreshold: 5000,
  bankThreshold: 10000,
  whitelistedDomains: ['kmkentertainment.com', 'mybet.africa'],
  updatedBy: 'user-1',
  updatedAt: new Date('2023-01-01')
};

// Mock Dashboard Stats
export const mockStats: DashboardStats = {
  totalAmount: 5000,
  totalRequests: 3,
  pendingRequests: 1,
  approvedRequests: 1,
  rejectedRequests: 1,
  resubmittedRequests: 0,
  mobileMoneyAmount: 5000,
  bankTransferAmount: 0,
  uniqueAreas: 3,
  uniqueShops: 3,
  monthlyStats: [
    { month: 1, total: 0, count: 0 },
    { month: 2, total: 0, count: 0 },
    { month: 3, total: 0, count: 0 },
    { month: 4, total: 0, count: 0 },
    { month: 5, total: 0, count: 0 },
    { month: 6, total: 0, count: 0 },
    { month: 7, total: 0, count: 0 },
    { month: 8, total: 0, count: 0 },
    { month: 9, total: 0, count: 0 },
    { month: 10, total: 0, count: 0 },
    { month: 11, total: 0, count: 0 },
    { month: 12, total: 5000, count: 1 }
  ]
};

// Mock Auth State - Start unauthenticated so users can see the login page
export const mockAuthState = {
  user: null,
  isAuthenticated: false
};
