import { query, mutation } from "./_generated/server";
import { v } from "convex/values";
import { requirePermissionWithWorkOSUser } from "./permissions";
import { PERMISSIONS } from "./permissions";

/**
 * Test authentication with WorkOS user ID
 */
export const testCreateAreaWithWorkOSAuth = mutation({
  args: {
    name: v.string(),
    workosUserId: v.string(),
  },
  handler: async (ctx, args) => {
    console.log("Testing area creation with WorkOS auth for user:", args.workosUserId);
    
    try {
      // Test the authentication and permission check
      const userWithPermissions = await requirePermissionWithWorkOSUser(
        ctx,
        PERMISSIONS.AREA_CREATE,
        args.workosUserId
      );
      
      console.log("Authentication successful for user:", userWithPermissions.user.email);
      console.log("User permissions:", userWithPermissions.permissions);
      
      // Check for duplicate area name
      const existingArea = await ctx.db
        .query("areas")
        .withIndex("by_name", (q) => q.eq("name", args.name))
        .first();

      if (existingArea) {
        throw new Error("Area with this name already exists");
      }

      const now = Date.now();
      const areaId = await ctx.db.insert("areas", {
        name: args.name,
        description: `Test area created via WorkOS auth`,
        isActive: true,
        createdAt: now,
        updatedAt: now,
        createdBy: userWithPermissions.user._id,
      });

      console.log("Area created successfully:", areaId);
      
      return {
        success: true,
        areaId,
        message: "Area created successfully with WorkOS authentication",
        user: userWithPermissions.user.email,
        permissions: userWithPermissions.permissions.length
      };
      
    } catch (error) {
      console.error("Error in testCreateAreaWithWorkOSAuth:", error);
      return {
        success: false,
        error: error.message,
        message: "Failed to create area"
      };
    }
  },
});

// Test permission check with WorkOS user ID
export const testPermissionWithWorkOSUser = query({
  args: {
    workosUserId: v.string(),
    permission: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      const userWithPermissions = await requirePermissionWithWorkOSUser(
        ctx,
        args.permission,
        args.workosUserId
      );
      
      return {
        success: true,
        hasPermission: true,
        user: {
          id: userWithPermissions.user._id,
          email: userWithPermissions.user.email,
          workosId: userWithPermissions.user.workosId
        },
        roles: userWithPermissions.roles.map(r => r.name),
        permissions: userWithPermissions.permissions,
        message: `User has permission: ${args.permission}`
      };
      
    } catch (error) {
      return {
        success: false,
        hasPermission: false,
        error: error.message,
        message: `Permission check failed for: ${args.permission}`
      };
    }
  },
});