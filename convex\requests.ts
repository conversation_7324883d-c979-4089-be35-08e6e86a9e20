import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { api } from "./_generated/api";
import {
  requireAuth,
  requirePermission,
  requireAnyPermission,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  ROLES,
  canAccessShop,
  canManageShop,
  getAccessibleShopIds,
  getAccessibleAreaIds,
} from "./permissions";
import { recordStatusChange, getRequestStatusHistory } from "./requestStatusHistory";

// Get requests with role-based filtering
export const getRequests = query({
  args: {
    status: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("resubmitted"),
      v.literal("paid"),
      v.literal("cancelled")
    )),
    shopId: v.optional(v.id("shops")),
    areaId: v.optional(v.id("areas")),
    paymentMethod: v.optional(v.union(v.literal("mobile_money"), v.literal("bank_transfer"))),
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAnyPermission(ctx, [
      PERMISSIONS.REQUEST_VIEW_OWN,
      PERMISSIONS.REQUEST_VIEW_SHOP,
      PERMISSIONS.REQUEST_VIEW_AREA,
      PERMISSIONS.REQUEST_VIEW_ALL,
    ]);

    let query = ctx.db.query("requests");

    // Apply role-based filtering
    if (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL)) {
      // Accounts role - can see all requests
    } else if (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_AREA)) {
      // Shop support - can see requests in their areas
      const accessibleAreaIds = await getAccessibleAreaIds(ctx);
      if (accessibleAreaIds.length === 0) {
        return [];
      }
      query = query.filter((q) => 
        q.or(...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId)))
      );
    } else if (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_SHOP)) {
      // Shop managers - can see requests in their shops
      const accessibleShopIds = await getAccessibleShopIds(ctx);
      if (accessibleShopIds.length === 0) {
        return [];
      }
      query = query.filter((q) => 
        q.or(...accessibleShopIds.map(shopId => q.eq(q.field("shopId"), shopId)))
      );
    } else {
      // Can only see own requests
      query = query.filter((q) => q.eq(q.field("requestedBy"), userWithPermissions.user._id));
    }

    // Apply additional filters
    if (args.status) {
      query = query.filter((q) => q.eq(q.field("status"), args.status));
    }
    if (args.shopId) {
      query = query.filter((q) => q.eq(q.field("shopId"), args.shopId));
    }
    if (args.areaId) {
      query = query.filter((q) => q.eq(q.field("areaId"), args.areaId));
    }
    if (args.paymentMethod) {
      query = query.filter((q) => q.eq(q.field("paymentMethod"), args.paymentMethod));
    }

    const requests = await query
      .order("desc")
      .take(args.limit || 50);

    // Enrich with related data
    const enrichedRequests = await Promise.all(
      requests.map(async (request) => {
        const [shop, area, requestedBy, approvedBy, rejectedBy] = await Promise.all([
          ctx.db.get(request.shopId),
          ctx.db.get(request.areaId),
          ctx.db.get(request.requestedBy),
          request.approvedBy ? ctx.db.get(request.approvedBy) : null,
          request.rejectedBy ? ctx.db.get(request.rejectedBy) : null,
        ]);

        return {
          ...request,
          shop,
          area,
          requestedBy,
          approvedBy,
          rejectedBy,
        };
      })
    );

    return enrichedRequests;
  },
});

// Get single request by ID
export const getRequest = query({
  args: { requestId: v.id("requests") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAnyPermission(ctx, [
      PERMISSIONS.REQUEST_VIEW_OWN,
      PERMISSIONS.REQUEST_VIEW_SHOP,
      PERMISSIONS.REQUEST_VIEW_AREA,
      PERMISSIONS.REQUEST_VIEW_ALL,
    ]);

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      return null;
    }

    // Check access permissions
    const canAccess = 
      userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL) ||
      (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_AREA) && 
       await canAccessShop(ctx, request.shopId)) ||
      (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_SHOP) && 
       await canAccessShop(ctx, request.shopId)) ||
      (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_OWN) && 
      request.requestedBy === userWithPermissions.user._id);

    if (!canAccess) {
      throw new Error("Access denied");
    }

    // Enrich with related data
    const [shop, area, requestedBy, approvedBy, rejectedBy] = await Promise.all([
      ctx.db.get(request.shopId),
      ctx.db.get(request.areaId),
      ctx.db.get(request.requestedBy),
      request.approvedBy ? ctx.db.get(request.approvedBy) : null,
      request.rejectedBy ? ctx.db.get(request.rejectedBy) : null,
    ]);

    return {
      ...request,
      shop,
      area,
      requestedBy,
      approvedBy,
      rejectedBy,
    };
  },
});

// Create a new request
export const createRequest = mutation({
  args: {
    title: v.string(),
    ticketId: v.string(),
    productId: v.string(),
    areaId: v.id("areas"),
    shopId: v.id("shops"),
    shopName: v.string(),
    amount: v.number(),
    paymentMethod: v.union(v.literal("mobile_money"), v.literal("bank_transfer")),
    customerName: v.string(),
    customerContact: v.string(),
    networkProvider: v.optional(v.string()), // Now stores network name instead of ID
    bankName: v.optional(v.string()),
    accountNumber: v.optional(v.string()),
    accountHolderName: v.optional(v.string()),
    comments: v.optional(v.string()),
    ticketImage: v.optional(v.string()),
    createdBy: v.optional(v.id("users")),
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log('[createRequest] Creating new request with args:', {
      title: args.title,
      ticketId: args.ticketId,
      paymentMethod: args.paymentMethod,
      networkProvider: args.networkProvider,
      amount: args.amount
    });

    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    // Use the authenticated user's ID as createdBy if not provided
    const createdBy = args.createdBy || userWithPermissions.user._id;

    // Check if user has permission to create requests
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_CREATE)) {
      throw new Error("You don't have permission to create requests");
    }

    // Validate network provider if mobile money is selected
    if (args.paymentMethod === "mobile_money") {
      if (!args.networkProvider) {
        throw new Error("Network provider is required for mobile money payments");
      }
      
      // Validate that the network provider is one of the allowed values
      const validNetworks = ['MTN', 'Telecel', 'AT'];
      if (!validNetworks.includes(args.networkProvider)) {
        throw new Error(`Invalid network provider. Must be one of: ${validNetworks.join(', ')}`);
      }
      
      console.log('[createRequest] Validated network provider:', args.networkProvider);
    }

    // Validate bank details if bank transfer is selected
    if (args.paymentMethod === "bank_transfer") {
      if (!args.bankName || !args.accountNumber || !args.accountHolderName) {
        throw new Error("Bank name, account number, and account holder name are required for bank transfers");
      }
    }

    // Check for duplicate ticket ID
    const existingRequest = await ctx.db
      .query("requests")
      .withIndex("by_ticket_number", (q) => q.eq("ticketNumber", args.ticketId))
      .first();

    if (existingRequest) {
      throw new Error(`A request with ticket ID "${args.ticketId}" already exists`);
    }

    // Verify shop exists and is active
    const shop = await ctx.db.get(args.shopId);
    if (!shop || !shop.isActive) {
      throw new Error("Selected shop is not valid or inactive");
    }

    // Verify area exists and is active
    const area = await ctx.db.get(args.areaId);
    if (!area || !area.isActive) {
      throw new Error("Selected area is not valid or inactive");
    }

    // Verify shop belongs to the selected area
    if (shop.areaId !== args.areaId) {
      throw new Error("Selected shop does not belong to the chosen area");
    }

    const now = Date.now();
    const requestId = await ctx.db.insert("requests", {
      ticketNumber: args.ticketId,
      amount: args.amount,
      paymentMethod: args.paymentMethod,
      status: "pending",
      priority: "medium",
      customerName: args.customerName,
      customerPhone: args.customerContact,
      mobileMoneyNumber: args.paymentMethod === "mobile_money" ? args.customerContact : undefined,
      mobileMoneyProvider: args.networkProvider,
      bankName: args.bankName,
      accountNumber: args.accountNumber,
      accountName: args.accountHolderName,
      shopId: args.shopId,
      areaId: args.areaId,
      requestedBy: createdBy,
      workosUserId: args.workosUserId || userWithPermissions.user.workosUserId,
      requestedAt: now,
      notes: args.comments,
      ticketImageId: args.ticketImage && !args.ticketImage.startsWith("data:") ? args.ticketImage as any : undefined,
      isResubmission: false,
      createdAt: now,
      updatedAt: now,
    });

    // Record initial status
    await recordStatusChange(ctx, {
      requestId,
      toStatus: "pending",
      changedBy: createdBy,
      metadata: {
        ticketId: args.ticketId,
        amount: args.amount,
        paymentMethod: args.paymentMethod,
        networkProvider: args.networkProvider,
      }
    });

    console.log('[createRequest] Successfully created request:', requestId);

    // Schedule email notification (will be sent asynchronously)
    await ctx.scheduler.runAfter(0, api.emailNotificationService.sendComprehensiveNotification, {
      requestId,
      statusType: "request_submitted"
    });

    // Create audit log
    await ctx.db.insert("audit_logs", {
      action: "request_created",
      entityType: "request",
      entityId: requestId,
      userId: userWithPermissions.user._id,
      metadata: {
        ticketId: args.ticketId,
        amount: args.amount,
        paymentMethod: args.paymentMethod,
        networkProvider: args.networkProvider,
      },
      timestamp: Date.now(),
    });

    return requestId;
  },
});

// Approve request
export const approveRequest = mutation({
  args: {
    requestId: v.id("requests"),
    notes: v.optional(v.string()),
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    // Check if user has approval permissions
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_APPROVE_MOBILE) &&
        !userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_APPROVE_ALL)) {
      throw new Error("You don't have permission to approve requests");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    if (request.status !== "pending" && request.status !== "resubmitted") {
      throw new Error("Only pending or resubmitted requests can be approved");
    }

    // Check if user can access this request (accounts role can access all)
    if (!userWithPermissions.roles.some((role) => role.name === "accounts")) {
      const shop = await ctx.db.get(request.shopId);
      if (!shop) {
        throw new Error("Shop not found");
      }
      
      // Check if user can access the shop's area
      if (!userWithPermissions.areaIds.includes(shop.areaId)) {
        throw new Error("Access denied");
      }
    }

    // Check approval permissions based on payment method and amount
    const settings = await ctx.db.query("settings").collect();
    const mobileMoneyThreshold = settings.find(s => s.key === "mobile_money_approval_threshold")?.value || 50000;

    if (request.paymentMethod === "mobile_money" && request.amount <= mobileMoneyThreshold) {
      // Can be approved by shop support or accounts
      if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_APPROVE_MOBILE) &&
          !userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_APPROVE_ALL)) {
        throw new Error("Insufficient permissions to approve this request");
      }
    } else {
      // Requires accounts approval
      if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_APPROVE_ALL)) {
        throw new Error("Only accounts team can approve this request");
      }
    }

    const now = Date.now();
    await ctx.db.patch(args.requestId, {
      status: "approved",
      approvedBy: userWithPermissions.user._id,
      approvedAt: now,
      notes: args.notes ? `${request.notes || ""}\n\nApproval notes: ${args.notes}`.trim() : request.notes,
      updatedAt: now,
    });

    // Record status change
    await recordStatusChange(ctx, {
      requestId: args.requestId,
      fromStatus: request.status,
      toStatus: "approved",
      changedBy: userWithPermissions.user._id,
      reason: args.notes,
      metadata: { approvalNotes: args.notes }
    });

    // Create notification and send emails
    await createRequestNotification(ctx, args.requestId, "request_approved");
    
    // Schedule email notification (will be sent asynchronously)
    await ctx.scheduler.runAfter(0, api.emailNotificationService.sendComprehensiveNotification, {
      requestId: args.requestId,
      statusType: "request_approved"
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "request_approved",
      entityType: "request",
      entityId: args.requestId,
      userId: userWithPermissions.user._id,
      newValues: { status: "approved", approvedBy: userWithPermissions.user._id },
      timestamp: now,
    });

    return args.requestId;
  },
});

// Reject request
export const rejectRequest = mutation({
  args: {
    requestId: v.id("requests"),
    reason: v.string(),
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    // Check if user has rejection permissions
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_REJECT)) {
      throw new Error("You don't have permission to reject requests");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    if (request.status !== "pending" && request.status !== "resubmitted") {
      throw new Error("Only pending or resubmitted requests can be rejected");
    }

    // Check if user can access this request (accounts role can access all)
    if (!userWithPermissions.roles.some((role) => role.name === "accounts")) {
      const shop = await ctx.db.get(request.shopId);
      if (!shop) {
        throw new Error("Shop not found");
      }
      
      // Check if user can access the shop's area
      if (!userWithPermissions.areaIds.includes(shop.areaId)) {
        throw new Error("Access denied");
      }
    }

    const now = Date.now();
    await ctx.db.patch(args.requestId, {
      status: "rejected",
      rejectedBy: userWithPermissions.user._id,
      rejectedAt: now,
      rejectionReason: args.reason,
      updatedAt: now,
    });

    // Record status change
    await recordStatusChange(ctx, {
      requestId: args.requestId,
      fromStatus: request.status,
      toStatus: "rejected",
      changedBy: userWithPermissions.user._id,
      reason: args.reason,
      metadata: { rejectionReason: args.reason }
    });

    // Create notification and send emails
    await createRequestNotification(ctx, args.requestId, "request_rejected");
    
    // Schedule email notification (will be sent asynchronously)
    await ctx.scheduler.runAfter(0, api.emailNotificationService.sendComprehensiveNotification, {
      requestId: args.requestId,
      statusType: "request_rejected"
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "request_rejected",
      entityType: "request",
      entityId: args.requestId,
      userId: userWithPermissions.user._id,
      newValues: { status: "rejected", rejectedBy: userWithPermissions.user._id, rejectionReason: args.reason },
      timestamp: now,
    });

    return args.requestId;
  },
});

// Resubmit a rejected request
export const resubmitRequest = mutation({
  args: {
    requestId: v.id("requests"),
    title: v.string(),
    ticketId: v.string(),
    productId: v.string(),
    areaId: v.id("areas"),
    shopId: v.id("shops"),
    shopName: v.string(),
    amount: v.number(),
    paymentMethod: v.union(v.literal("mobile_money"), v.literal("bank_transfer")),
    customerName: v.string(),
    customerContact: v.string(),
    networkProvider: v.optional(v.string()), // Now stores network name instead of ID
    bankName: v.optional(v.string()),
    accountNumber: v.optional(v.string()),
    accountHolderName: v.optional(v.string()),
    comments: v.optional(v.string()),
    ticketImage: v.optional(v.string()),
    changeNotes: v.string(),
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log('[resubmitRequest] Resubmitting request:', args.requestId, {
      paymentMethod: args.paymentMethod,
      networkProvider: args.networkProvider,
      changeNotes: args.changeNotes
    });

    const userWithPermissions = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await getCurrentUserWithPermissions(ctx);

    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    // Check if user has permission to resubmit requests
    if (!userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_RESUBMIT)) {
      throw new Error("You don't have permission to resubmit requests");
    }

    // Get the existing request
    const existingRequest = await ctx.db.get(args.requestId);
    if (!existingRequest) {
      throw new Error("Request not found");
    }

    // Check if request can be resubmitted
    if (existingRequest.status !== "rejected") {
      throw new Error("Only rejected requests can be resubmitted");
    }

    // Check if user owns the request or has permission to edit others' requests
    const isOwner = existingRequest.requestedBy === userWithPermissions.user._id ||
                   (existingRequest.workosUserId && existingRequest.workosUserId === userWithPermissions.user.workosUserId);
    
    const canEdit = isOwner || userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_EDIT_ALL);
    
    if (!canEdit) {
      throw new Error("You can only resubmit your own requests");
    }

    // Validate network provider if mobile money is selected
    if (args.paymentMethod === "mobile_money") {
      if (!args.networkProvider) {
        throw new Error("Network provider is required for mobile money payments");
      }
      
      // Validate that the network provider is one of the allowed values
      const validNetworks = ['MTN', 'Telecel', 'AT'];
      if (!validNetworks.includes(args.networkProvider)) {
        throw new Error(`Invalid network provider. Must be one of: ${validNetworks.join(', ')}`);
      }
      
      console.log('[resubmitRequest] Validated network provider:', args.networkProvider);
    }

    // Validate bank details if bank transfer is selected
    if (args.paymentMethod === "bank_transfer") {
      if (!args.bankName || !args.accountNumber || !args.accountHolderName) {
        throw new Error("Bank name, account number, and account holder name are required for bank transfers");
      }
    }

    // Check for duplicate ticket ID (excluding current request)
    const duplicateRequest = await ctx.db
      .query("requests")
      .withIndex("by_ticket_number", (q) => q.eq("ticketNumber", args.ticketId))
      .filter((q) => q.neq(q.field("_id"), args.requestId))
      .first();

    if (duplicateRequest) {
      throw new Error(`A request with ticket ID "${args.ticketId}" already exists`);
    }

    // Verify shop exists and is active
    const shop = await ctx.db.get(args.shopId);
    if (!shop || !shop.isActive) {
      throw new Error("Selected shop is not valid or inactive");
    }

    // Verify area exists and is active
    const area = await ctx.db.get(args.areaId);
    if (!area || !area.isActive) {
      throw new Error("Selected area is not valid or inactive");
    }

    // Verify shop belongs to the selected area
    if (shop.areaId !== args.areaId) {
      throw new Error("Selected shop does not belong to the chosen area");
    }

    const now = Date.now();
    // Update the request
    await ctx.db.patch(args.requestId, {
      ticketNumber: args.ticketId,
      amount: args.amount,
      paymentMethod: args.paymentMethod,
      customerName: args.customerName,
      customerPhone: args.customerContact,
      mobileMoneyNumber: args.paymentMethod === "mobile_money" ? args.customerContact : undefined,
      mobileMoneyProvider: args.networkProvider,
      bankName: args.bankName,
      accountNumber: args.accountNumber,
      accountName: args.accountHolderName,
      shopId: args.shopId,
      areaId: args.areaId,
      notes: args.comments,
      ticketImageId: args.ticketImage && !args.ticketImage.startsWith("data:") ? args.ticketImage as any : undefined,
      status: "resubmitted",
      rejectionReason: undefined,
      rejectedBy: undefined,
      rejectedAt: undefined,
      updatedAt: now,
    });

    // Record status change
    await recordStatusChange(ctx, {
      requestId: args.requestId,
      fromStatus: existingRequest.status,
      toStatus: "resubmitted",
      changedBy: userWithPermissions.user._id,
      reason: args.changeNotes,
      metadata: {
        changeNotes: args.changeNotes,
        previousRejectionReason: existingRequest.rejectionReason
      }
    });

    console.log('[resubmitRequest] Successfully resubmitted request:', args.requestId);

    // Schedule email notification (will be sent asynchronously)
    await ctx.scheduler.runAfter(0, api.emailNotificationService.sendComprehensiveNotification, {
      requestId: args.requestId,
      statusType: "request_resubmitted"
    });

    // Create audit log
    await ctx.db.insert("audit_logs", {
      action: "request_resubmitted",
      entityType: "request",
      entityId: args.requestId,
      userId: userWithPermissions.user._id,
      metadata: {
        ticketId: args.ticketId,
        amount: args.amount,
        paymentMethod: args.paymentMethod,
        networkProvider: args.networkProvider,
        changeNotes: args.changeNotes,
      },
      timestamp: Date.now(),
    });

    return args.requestId;
  },
});

// Mark request as paid
export const markAsPaid = mutation({
  args: {
    requestId: v.id("requests"),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requirePermission(ctx, PERMISSIONS.REQUEST_APPROVE_ALL);

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    if (request.status !== "approved") {
      throw new Error("Only approved requests can be marked as paid");
    }

    const now = Date.now();
    await ctx.db.patch(args.requestId, {
      status: "paid",
      notes: args.notes ? `${request.notes || ""}\n\nPayment notes: ${args.notes}`.trim() : request.notes,
      updatedAt: now,
    });

    // Record status change
    await recordStatusChange(ctx, {
      requestId: args.requestId,
      fromStatus: request.status,
      toStatus: "paid",
      changedBy: userWithPermissions.user._id,
      reason: args.notes,
      metadata: { paymentNotes: args.notes }
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "request_paid",
      entityType: "request",
      entityId: args.requestId,
      userId: userWithPermissions.user._id,
      newValues: { status: "paid" },
      timestamp: now,
    });

    return args.requestId;
  },
});

// Cancel request
export const cancelRequest = mutation({
  args: {
    requestId: v.id("requests"),
    reason: v.string(),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      throw new Error("Request not found");
    }

    if (request.status !== "pending") {
      throw new Error("Only pending requests can be cancelled");
    }

    // Only the requester or accounts team can cancel
    const canCancel = 
      request.requestedBy === userWithPermissions.user._id ||
      userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_APPROVE_ALL);

    if (!canCancel) {
      throw new Error("Only the requester or accounts team can cancel requests");
    }

    const now = Date.now();
    await ctx.db.patch(args.requestId, {
      status: "cancelled",
      notes: `${request.notes || ""}\n\nCancellation reason: ${args.reason}`.trim(),
      updatedAt: now,
    });

    // Record status change
    await recordStatusChange(ctx, {
      requestId: args.requestId,
      fromStatus: request.status,
      toStatus: "cancelled",
      changedBy: userWithPermissions.user._id,
      reason: args.reason,
      metadata: { cancellationReason: args.reason }
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "request_cancelled",
      entityType: "request",
      entityId: args.requestId,
      userId: userWithPermissions.user._id,
      newValues: { status: "cancelled", cancellationReason: args.reason },
      timestamp: now,
    });

    return args.requestId;
  },
});

// Get request statistics
export const getRequestStats = query({
  args: {
    areaId: v.optional(v.id("areas")),
    shopId: v.optional(v.id("shops")),
    startDate: v.optional(v.number()),
    endDate: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAnyPermission(ctx, [
      PERMISSIONS.REPORTS_VIEW_OWN,
      PERMISSIONS.REPORTS_VIEW_SHOP,
      PERMISSIONS.REPORTS_VIEW_AREA,
      PERMISSIONS.REPORTS_VIEW_ALL,
    ]);

    let query = ctx.db.query("requests");

    // Apply role-based filtering
    if (userWithPermissions.permissions.includes(PERMISSIONS.REPORTS_VIEW_ALL)) {
      // Accounts role - can see all stats
    } else if (userWithPermissions.permissions.includes(PERMISSIONS.REPORTS_VIEW_AREA)) {
      const accessibleAreaIds = await getAccessibleAreaIds(ctx);
      if (accessibleAreaIds.length === 0) {
        return { total: 0, pending: 0, approved: 0, rejected: 0, paid: 0, cancelled: 0, totalAmount: 0 };
      }
      query = query.filter((q) => 
        q.or(...accessibleAreaIds.map(areaId => q.eq(q.field("areaId"), areaId)))
      );
    } else if (userWithPermissions.permissions.includes(PERMISSIONS.REPORTS_VIEW_SHOP)) {
      const accessibleShopIds = await getAccessibleShopIds(ctx);
      if (accessibleShopIds.length === 0) {
        return { total: 0, pending: 0, approved: 0, rejected: 0, paid: 0, cancelled: 0, totalAmount: 0 };
      }
      query = query.filter((q) => 
        q.or(...accessibleShopIds.map(shopId => q.eq(q.field("shopId"), shopId)))
      );
    } else {
      query = query.filter((q) => q.eq(q.field("requestedBy"), userWithPermissions.user._id));
    }

    // Apply additional filters
    if (args.areaId) {
      query = query.filter((q) => q.eq(q.field("areaId"), args.areaId));
    }
    if (args.shopId) {
      query = query.filter((q) => q.eq(q.field("shopId"), args.shopId));
    }
    if (args.startDate) {
      query = query.filter((q) => q.gte(q.field("createdAt"), args.startDate));
    }
    if (args.endDate) {
      query = query.filter((q) => q.lte(q.field("createdAt"), args.endDate));
    }

    const requests = await query.collect();

    const stats = requests.reduce(
      (acc, request) => {
        acc.total++;
        acc[request.status]++;
        acc.totalAmount += request.amount;
        return acc;
      },
      { total: 0, pending: 0, approved: 0, rejected: 0, paid: 0, cancelled: 0, totalAmount: 0 }
    );

    return stats;
  },
});

// Get single request with complete status history
export const getRequestWithHistory = query({
  args: { 
    requestId: v.id("requests"),
    workosUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Use getCurrentUserWithPermissions with workosUserId for WorkOS AuthKit compatibility
    const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
    if (!userWithPermissions) {
      throw new Error("Authentication required");
    }

    // Check if user has any of the required permissions
    const hasRequiredPermission = [
      PERMISSIONS.REQUEST_VIEW_OWN,
      PERMISSIONS.REQUEST_VIEW_SHOP,
      PERMISSIONS.REQUEST_VIEW_AREA,
      PERMISSIONS.REQUEST_VIEW_ALL,
    ].some(permission => userWithPermissions.permissions.includes(permission));

    if (!hasRequiredPermission) {
      throw new Error("Permission denied");
    }

    const request = await ctx.db.get(args.requestId);
    if (!request) {
      return null;
    }

    // Check access permissions
    const canAccess = 
      userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_ALL) ||
      (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_AREA) && 
       await canAccessShop(ctx, request.shopId)) ||
      (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_SHOP) && 
       await canAccessShop(ctx, request.shopId)) ||
      (userWithPermissions.permissions.includes(PERMISSIONS.REQUEST_VIEW_OWN) && 
      request.requestedBy === userWithPermissions.user._id);

    if (!canAccess) {
      throw new Error("Access denied");
    }

    // Get status history
    const statusHistory = await ctx.db
      .query("request_status_history")
      .withIndex("by_request_date", (q) => q.eq("requestId", args.requestId))
      .order("asc")
      .collect();

    // Enrich status history with user information
    const enrichedStatusHistory = await Promise.all(
      statusHistory.map(async (entry) => {
        const user = await ctx.db.get(entry.changedBy);
        return {
          ...entry,
          changedByUser: user ? {
            _id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          } : null,
        };
      })
    );

    // Enrich request with related data
    const [shop, area, requestedBy, approvedBy, rejectedBy] = await Promise.all([
      ctx.db.get(request.shopId),
      ctx.db.get(request.areaId),
      ctx.db.get(request.requestedBy),
      request.approvedBy ? ctx.db.get(request.approvedBy) : null,
      request.rejectedBy ? ctx.db.get(request.rejectedBy) : null,
    ]);

    return {
      ...request,
      shop,
      area,
      requestedBy,
      approvedBy,
      rejectedBy,
      statusHistory: enrichedStatusHistory,
    };
  },
});

// Helper function to create notifications
async function createRequestNotification(
  ctx: any,
  requestId: Id<"requests">,
  type: "request_submitted" | "request_approved" | "request_rejected" | "request_resubmitted"
) {
  const request = await ctx.db.get(requestId);
  if (!request) return;

  const shop = await ctx.db.get(request.shopId);
  const area = await ctx.db.get(request.areaId);

  let title: string;
  let message: string;
  let targetUsers: Id<"users">[] = [];

  switch (type) {
    case "request_submitted":
      title = "New Request Submitted";
      message = `Request ${request.ticketNumber} for ${request.amount} has been submitted from ${shop?.name}`;
      // Notify approvers in the area
      const areaUsers = await ctx.db
        .query("user_area_assignments")
        .withIndex("by_area_active", (q) => 
          q.eq("areaId", request.areaId).eq("isActive", true)
        )
        .collect();
      targetUsers = areaUsers.map(ua => ua.userId);
      break;

    case "request_approved":
      title = "Request Approved";
      message = `Request ${request.ticketNumber} has been approved`;
      targetUsers = [request.requestedBy];
      break;

    case "request_rejected":
      title = "Request Rejected";
      message = `Request ${request.ticketNumber} has been rejected`;
      targetUsers = [request.requestedBy];
      break;

    case "request_resubmitted":
      title = "Request Resubmitted";
      message = `Request ${request.ticketNumber} has been resubmitted from ${shop?.name}`;
      // Notify approvers in the area
      const areaUsersResubmit = await ctx.db
        .query("user_area_assignments")
        .withIndex("by_area_active", (q) => 
          q.eq("areaId", request.areaId).eq("isActive", true)
        )
        .collect();
      targetUsers = areaUsersResubmit.map(ua => ua.userId);
      break;
  }

  // Create notifications for target users
  const now = Date.now();
  for (const userId of targetUsers) {
    await ctx.db.insert("notifications", {
      type,
      title,
      message,
      userId,
      relatedEntityType: "request",
      relatedEntityId: requestId,
      isRead: false,
      createdAt: now,
    });
  }
}
