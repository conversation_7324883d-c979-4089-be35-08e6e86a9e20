/**
 * Complete CRUD test for areas
 */

import { authenticatedConvex } from './convex-auth';

export const testCompleteAreaCRUD = async () => {
  try {
    console.log('🔍 Testing complete area CRUD operations...');
    
    const testName = `Test Area ${Date.now()}`;
    const results = [];
    
    // Test 1: Create Area
    console.log('\n📋 Step 1: Creating area...');
    const areaId = await authenticatedConvex.createArea(testName, 'Test area description');
    console.log('✅ Area created:', areaId);
    results.push({ operation: 'create', success: true, areaId });
    
    // Test 2: Get Areas (should include new area)
    console.log('\n📋 Step 2: Fetching all areas...');
    const areas = await authenticatedConvex.getAreas();
    const createdArea = areas.find(a => a._id === areaId);
    console.log(`✅ Found ${areas.length} areas, including our new area:`, !!createdArea);
    results.push({ operation: 'list', success: !!createdArea, totalAreas: areas.length });
    
    // Test 3: Get Single Area
    console.log('\n📋 Step 3: Fetching single area...');
    const singleArea = await authenticatedConvex.getArea(areaId);
    console.log('✅ Single area fetched:', singleArea?.name);
    results.push({ operation: 'read', success: !!singleArea, areaName: singleArea?.name });
    
    // Test 4: Update Area
    console.log('\n📋 Step 4: Updating area...');
    const updatedName = `${testName} Updated`;
    await authenticatedConvex.updateArea(areaId, updatedName, 'Updated description');
    
    // Verify update
    const updatedArea = await authenticatedConvex.getArea(areaId);
    const updateSuccess = updatedArea?.name === updatedName;
    console.log('✅ Area updated:', updateSuccess, updatedArea?.name);
    results.push({ operation: 'update', success: updateSuccess, newName: updatedArea?.name });
    
    // Test 5: Delete Area
    console.log('\n📋 Step 5: Deleting area...');
    await authenticatedConvex.deleteArea(areaId);
    
    // Verify deletion (should not appear in active areas list)
    const areasAfterDelete = await authenticatedConvex.getAreas();
    const deletedAreaStillVisible = areasAfterDelete.find(a => a._id === areaId);
    const deleteSuccess = !deletedAreaStillVisible;
    console.log('✅ Area deleted (not in active list):', deleteSuccess);
    results.push({ operation: 'delete', success: deleteSuccess, stillVisible: !!deletedAreaStillVisible });
    
    // Summary
    const successCount = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n=== Test Summary ===`);
    console.log(`✅ ${successCount}/${totalTests} operations successful`);
    
    return {
      success: successCount === totalTests,
      totalTests,
      successCount,
      results,
      message: successCount === totalTests ? 
        '🎉 All CRUD operations working perfectly!' : 
        `⚠️ ${totalTests - successCount} operation(s) failed`
    };
    
  } catch (error) {
    console.error('❌ CRUD test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'CRUD test failed with error'
    };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testCompleteAreaCRUD = testCompleteAreaCRUD;
}