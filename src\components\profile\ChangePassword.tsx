import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { toast } from '@/components/ui/use-toast';
import { Eye, EyeOff, AlertCircle } from 'lucide-react';
import { useAuth } from '@/lib/auth-context';
import { useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

const ChangePassword: React.FC = () => {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const changePasswordMutation = useMutation(api.users.changePassword);

  const handleRequestPasswordReset = async () => {
    if (!user?.email) {
      toast({
        title: "Error",
        description: "No email address found for your account.",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      const result = await changePasswordMutation({
        workosUserId: user.id,
      });

      setEmailSent(true);
      toast({
        title: "Password Reset Email Sent",
        description: `We've sent a secure password reset link to ${user.email}`,
      });
    } catch (error) {
      console.error("Password reset request error:", error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to send password reset email. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };
  
  if (emailSent) {
    return (
      <div className="space-y-6 max-w-md">
        {/* Success state */}
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-green-800">Email sent to {user?.email}</h3>
              <p className="text-sm text-green-700">Check your inbox and click the reset link.</p>
            </div>
          </div>
        </div>

        {/* Resend option */}
        <div className="text-center">
          <Button
            variant="outline"
            onClick={() => {
              setEmailSent(false);
              handleRequestPasswordReset();
            }}
            disabled={isLoading}
            className="text-sm"
          >
            Resend Email
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 max-w-md">
      {/* Current user info */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="text-sm">
          <p className="font-medium text-gray-900 mb-1">Account</p>
          <p className="text-gray-600">{user?.email}</p>
        </div>
      </div>

      {/* Password change action */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Change Password</h3>
          <p className="text-sm text-gray-600 mb-4">
            We'll send a secure reset link to your email address.
          </p>
        </div>

        <Button
          onClick={handleRequestPasswordReset}
          className="w-full sm:w-auto"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
              Sending...
            </>
          ) : (
            "Send Reset Link"
          )}
        </Button>
      </div>
    </div>
  );
};

export default ChangePassword;