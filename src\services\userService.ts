import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { useQuery, useMutation } from 'convex/react';
import { convex } from '@/lib/convex';
import { User, UserRole } from '@/lib/types';

/**
 * Fetch all users/profiles
 */
export async function getUsers(): Promise<User[]> {
  try {
    const users = await convex.query(api.users.getUsers, {});
    return users.map(mapConvexUserToUser);
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
}

/**
 * Fetch a user by ID
 */
export async function getUserById(id: string): Promise<User | null> {
  try {
    const user = await convex.query(api.users.getUser, { 
      userId: id as Id<"users"> 
    });
    
    if (!user) {
      return null;
    }
    
    return mapConvexUserToUser(user);
  } catch (error) {
    console.error('Error fetching user by ID:', error);
    throw error;
  }
}

/**
 * Fetch the current user's profile
 */
export async function getCurrentUser(): Promise<User | null> {
  try {
    const currentUser = await convex.query(api.auth.getCurrentUser, {});
    
    if (!currentUser) {
      return null;
    }
    
    // Get user with roles and areas
    const userWithRoles = await convex.query(api.auth.getUserWithRoles, { 
      userId: currentUser._id 
    });
    
    if (!userWithRoles) {
      return null;
    }
    
    return mapConvexUserToUser(userWithRoles);
  } catch (error) {
    console.error('Error fetching current user:', error);
    return null;
  }
}

/**
 * Update a user's profile
 */
export async function updateUser(id: string, updates: Partial<User>): Promise<User> {
  try {
    await convex.mutation(api.users.updateUser, {
      userId: id as Id<"users">,
      firstName: updates.name?.split(' ')[0],
      lastName: updates.name?.split(' ').slice(1).join(' '),
      isActive: updates.role !== undefined, // Simplified mapping
    });

    const updatedUser = await convex.query(api.users.getUser, { 
      userId: id as Id<"users"> 
    });
    
    if (!updatedUser) {
      throw new Error('Failed to retrieve updated user');
    }
    
    return mapConvexUserToUser(updatedUser);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
}

/**
 * Delete a user
 */
export async function deleteUser(id: string): Promise<void> {
  try {
    await convex.mutation(api.users.updateUser, {
      userId: id as Id<"users">,
      isActive: false,
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
}

/**
 * Invite a new user using WorkOS invitation system
 */
export async function inviteUser(userData: {
  name: string;
  email: string;
  role: UserRole;
  shop?: string;
  areas?: string[];
}): Promise<void> {
  try {
    // Get role ID
    const roles = await convex.query(api.users.getRoles, {});
    const role = roles.find(r => r.name === userData.role);
    
    if (!role) {
      throw new Error(`Role ${userData.role} not found`);
    }

    // Get area IDs if specified
    let areaIds: Id<"areas">[] | undefined;
    if (userData.areas && userData.areas.length > 0) {
      const areas = await convex.query(api.users.getAreas, {});
      areaIds = userData.areas
        .map(areaName => areas.find(a => a.name === areaName)?._id)
        .filter(Boolean) as Id<"areas">[];
    }

    const result = await convex.mutation(api.users.inviteUser, {
      email: userData.email,
      roleId: role._id,
      areaIds,
    });

    // The invitation system now automatically handles WorkOS integration
    console.log('User invited successfully. Invitation token:', result.token);
    
    // Show success message
    alert(`User invitation sent successfully!\n\nEmail: ${userData.email}\n\nThe user will receive an invitation email and will be automatically assigned the correct role when they sign up.`);
  } catch (error) {
    console.error('Error inviting user:', error);
    throw error;
  }
}

/**
 * Assign role to user
 */
export async function assignRole(userId: string, role: UserRole): Promise<void> {
  try {
    // Get role ID
    const roles = await convex.query(api.users.getRoles, {});
    const roleObj = roles.find(r => r.name === role);
    
    if (!roleObj) {
      throw new Error(`Role ${role} not found`);
    }

    await convex.mutation(api.users.assignRole, {
      userId: userId as Id<"users">,
      roleId: roleObj._id,
    });
  } catch (error) {
    console.error('Error assigning role:', error);
    throw error;
  }
}

/**
 * Remove role from user
 */
export async function removeRole(userId: string, role: UserRole): Promise<void> {
  try {
    // Get role ID
    const roles = await convex.query(api.users.getRoles, {});
    const roleObj = roles.find(r => r.name === role);
    
    if (!roleObj) {
      throw new Error(`Role ${role} not found`);
    }

    await convex.mutation(api.users.removeRole, {
      userId: userId as Id<"users">,
      roleId: roleObj._id,
    });
  } catch (error) {
    console.error('Error removing role:', error);
    throw error;
  }
}

/**
 * Assign area to user
 */
export async function assignArea(userId: string, areaId: string): Promise<void> {
  try {
    await convex.mutation(api.users.assignArea, {
      userId: userId as Id<"users">,
      areaId: areaId as Id<"areas">,
    });
  } catch (error) {
    console.error('Error assigning area:', error);
    throw error;
  }
}

/**
 * Remove area from user
 */
export async function removeArea(userId: string, areaId: string): Promise<void> {
  try {
    await convex.mutation(api.users.removeArea, {
      userId: userId as Id<"users">,
      areaId: areaId as Id<"areas">,
    });
  } catch (error) {
    console.error('Error removing area:', error);
    throw error;
  }
}

/**
 * Helper function to map a Convex user to a User object
 */
function mapConvexUserToUser(convexUser: any): User {
  const fullName = [convexUser.firstName, convexUser.lastName]
    .filter(Boolean)
    .join(' ') || convexUser.email;

  return {
    id: convexUser._id,
    name: fullName,
    email: convexUser.email,
    role: convexUser.roles?.[0]?.name as UserRole || 'watcher',
    shop: undefined, // Not directly mapped in Convex schema
    areas: convexUser.areas?.map((area: any) => area.name) || [],
    avatar: convexUser.profilePicture,
    phone: undefined, // Not in Convex schema
  };
}

/**
 * Check for pending invites and create profiles if needed
 * This is handled automatically by the Convex auth system
 */
export async function checkPendingInvites(): Promise<void> {
  // This is now handled automatically by the WorkOS + Convex integration
  // in the auth context when users sign in
  console.log('Pending invites are handled automatically by WorkOS + Convex integration');
}

/**
 * Refresh the user's JWT claims
 * Not needed with Convex as it handles this automatically
 */
export async function refreshJwtClaims(): Promise<void> {
  console.log('JWT claims refresh is handled automatically by Convex');
}

// React hooks for components
export const useUsers = () => {
  return useQuery(api.users.getUsers, {});
};

export const useUser = (userId: string) => {
  return useQuery(api.users.getUser, { 
    userId: userId as Id<"users"> 
  });
};

export const useCurrentUser = () => {
  return useQuery(api.auth.getCurrentUser, {});
};

export const useSessionInfo = () => {
  return useQuery(api.auth.getSessionInfo, {});
};

export const useUpdateUser = () => {
  return useMutation(api.users.updateUser);
};

export const useInviteUser = () => {
  return useMutation(api.users.inviteUser);
};

export const useAssignRole = () => {
  return useMutation(api.users.assignRole);
};

export const useRemoveRole = () => {
  return useMutation(api.users.removeRole);
};

export const useAssignArea = () => {
  return useMutation(api.users.assignArea);
};

export const useRemoveArea = () => {
  return useMutation(api.users.removeArea);
};
