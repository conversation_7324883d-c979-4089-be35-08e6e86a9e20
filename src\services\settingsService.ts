import { mockSettings } from '@/lib/mockData';
import { getSettings as getMockSettings, updateSettings as updateMockSettings } from '@/services/mockServices';

/**
 * Settings interface
 */
export interface Settings {
  id: number;
  momoThreshold: number;
  bankThreshold: number;
  whitelistedDomains: string[];
  updatedBy?: string;
  updatedAt?: Date;
}

/**
 * Fetch application settings
 */
export async function getSettings(): Promise<Settings> {
  // Use mock settings service
  return await getMockSettings();
}

/**
 * Update application settings
 */
export async function updateSettings(updates: {
  momoThreshold?: number;
  bankThreshold?: number;
  whitelistedDomains?: string[];
}): Promise<Settings> {
  // Use mock settings service
  return await updateMockSettings(updates);
}
