import { query } from "./_generated/server";
import { v } from "convex/values";

/**
 * Debug authentication and permissions
 */

// Check current authentication state
export const getCurrentAuthState = query({
  args: {},
  handler: async (ctx) => {
    try {
      // Get the current user from auth
      const identity = await ctx.auth.getUserIdentity();
      
      if (!identity) {
        return {
          authenticated: false,
          message: "No identity found"
        };
      }
      
      // Find user in database
      const user = await ctx.db
        .query("users")
        .withIndex("by_workos_id", (q) => q.eq("workosId", identity.subject))
        .unique();
      
      if (!user) {
        return {
          authenticated: false,
          identity: {
            subject: identity.subject,
            email: identity.email,
            name: identity.name
          },
          message: "Identity found but user not in database"
        };
      }
      
      // Get user roles
      const userRoles = await ctx.db
        .query("user_roles")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", user._id).eq("isActive", true)
        )
        .collect();
      
      const roleDetails = [];
      for (const userRole of userRoles) {
        const role = await ctx.db.get(userRole.roleId);
        if (role) {
          roleDetails.push({
            roleId: role._id,
            roleName: role.name,
            permissions: role.permissions
          });
        }
      }
      
      return {
        authenticated: true,
        identity: {
          subject: identity.subject,
          email: identity.email,
          name: identity.name
        },
        user: {
          id: user._id,
          email: user.email,
          workosId: user.workosId,
          firstName: user.firstName,
          lastName: user.lastName,
          isActive: user.isActive
        },
        roles: roleDetails,
        totalRoles: roleDetails.length
      };
      
    } catch (error) {
      return {
        authenticated: false,
        error: error.message,
        message: "Error checking authentication"
      };
    }
  },
});

// Test permission check
export const testPermissionCheck = query({
  args: { permission: v.string() },
  handler: async (ctx, args) => {
    try {
      // Get the current user from auth
      const identity = await ctx.auth.getUserIdentity();
      
      if (!identity) {
        return {
          hasPermission: false,
          reason: "No identity found"
        };
      }
      
      // Find user in database
      const user = await ctx.db
        .query("users")
        .withIndex("by_workos_id", (q) => q.eq("workosId", identity.subject))
        .unique();
      
      if (!user) {
        return {
          hasPermission: false,
          reason: "User not found in database",
          identity: identity.subject
        };
      }
      
      if (!user.isActive) {
        return {
          hasPermission: false,
          reason: "User is not active"
        };
      }
      
      // Get user roles and check permissions
      const userRoles = await ctx.db
        .query("user_roles")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", user._id).eq("isActive", true)
        )
        .collect();
      
      if (userRoles.length === 0) {
        return {
          hasPermission: false,
          reason: "User has no active roles"
        };
      }
      
      // Check if any role has the required permission
      for (const userRole of userRoles) {
        const role = await ctx.db.get(userRole.roleId);
        if (role && role.permissions.includes(args.permission)) {
          return {
            hasPermission: true,
            grantedByRole: role.name,
            allRoles: userRoles.length
          };
        }
      }
      
      return {
        hasPermission: false,
        reason: `Permission '${args.permission}' not found in any user roles`,
        userRoles: userRoles.length
      };
      
    } catch (error) {
      return {
        hasPermission: false,
        error: error.message,
        reason: "Error checking permission"
      };
    }
  },
});