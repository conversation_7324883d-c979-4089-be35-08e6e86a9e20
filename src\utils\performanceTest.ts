/**
 * Performance Testing Utilities
 * 
 * Simple utilities to test and verify performance improvements
 */

import { measureAsync } from '@/lib/performance';

// Test data loading performance
export async function testDataLoadingPerformance() {
  console.log('🚀 Starting Performance Tests...');
  
  const results = {
    mockApiCalls: [] as Array<{ operation: string; duration: number }>,
    componentRenders: [] as Array<{ component: string; duration: number }>,
    bundleSize: 0,
    totalLoadTime: 0
  };

  // Test mock API call speeds
  const testApiCalls = [
    { name: 'getRequests', fn: () => import('@/services/mockServices').then(m => m.getRequests()) },
    { name: 'getStats', fn: () => import('@/services/mockServices').then(m => m.getStats()) },
    { name: 'getAreas', fn: () => import('@/services/mockServices').then(m => m.getAreas()) },
    { name: 'getShops', fn: () => import('@/services/mockServices').then(m => m.getShops()) },
  ];

  for (const test of testApiCalls) {
    const duration = await measureAsync(test.name, test.fn);
    if (duration) {
      results.mockApiCalls.push({ operation: test.name, duration });
    }
  }

  // Test component loading
  const testComponents = [
    { name: 'Dashboard', fn: () => import('@/pages/Dashboard') },
    { name: 'Requests', fn: () => import('@/pages/Requests') },
    { name: 'ActivityTimeline', fn: () => import('@/components/dashboard/ActivityTimeline') },
  ];

  for (const test of testComponents) {
    const duration = await measureAsync(`Load ${test.name}`, test.fn);
    if (duration) {
      results.componentRenders.push({ component: test.name, duration });
    }
  }

  // Calculate total load time
  results.totalLoadTime = results.mockApiCalls.reduce((sum, call) => sum + call.duration, 0);

  return results;
}

// Performance benchmarks (what we consider good performance)
export const PERFORMANCE_BENCHMARKS = {
  // API calls should be under these thresholds (in ms)
  API_CALLS: {
    EXCELLENT: 50,
    GOOD: 100,
    ACCEPTABLE: 200,
    POOR: 500
  },
  
  // Component loading should be under these thresholds (in ms)
  COMPONENT_LOADING: {
    EXCELLENT: 100,
    GOOD: 200,
    ACCEPTABLE: 500,
    POOR: 1000
  },
  
  // Total dashboard load time (in ms)
  DASHBOARD_LOAD: {
    EXCELLENT: 200,
    GOOD: 500,
    ACCEPTABLE: 1000,
    POOR: 2000
  }
};

// Evaluate performance results
export function evaluatePerformance(results: any) {
  const evaluation = {
    apiCalls: 'unknown' as 'excellent' | 'good' | 'acceptable' | 'poor' | 'unknown',
    componentLoading: 'unknown' as 'excellent' | 'good' | 'acceptable' | 'poor' | 'unknown',
    dashboardLoad: 'unknown' as 'excellent' | 'good' | 'acceptable' | 'poor' | 'unknown',
    overallScore: 0,
    recommendations: [] as string[]
  };

  // Evaluate API calls
  const avgApiTime = results.mockApiCalls.reduce((sum: number, call: any) => sum + call.duration, 0) / results.mockApiCalls.length;
  
  if (avgApiTime <= PERFORMANCE_BENCHMARKS.API_CALLS.EXCELLENT) {
    evaluation.apiCalls = 'excellent';
  } else if (avgApiTime <= PERFORMANCE_BENCHMARKS.API_CALLS.GOOD) {
    evaluation.apiCalls = 'good';
  } else if (avgApiTime <= PERFORMANCE_BENCHMARKS.API_CALLS.ACCEPTABLE) {
    evaluation.apiCalls = 'acceptable';
  } else {
    evaluation.apiCalls = 'poor';
    evaluation.recommendations.push('Consider optimizing API response times');
  }

  // Evaluate component loading
  const avgComponentTime = results.componentRenders.reduce((sum: number, comp: any) => sum + comp.duration, 0) / results.componentRenders.length;
  
  if (avgComponentTime <= PERFORMANCE_BENCHMARKS.COMPONENT_LOADING.EXCELLENT) {
    evaluation.componentLoading = 'excellent';
  } else if (avgComponentTime <= PERFORMANCE_BENCHMARKS.COMPONENT_LOADING.GOOD) {
    evaluation.componentLoading = 'good';
  } else if (avgComponentTime <= PERFORMANCE_BENCHMARKS.COMPONENT_LOADING.ACCEPTABLE) {
    evaluation.componentLoading = 'acceptable';
  } else {
    evaluation.componentLoading = 'poor';
    evaluation.recommendations.push('Consider implementing code splitting and lazy loading');
  }

  // Evaluate dashboard load time
  if (results.totalLoadTime <= PERFORMANCE_BENCHMARKS.DASHBOARD_LOAD.EXCELLENT) {
    evaluation.dashboardLoad = 'excellent';
  } else if (results.totalLoadTime <= PERFORMANCE_BENCHMARKS.DASHBOARD_LOAD.GOOD) {
    evaluation.dashboardLoad = 'good';
  } else if (results.totalLoadTime <= PERFORMANCE_BENCHMARKS.DASHBOARD_LOAD.ACCEPTABLE) {
    evaluation.dashboardLoad = 'acceptable';
  } else {
    evaluation.dashboardLoad = 'poor';
    evaluation.recommendations.push('Dashboard loading is slow - consider caching and data optimization');
  }

  // Calculate overall score (0-100)
  const scores = {
    excellent: 100,
    good: 80,
    acceptable: 60,
    poor: 30,
    unknown: 0
  };

  evaluation.overallScore = Math.round(
    (scores[evaluation.apiCalls] + scores[evaluation.componentLoading] + scores[evaluation.dashboardLoad]) / 3
  );

  return evaluation;
}

// Generate performance report
export function generatePerformanceReport(results: any, evaluation: any) {
  const report = `
🚀 PERFORMANCE REPORT
=====================

📊 API Calls Performance: ${evaluation.apiCalls.toUpperCase()}
${results.mockApiCalls.map((call: any) => `  • ${call.operation}: ${call.duration.toFixed(2)}ms`).join('\n')}

🧩 Component Loading: ${evaluation.componentLoading.toUpperCase()}
${results.componentRenders.map((comp: any) => `  • ${comp.component}: ${comp.duration.toFixed(2)}ms`).join('\n')}

⏱️  Dashboard Load Time: ${evaluation.dashboardLoad.toUpperCase()} (${results.totalLoadTime.toFixed(2)}ms)

🎯 Overall Score: ${evaluation.overallScore}/100

${evaluation.recommendations.length > 0 ? `
📝 Recommendations:
${evaluation.recommendations.map((rec: string) => `  • ${rec}`).join('\n')}
` : '✅ No performance issues detected!'}

🎯 Performance Targets:
  • API Calls: < ${PERFORMANCE_BENCHMARKS.API_CALLS.GOOD}ms (current avg: ${(results.mockApiCalls.reduce((sum: number, call: any) => sum + call.duration, 0) / results.mockApiCalls.length).toFixed(2)}ms)
  • Component Loading: < ${PERFORMANCE_BENCHMARKS.COMPONENT_LOADING.GOOD}ms
  • Dashboard Load: < ${PERFORMANCE_BENCHMARKS.DASHBOARD_LOAD.GOOD}ms (current: ${results.totalLoadTime.toFixed(2)}ms)
`;

  return report;
}

// Run complete performance test
export async function runPerformanceTest() {
  try {
    const results = await testDataLoadingPerformance();
    const evaluation = evaluatePerformance(results);
    const report = generatePerformanceReport(results, evaluation);
    
    console.log(report);
    
    return {
      results,
      evaluation,
      report,
      passed: evaluation.overallScore >= 70 // Consider 70+ as passing
    };
  } catch (error) {
    console.error('Performance test failed:', error);
    return {
      results: null,
      evaluation: null,
      report: 'Performance test failed',
      passed: false
    };
  }
}

// Quick performance check for development
export function quickPerformanceCheck() {
  if (process.env.NODE_ENV === 'development') {
    // Run performance test after a short delay to allow app to load
    setTimeout(() => {
      runPerformanceTest().then(result => {
        if (result.passed) {
          console.log('✅ Performance check passed!');
        } else {
          console.warn('⚠️ Performance issues detected. Run full test for details.');
        }
      });
    }, 2000);
  }
}
