# Convex Settings Implementation

This document describes the settings management system implemented for the MyBet Africa Cash Management System.

## Overview

The settings system provides centralized configuration management with the following features:

- **Key-value storage** for application settings
- **Role-based access control** for settings management
- **Default value handling** with automatic fallbacks
- **Validation** for setting values
- **Audit logging** for all setting changes
- **Category organization** for better management

## Files

- `convex/settings.ts` - Main settings functions
- `convex/init.ts` - System initialization functions
- `src/stores/settingsStore.ts` - Frontend integration

## Available Functions

### Queries

#### `getSettings()`
Returns all settings as a key-value object with default values.

```typescript
const settings = await convex.query(api.settings.getSettings, {});
// Returns: { mobile_money_approval_threshold: 5000, ... }
```

#### `getSettingsWithMetadata(category?, isSystem?)`
Returns settings with full metadata for admin interfaces.

```typescript
const settings = await convex.query(api.settings.getSettingsWithMetadata, {
  category: "approval_thresholds"
});
```

#### `getSetting(key)`
Returns a specific setting value by key.

```typescript
const threshold = await convex.query(api.settings.getSetting, {
  key: "mobile_money_approval_threshold"
});
```

#### `getSettingsByCategory(category)`
Returns all settings in a specific category.

```typescript
const securitySettings = await convex.query(api.settings.getSettingsByCategory, {
  category: "security"
});
```

#### `getSettingCategories()`
Returns all available setting categories.

```typescript
const categories = await convex.query(api.settings.getSettingCategories, {});
```

### Mutations

#### `updateSettings(settings)`
Updates multiple settings at once.

```typescript
await convex.mutation(api.settings.updateSettings, {
  settings: {
    mobile_money_approval_threshold: 6000,
    bank_transfer_approval_threshold: 12000,
    whitelisted_domains: ["mybet.africa", "kmkentertainment.com"]
  }
});
```

#### `updateSetting(key, value, description?, category?)`
Updates a single setting.

```typescript
await convex.mutation(api.settings.updateSetting, {
  key: "mobile_money_approval_threshold",
  value: 6000,
  description: "Updated threshold for mobile money"
});
```

#### `initializeDefaultSettings()`
Creates all default system settings if they don't exist.

```typescript
const created = await convex.mutation(api.settings.initializeDefaultSettings, {});
```

#### `resetSettingsToDefault(keys)`
Resets specified settings to their default values.

```typescript
await convex.mutation(api.settings.resetSettingsToDefault, {
  keys: ["mobile_money_approval_threshold", "bank_transfer_approval_threshold"]
});
```

#### `deleteSetting(key)`
Deletes a non-system setting.

```typescript
await convex.mutation(api.settings.deleteSetting, {
  key: "custom_setting"
});
```

## Default Settings

The system includes the following default settings:

### Approval Thresholds
- `mobile_money_approval_threshold`: 5000 (GHS)
- `bank_transfer_approval_threshold`: 10000 (GHS)

### Security
- `whitelisted_domains`: ["kmkentertainment.com", "mybet.africa"]
- `session_timeout_minutes`: 480 (8 hours)
- `auto_logout_warning_minutes`: 15

### File Management
- `max_file_size`: ******** (10MB)
- `max_avatar_size`: 2097152 (2MB)
- `max_ticket_image_size`: 5242880 (5MB)

### Notifications
- `notification_retention_days`: 30

## Frontend Integration

### Using the Settings Store

```typescript
import { useSettingsStore } from '@/stores';

const { settings, fetchSettings, updateSettings } = useSettingsStore();

// Fetch settings
await fetchSettings();

// Update settings
await updateSettings({
  momoThreshold: 6000,
  bankThreshold: 12000
});
```

### Using Convex Hooks

```typescript
import { useSettingsQuery, useUpdateSettingsMutation } from '@/stores/settingsStore';

// Real-time settings
const settings = useSettingsQuery();

// Update mutation
const updateSettings = useUpdateSettingsMutation();
```

## Permissions

Settings management requires the following permissions:

- `SETTINGS_VIEW` - View settings
- `SETTINGS_UPDATE` - Update settings

Only users with the `accounts` role have these permissions by default.

## Validation

Settings values are validated based on their type:

- **Thresholds**: Must be positive numbers ≤ 1,000,000
- **Domains**: Must be valid domain format
- **File sizes**: Must be positive numbers ≤ 100MB
- **Time values**: Must be within reasonable ranges

## Initialization

To initialize the system with default settings:

```typescript
// Initialize all default settings
await convex.mutation(api.init.initializeSystem, {});

// Check initialization status
const status = await convex.query(api.init.checkSystemInitialization, {});
```

## Testing

Run the settings test script:

```bash
cd scripts
npm install
npm run test-settings
```

## Error Handling

All settings functions include proper error handling:

- **Validation errors** for invalid values
- **Permission errors** for unauthorized access
- **Not found errors** for missing settings
- **Constraint errors** for system settings

## Audit Trail

All settings changes are logged in the `audit_logs` table with:

- Action type (`setting_updated`, `setting_deleted`, etc.)
- User who made the change
- Old and new values
- Timestamp

## Categories

Settings are organized into categories:

- `approval_thresholds` - Payment approval limits
- `security` - Security-related settings
- `file_management` - File upload limits
- `notifications` - Notification settings
- `custom` - User-defined settings

## Best Practices

1. **Always use the settings store** for frontend access
2. **Validate settings** before updating
3. **Use categories** to organize related settings
4. **Provide descriptions** for custom settings
5. **Test changes** in development first
6. **Monitor audit logs** for setting changes

## Migration Notes

The settings system replaces the previous hardcoded configuration with:

- Dynamic configuration management
- Real-time updates without deployment
- Proper validation and error handling
- Complete audit trail
- Role-based access control

All existing functionality continues to work with graceful fallbacks to default values.