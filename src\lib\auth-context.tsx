import React, { createContext, useContext, ReactNode, useEffect, useState } from 'react';
import { useQuery, useMutation } from 'convex/react';

import { api } from '../../convex/_generated/api';
import { 
  getSession, 
  signIn as workosSignIn, 
  signUp as workosSignUp, 
  signOut as workosSignOut,
  isAuthenticated as workosIsAuthenticated,
  type WorkOSSession,
  type WorkOSUser as WorkOSUserType
} from '@/lib/workos-auth';
import { toast } from '@/components/ui/use-toast';

// WorkOS AuthKit types
interface WorkOSUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  profilePictureUrl?: string;
}

interface AuthContextType {
  user: WorkOSUser | null;
  session: any | null;
  appUser: any | null;
  userWithRoles: any | null;
  isLoading: boolean;
  signIn: () => Promise<void>;
  signUp: () => Promise<void>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  // WorkOS session state
  const [workosSession, setWorkosSession] = useState<WorkOSSession | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  
  // Convex mutations for syncing user data
  const syncUserMutation = useMutation(api.auth.syncUser);
  
  // Get current user from Convex using WorkOS ID
  const currentUserWithRoles = useQuery(
    api.auth.getCurrentUserWithRoles,
    workosSession?.user?.id ? { workosUserId: workosSession.user.id } : "skip"
  );

  // Extract user data from userWithRoles for appUser
  const currentUser = currentUserWithRoles?.user || null;

  // Debug logging
  console.log('AuthContext Debug:', {
    hasWorkosSession: !!workosSession,
    workosUserId: workosSession?.user?.id,
    workosUserEmail: workosSession?.user?.email,
    currentUserWithRoles: !!currentUserWithRoles,
    currentUser: !!currentUser,
    currentUserData: currentUser,
    isLoading,
    querySkipped: !workosSession?.user?.id
  });

  // Initialize WorkOS session on mount
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        setIsLoading(true);
        const session = await getSession();
        setWorkosSession(session);
        
        // If we have a session, sync the user with Convex
        if (session) {
          console.log('Syncing user with Convex:', session.user);

          const syncData: any = {
            workosId: session.user.id,
            email: session.user.email,
          };

          // Only include optional fields if they have values
          if (session.user.firstName) {
            syncData.firstName = session.user.firstName;
          }
          if (session.user.lastName) {
            syncData.lastName = session.user.lastName;
          }
          if (session.user.profilePictureUrl) {
            syncData.profilePicture = session.user.profilePictureUrl;
          }

          console.log('Sync data:', syncData);
          await syncUserMutation(syncData);
          console.log('User sync completed');
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, [syncUserMutation]);

  // The user object for the context
  const user = workosSession?.user || null;


  const signIn = async () => {
    try {
      console.log("Starting WorkOS AuthKit sign-in...");
      await workosSignIn();
    } catch (error) {
      console.error("Sign-in error:", error);
      toast({
        title: "Authentication Error",
        description: "An error occurred during sign-in. Please try again.",
        variant: "destructive",
      });
    }
  };
    

  const signOut = async () => {
    try {
      console.log("Starting WorkOS AuthKit sign-out...");
      await workosSignOut();
      setWorkosSession(null);
    } catch (error) {
      console.error("Sign-out error:", error);
      toast({
        title: "Sign Out Error",
        description: "An error occurred during sign-out. Please try again.",
        variant: "destructive",
      });
    }
  };

  const signUp = async () => {
    try {
      console.log("Starting WorkOS AuthKit sign-up...");
      await workosSignUp();
    } catch (error) {
      console.error("Sign-up error:", error);
      toast({
        title: "Registration Error",
        description: "Registration is handled by your administrator. Please contact them for an invitation.",
        variant: "destructive",
      });
    }
  };

  const resetPassword = async (email: string) => {
    toast({
      title: "Password Reset",
      description: "Password reset is handled by your identity provider (WorkOS).",
    });
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        session: workosSession,
        appUser: currentUser,
        userWithRoles: currentUserWithRoles,
        isLoading,
        signIn,
        signUp,
        signOut,
        resetPassword,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
