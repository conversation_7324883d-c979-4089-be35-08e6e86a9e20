#!/usr/bin/env node

/**
 * WorkOS Webhook Delivery Testing Script
 * 
 * This script tests webhook delivery and helps diagnose issues
 */

const https = require('https');
const crypto = require('crypto');

// Configuration
const WEBHOOK_URL = 'https://efficient-toucan-547.convex.cloud/http/workos-webhook';
const TEST_SECRET = 'test_secret_for_development';

console.log('🧪 WorkOS Webhook Delivery Testing');
console.log('===================================\n');

// Test payload - simulates a real WorkOS organization_membership.created event
const testPayload = {
  event: 'organization_membership.created',
  data: {
    id: 'om_test_123456789',
    object: 'organization_membership',
    organization_id: 'org_01JM086NJQP34JDKMG0Z3K2ATR',
    organization_name: 'Test Organization',
    user_id: 'user_01JYXT7JSXFEYBE0W9CDYZYPGW',
    status: 'active',
    role: {
      slug: 'accounts'
    },
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString()
  }
};

// Create proper WorkOS signature
function createWorkOSSignature(payload, secret) {
  const timestamp = Date.now();
  const payloadString = JSON.stringify(payload);
  const signatureString = `${timestamp}.${payloadString}`;
  
  const signature = crypto
    .createHmac('sha256', secret)
    .update(signatureString, 'utf8')
    .digest('hex');
  
  return {
    signature: `t=${timestamp},v1=${signature}`,
    payload: payloadString,
    timestamp
  };
}

// Test webhook delivery
async function testWebhookDelivery() {
  console.log('📤 Testing webhook delivery...\n');
  
  const { signature, payload, timestamp } = createWorkOSSignature(testPayload, TEST_SECRET);
  
  console.log('📋 Test Details:');
  console.log(`   Event: ${testPayload.event}`);
  console.log(`   User ID: ${testPayload.data.user_id}`);
  console.log(`   Role: ${testPayload.data.role.slug}`);
  console.log(`   Timestamp: ${timestamp}`);
  console.log(`   Signature: ${signature}`);
  console.log('');
  
  const url = new URL(WEBHOOK_URL);
  const options = {
    hostname: url.hostname,
    port: 443,
    path: url.pathname,
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(payload),
      'WorkOS-Signature': signature,
      'User-Agent': 'WorkOS-Webhook-Test/1.0'
    },
    timeout: 10000
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      let responseBody = '';
      
      res.on('data', (chunk) => {
        responseBody += chunk;
      });
      
      res.on('end', () => {
        console.log('📥 Webhook Response:');
        console.log(`   Status: ${res.statusCode} ${res.statusMessage}`);
        console.log(`   Headers: ${JSON.stringify(res.headers, null, 2)}`);
        console.log(`   Body: ${responseBody}`);
        console.log('');
        
        if (res.statusCode === 200) {
          console.log('✅ Webhook delivery successful!');
          resolve({ success: true, statusCode: res.statusCode, body: responseBody });
        } else {
          console.log(`❌ Webhook delivery failed with status ${res.statusCode}`);
          resolve({ success: false, statusCode: res.statusCode, body: responseBody });
        }
      });
    });

    req.on('error', (error) => {
      console.log(`❌ Webhook delivery error: ${error.message}`);
      reject(error);
    });

    req.on('timeout', () => {
      console.log('❌ Webhook delivery timed out');
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.write(payload);
    req.end();
  });
}

// Test webhook endpoint accessibility
async function testEndpointAccessibility() {
  console.log('🌐 Testing endpoint accessibility...\n');
  
  const url = new URL(WEBHOOK_URL);
  const options = {
    hostname: url.hostname,
    port: 443,
    path: url.pathname,
    method: 'GET',
    timeout: 5000
  };

  return new Promise((resolve, reject) => {
    const req = https.request(options, (res) => {
      console.log('📥 Endpoint Response:');
      console.log(`   Status: ${res.statusCode} ${res.statusMessage}`);
      console.log('');
      
      if (res.statusCode === 405) {
        console.log('✅ Endpoint is accessible (405 Method Not Allowed is expected for GET)');
        resolve(true);
      } else if (res.statusCode === 200) {
        console.log('✅ Endpoint is accessible');
        resolve(true);
      } else {
        console.log(`⚠️  Unexpected status code: ${res.statusCode}`);
        resolve(false);
      }
    });

    req.on('error', (error) => {
      console.log(`❌ Endpoint accessibility test failed: ${error.message}`);
      reject(error);
    });

    req.on('timeout', () => {
      console.log('❌ Endpoint accessibility test timed out');
      req.destroy();
      reject(new Error('Timeout'));
    });

    req.end();
  });
}

// Main test function
async function runTests() {
  try {
    console.log('🚀 Starting webhook delivery tests...\n');
    
    // Test 1: Endpoint accessibility
    await testEndpointAccessibility();
    
    // Test 2: Webhook delivery
    const result = await testWebhookDelivery();
    
    console.log('📊 Test Summary:');
    console.log('================');
    
    if (result.success) {
      console.log('✅ All tests passed!');
      console.log('✅ Webhook endpoint is working correctly');
      console.log('');
      console.log('🎯 Next steps:');
      console.log('1. Configure the webhook in WorkOS Dashboard');
      console.log('2. Set the real webhook secret in Convex');
      console.log('3. Test with real WorkOS events');
    } else {
      console.log('❌ Webhook delivery test failed');
      console.log('');
      console.log('🔧 Troubleshooting:');
      console.log('1. Check Convex deployment status');
      console.log('2. Verify HTTP routes are configured');
      console.log('3. Check Convex logs: npx convex logs');
      console.log('4. Verify webhook secret matches');
    }
    
  } catch (error) {
    console.log(`❌ Test failed with error: ${error.message}`);
    console.log('');
    console.log('🔧 Troubleshooting:');
    console.log('1. Check your internet connection');
    console.log('2. Verify the webhook URL is correct');
    console.log('3. Check Convex deployment status');
  }
}

// Run the tests
runTests();