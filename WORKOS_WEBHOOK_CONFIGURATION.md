# WorkOS Webhook Configuration Guide

## 🎯 **Step-by-Step Configuration**

### **1. Access WorkOS Dashboard**
1. Go to [WorkOS Dashboard](https://dashboard.workos.com/)
2. Navigate to your project
3. Go to **Webhooks** section

### **2. Create New Webhook Endpoint**
Click "Add Endpoint" or "Create Webhook" and configure:

#### **Webhook URL:**
```
https://efficient-toucan-547.convex.cloud/http/workos-webhook
```

#### **Events to Subscribe:**
Select these events for complete synchronization:

**User Events:**
- ✅ `user.created`
- ✅ `user.updated` 
- ✅ `user.deleted`

**Organization Events:**
- ✅ `organization.created`
- ✅ `organization.updated`
- ✅ `organization.deleted`

**Organization Membership Events:**
- ✅ `organization_membership.created`
- ✅ `organization_membership.updated`
- ✅ `organization_membership.deleted`

### **3. Configure Webhook Secret**
1. After creating the webhook, WorkOS will generate a **webhook secret**
2. Copy this secret
3. Add it to your Convex environment:

```bash
npx convex env set WORKOS_WEBHOOK_SECRET "your_webhook_secret_here"
```

### **4. Test the Webhook**
1. Use the "Send test event" button in WorkOS Dashboard
2. Send a `user.created` test event
3. Verify the webhook is received successfully

---

## ✅ **Current Status**

### **What's Working:**
- ✅ Webhook endpoint: `https://efficient-toucan-547.convex.cloud/http/workos-webhook`
- ✅ All event types tested and working
- ✅ Basic signature verification implemented
- ✅ User creation/update/deletion handling
- ✅ Organization event handling
- ✅ Bidirectional sync (Convex ↔ WorkOS)

### **Test Results:**
```
✅ user.created: SUCCESS
✅ user.updated: SUCCESS  
✅ user.deleted: SUCCESS
✅ organization.created: SUCCESS
✅ organization_membership.created: SUCCESS
```

### **Next Steps After Webhook Configuration:**
1. Replace test webhook secret with real WorkOS webhook secret
2. Test with real WorkOS events
3. Monitor sync dashboard for any issues
4. Implement proper HMAC verification (when Convex supports crypto)

---

## 🔧 **Troubleshooting**

### **If Webhook Fails:**
1. Check the webhook URL includes `/http/` prefix
2. Verify webhook secret is correctly set in Convex environment
3. Check Convex logs: `npx convex logs --history 20`
4. Test with our test script: `cd scripts && node test-webhook-with-url.js`

### **Common Issues:**
- ❌ **404 Error**: Missing `/http/` prefix in URL
- ❌ **401 Error**: Incorrect webhook secret
- ❌ **500 Error**: Server error (check Convex logs)

---

## 📊 **Monitoring**

After configuration, monitor:
1. **Convex Logs**: `npx convex logs`
2. **User Count**: `npx convex run testQueries:countUsers '{}'`
3. **Sync Status**: Access Sync Management Dashboard (when UI routing is fixed)
4. **WorkOS Dashboard**: Check webhook delivery status