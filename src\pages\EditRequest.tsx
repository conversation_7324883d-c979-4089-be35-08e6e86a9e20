
import React from 'react';
import { useParams } from 'react-router-dom';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import { useAuth } from '@/lib/auth-context';
import RequestForm from '@/components/requests/RequestForm';
import PageTransition from '@/components/common/PageTransition';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

const EditRequest: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { user } = useRequireAuth(['shop_manager']);
  const { user: workosUser } = useAuth();
  
  // Use WorkOS-compatible public query to get requests
  const convexRequests = useQuery(
    api.publicQueries.getRequestsForUser,
    workosUser?.id ? { workosUserId: workosUser.id } : "skip"
  );
  
  // Find the specific request from the query results
  const request = convexRequests?.find(r => r._id === id);

  if (!request) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Request not found</h1>
            <p className="text-muted-foreground">
              The request you're trying to edit could not be found
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  // Check if current user is the creator of this request
  if (workosUser?.id !== request.requestedBy) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Access denied</h1>
            <p className="text-muted-foreground">
              You can only edit requests that you created
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  // Check if request is still pending
  if (request.status !== 'pending') {
    return (
      <Layout>
        <div className="flex justify-center items-center h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold mb-2">Cannot edit request</h1>
            <p className="text-muted-foreground">
              You can only edit requests that are still pending
            </p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageTransition>
        <RequestForm existingRequest={request} />
      </PageTransition>
    </Layout>
  );
};

export default EditRequest;
