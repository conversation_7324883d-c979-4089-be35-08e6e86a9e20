/**
 * Test area listing functionality
 */

import { authenticatedConvex } from './convex-auth';

export const testAreaListing = async () => {
  try {
    console.log('🔍 Testing area listing...');
    
    // Test the authenticated area listing
    const areas = await authenticatedConvex.getAreas();
    
    console.log('✅ Areas fetched successfully:');
    console.log(`   - Total areas: ${areas.length}`);
    
    areas.forEach((area, index) => {
      console.log(`   ${index + 1}. ${area.name} (ID: ${area._id})`);
    });
    
    return {
      success: true,
      totalAreas: areas.length,
      areas: areas.map(a => ({ id: a._id, name: a.name })),
      message: 'Area listing working correctly'
    };
    
  } catch (error) {
    console.error('❌ Area listing failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Area listing failed'
    };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testAreaListing = testAreaListing;
}