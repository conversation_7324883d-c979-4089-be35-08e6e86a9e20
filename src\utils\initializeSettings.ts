/**
 * Utility to initialize default settings in the database
 * This can be run from the browser console or called from a component
 */

import { ConvexReactClient } from 'convex/react';
import { api } from '../../convex/_generated/api';

/**
 * Initialize default threshold settings
 * Call this function from browser console or a component
 */
export async function initializeDefaultSettings(convex: ConvexReactClient) {
  try {
    console.log('🔧 Initializing default settings...');
    
    // Try the simple initialization first
    const result = await convex.mutation(api.simpleInit.initializeDefaultSettings, {});
    
    console.log('✅ Settings initialized successfully:', result);
    return result;
  } catch (error) {
    console.error('❌ Error initializing settings:', error);
    
    // If simple init fails, try manual creation
    console.log('🔄 Trying manual settings creation...');
    
    try {
      // Try to create settings one by one using the settings API
      const settingsToCreate = {
        mobile_money_approval_threshold: 5000,
        bank_transfer_approval_threshold: 10000,
        whitelisted_domains: ['kmkentertainment.com', 'mybet.africa']
      };
      
      const updateResult = await convex.mutation(api.settings.updateSettings, {
        settings: settingsToCreate
      });
      
      console.log('✅ Settings created manually:', updateResult);
      return updateResult;
    } catch (manualError) {
      console.error('❌ Manual creation also failed:', manualError);
      throw manualError;
    }
  }
}

/**
 * Check current settings status
 */
export async function checkSettingsStatus(convex: ConvexReactClient) {
  try {
    console.log('🔍 Checking current settings...');
    
    const settings = await convex.query(api.settings.getPublicSettings, {});
    
    console.log('📊 Current settings:', settings);
    
    // Check if threshold settings exist
    const hasThresholds = settings.mobile_money_approval_threshold !== undefined && 
                         settings.bank_transfer_approval_threshold !== undefined;
    
    console.log('🎯 Threshold settings exist:', hasThresholds);
    
    if (!hasThresholds) {
      console.log('⚠️ Threshold settings missing. Run initializeDefaultSettings() to create them.');
    }
    
    return {
      settings,
      hasThresholds,
      missingSettings: [
        !settings.mobile_money_approval_threshold && 'mobile_money_approval_threshold',
        !settings.bank_transfer_approval_threshold && 'bank_transfer_approval_threshold',
        !settings.whitelisted_domains && 'whitelisted_domains'
      ].filter(Boolean)
    };
  } catch (error) {
    console.error('❌ Error checking settings:', error);
    throw error;
  }
}

/**
 * Browser console helper script
 * Copy and paste this into your browser console when your app is running
 */
export const BROWSER_CONSOLE_SCRIPT = `
// Initialize default settings
if (window.convex) {
  // Check current status first
  window.convex.query('settings:getPublicSettings', {})
    .then(settings => {
      console.log('📊 Current settings:', settings);
      
      const hasThresholds = settings.mobile_money_approval_threshold !== undefined;
      
      if (!hasThresholds) {
        console.log('⚠️ Threshold settings missing. Initializing...');
        
        // Try to initialize
        return window.convex.mutation('simpleInit:initializeDefaultSettings', {});
      } else {
        console.log('✅ Threshold settings already exist!');
        return settings;
      }
    })
    .then(result => {
      console.log('✅ Initialization result:', result);
      
      // Check settings again
      return window.convex.query('settings:getPublicSettings', {});
    })
    .then(finalSettings => {
      console.log('🎯 Final settings:', finalSettings);
    })
    .catch(error => {
      console.error('❌ Error:', error);
      
      // Try manual creation as fallback
      console.log('🔄 Trying manual creation...');
      
      return window.convex.mutation('settings:updateSettings', {
        settings: {
          mobile_money_approval_threshold: 5000,
          bank_transfer_approval_threshold: 10000,
          whitelisted_domains: ['kmkentertainment.com', 'mybet.africa']
        }
      });
    })
    .then(manualResult => {
      if (manualResult) {
        console.log('✅ Manual creation successful:', manualResult);
      }
    })
    .catch(finalError => {
      console.error('❌ All initialization attempts failed:', finalError);
    });
} else {
  console.error('❌ Convex client not found. Make sure you are on the app page.');
}
`;

// Export for easy access
export default {
  initializeDefaultSettings,
  checkSettingsStatus,
  BROWSER_CONSOLE_SCRIPT
};
