
import React from 'react';
import { createRoot } from 'react-dom/client'
import { ConvexProvider } from 'convex/react';
import { convex } from './lib/convex';
import App from './App.tsx'
import './index.css'

// Import manual auth test for debugging
import './lib/manual-auth-test';
import './lib/test-area-listing';
import './lib/test-complete-area-crud';
import './lib/test-complete-shop-crud';
import './lib/test-authenticated-hooks';
import './lib/test-shop-fix-verification';
import './lib/test-shop-refresh-issue';
import './lib/test-user-invitation';


// Create root and render app
createRoot(document.getElementById("root")!).render(
  <React.StrictMode>
    <ConvexProvider client={convex}>
      <App />
    </ConvexProvider>
  </React.StrictMode>
);
