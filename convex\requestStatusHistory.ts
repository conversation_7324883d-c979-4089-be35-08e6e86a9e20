import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Helper function to record a status change (internal function for use within mutations)
export async function recordStatusChange(
  ctx: any,
  args: {
    requestId: Id<"requests">;
    fromStatus?: "pending" | "approved" | "rejected" | "resubmitted" | "paid" | "cancelled";
    toStatus: "pending" | "approved" | "rejected" | "resubmitted" | "paid" | "cancelled";
    changedBy: Id<"users">;
    reason?: string;
    metadata?: any;
  }
) {
  const now = Date.now();
  
  // Record the status change
  const statusHistoryId = await ctx.db.insert("request_status_history", {
    requestId: args.requestId,
    fromStatus: args.fromStatus,
    toStatus: args.toStatus,
    changedBy: args.changedBy,
    changedAt: now,
    reason: args.reason,
    metadata: args.metadata,
  });

  return statusHistoryId;
}

// Public mutation for recording status changes (if needed externally)
export const recordStatusChangeMutation = mutation({
  args: {
    requestId: v.id("requests"),
    fromStatus: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("resubmitted"),
      v.literal("paid"),
      v.literal("cancelled")
    )),
    toStatus: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("resubmitted"),
      v.literal("paid"),
      v.literal("cancelled")
    ),
    changedBy: v.id("users"),
    reason: v.optional(v.string()),
    metadata: v.optional(v.any()),
  },
  handler: async (ctx, args) => {
    return await recordStatusChange(ctx, args);
  },
});

// Get complete status history for a request
export const getRequestStatusHistory = query({
  args: { requestId: v.id("requests") },
  handler: async (ctx, args) => {
    const statusHistory = await ctx.db
      .query("request_status_history")
      .withIndex("by_request_date", (q) => q.eq("requestId", args.requestId))
      .order("asc") // Chronological order
      .collect();

    // Enrich with user information
    const enrichedHistory = await Promise.all(
      statusHistory.map(async (entry) => {
        const user = await ctx.db.get(entry.changedBy);
        return {
          ...entry,
          changedByUser: user ? {
            _id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          } : null,
        };
      })
    );

    return enrichedHistory;
  },
});

// Get timeline events for a request (combines status history with other events)
export const getRequestTimeline = query({
  args: { requestId: v.id("requests") },
  handler: async (ctx, args) => {
    // Get the request details
    const request = await ctx.db.get(args.requestId);
    if (!request) {
      return [];
    }

    // Get status history
    const statusHistory = await ctx.db
      .query("request_status_history")
      .withIndex("by_request_date", (q) => q.eq("requestId", args.requestId))
      .order("asc")
      .collect();

    // Create timeline events from status history
    const timelineEvents = await Promise.all(
      statusHistory.map(async (entry) => {
        const user = await ctx.db.get(entry.changedBy);
        const userName = user ? `${user.firstName || ''} ${user.lastName || ''}`.trim() || user.email : 'Unknown User';
        
        return {
          type: 'status_change',
          subType: entry.toStatus,
          timestamp: entry.changedAt,
          actor: userName,
          actorId: entry.changedBy,
          title: getStatusChangeTitle(entry.fromStatus, entry.toStatus),
          description: entry.reason || getDefaultStatusDescription(entry.toStatus),
          metadata: entry.metadata,
          fromStatus: entry.fromStatus,
          toStatus: entry.toStatus,
        };
      })
    );

    // Sort chronologically
    timelineEvents.sort((a, b) => a.timestamp - b.timestamp);

    return timelineEvents;
  },
});

// Get status history for multiple requests (for dashboard/reports)
export const getMultipleRequestsStatusHistory = query({
  args: { 
    requestIds: v.array(v.id("requests")),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const limit = args.limit || 100;
    
    // Get status history for all requests
    const allStatusHistory = await ctx.db
      .query("request_status_history")
      .order("desc") // Most recent first
      .take(limit * 2); // Get more than needed to filter

    // Filter to only include requested request IDs
    const filteredHistory = allStatusHistory.filter(entry => 
      args.requestIds.includes(entry.requestId)
    ).slice(0, limit);

    // Enrich with user and request information
    const enrichedHistory = await Promise.all(
      filteredHistory.map(async (entry) => {
        const [user, request] = await Promise.all([
          ctx.db.get(entry.changedBy),
          ctx.db.get(entry.requestId),
        ]);

        return {
          ...entry,
          changedByUser: user ? {
            _id: user._id,
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          } : null,
          request: request ? {
            _id: request._id,
            ticketNumber: request.ticketNumber,
            amount: request.amount,
            shopId: request.shopId,
            areaId: request.areaId,
          } : null,
        };
      })
    );

    return enrichedHistory;
  },
});

// Helper functions
function getStatusChangeTitle(fromStatus: string | undefined, toStatus: string): string {
  if (!fromStatus) {
    return 'Request Created';
  }
  
  switch (toStatus) {
    case 'pending':
      return 'Set to Pending';
    case 'approved':
      return 'Approved';
    case 'rejected':
      return 'Rejected';
    case 'resubmitted':
      return 'Resubmitted';
    case 'paid':
      return 'Marked as Paid';
    case 'cancelled':
      return 'Cancelled';
    default:
      return `Status Changed to ${toStatus}`;
  }
}

function getDefaultStatusDescription(status: string): string {
  switch (status) {
    case 'pending':
      return 'Request is awaiting review';
    case 'approved':
      return 'Request has been approved for payment';
    case 'rejected':
      return 'Request has been rejected';
    case 'resubmitted':
      return 'Request has been resubmitted for review';
    case 'paid':
      return 'Payment has been processed';
    case 'cancelled':
      return 'Request has been cancelled';
    default:
      return `Request status changed to ${status}`;
  }
}