import React from 'react';
import { create } from 'zustand';
import { User } from '@/lib/types';
import { useAuth } from '@/lib/auth-context';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => Promise<void>;
  setUser: (user: User | null) => void;
  refreshUser: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,

  login: async (email, password, rememberMe = false) => {
    set({ isLoading: true, error: null });

    try {
      // Login is now handled by the AuthContext
      // This store method is kept for compatibility but delegates to the context
      throw new Error('Login should be handled through AuthContext.signIn()');
    } catch (error: any) {
      set({
        error: error.message || 'Failed to login',
        isLoading: false
      });
      throw error;
    }
  },

  logout: async () => {
    set({ isLoading: true });

    try {
      // Logout is now handled by the AuthContext
      throw new Error('Logout should be handled through AuthContext.signOut()');
    } catch (error: any) {
      set({
        error: error.message || 'Failed to logout',
        isLoading: false
      });
      throw error;
    }
  },

  setUser: (user) => {
    set({
      user,
      isAuthenticated: !!user
    });
  },

  refreshUser: async () => {
    set({ isLoading: true });

    try {
      // User refresh is now handled automatically by Convex
      // This method is kept for compatibility
      set({ isLoading: false });
    } catch (error: any) {
      set({
        error: error.message || 'Failed to refresh user',
        isLoading: false
      });
    }
  }
}));

// Hook to sync auth store with auth context
export const useAuthSync = () => {
  const { user: authUser, isLoading: authLoading, appUser } = useAuth();
  const sessionInfo = useQuery(api.auth.getSessionInfo);
  const { setUser } = useAuthStore();

  // Sync auth context user with store
  React.useEffect(() => {
    if (authUser && sessionInfo && appUser) {
      const storeUser: User = {
        id: appUser._id,
        name: [appUser.firstName, appUser.lastName]
          .filter(Boolean)
          .join(' ') || appUser.email,
        email: appUser.email,
        role: sessionInfo.roles[0]?.name as any || 'watcher',
        areas: sessionInfo.areas?.map(area => area.name) || [],
        avatar: appUser.profilePicture,
      };
      setUser(storeUser);
    } else if (!authLoading) {
      setUser(null);
    }
  }, [authUser, sessionInfo, authLoading, appUser, setUser]);

  return {
    user: authUser,
    sessionInfo,
    isLoading: authLoading,
  };
};

// Hook to get current user from store (for backward compatibility)
export const useCurrentUser = () => {
  const { user } = useAuthStore();
  return user;
};

// Hook to check if user is authenticated (for backward compatibility)
export const useIsAuthenticated = () => {
  const { isAuthenticated } = useAuthStore();
  return isAuthenticated;
};

// Hook to get auth loading state (for backward compatibility)
export const useAuthLoading = () => {
  const { isLoading } = useAuthStore();
  return isLoading;
};
