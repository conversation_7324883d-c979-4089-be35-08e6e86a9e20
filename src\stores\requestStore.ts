import { create } from 'zustand';
import { Request, PaymentMethod } from '@/lib/types';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { convex } from '@/lib/convex';

interface RequestState {
  requests: Request[];
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchRequests: (force?: boolean) => Promise<void>;
  getRequestById: (id: string) => Request | undefined;
  createRequest: (requestData: {
    title: string;
    shopId: string;
    shopName: string;
    amount: number;
    paymentMethod: PaymentMethod;
    customerName?: string;
    customerContact?: string;
    ticketImage?: string;
    comments?: string;
    areaId?: string;
    ticketId?: string;
    productId?: string;
    networkProvider?: string;
    bankName?: string;
    accountNumber?: string;
    accountHolderName?: string;
    workosUserId?: string;
  }) => Promise<Request>;
  updateRequest: (id: string, updates: Partial<Request>) => Promise<Request>;
  approveRequest: (id: string, approvalReason: string) => Promise<Request>;
  rejectRequest: (id: string, rejectionReason: string) => Promise<Request>;
  resubmitRequest: (id: string, updates: Partial<Request>, notes: string) => Promise<Request>;
  deleteRequest: (id: string) => Promise<void>;
}

// Cache duration: 2 minutes
const CACHE_DURATION = 2 * 60 * 1000;

// Helper function to map Convex request to Request type
function mapConvexRequestToRequest(convexRequest: any): Request {
  return {
    id: convexRequest._id,
    title: convexRequest.ticketNumber,
    shopId: convexRequest.shopId,
    shopName: convexRequest.shop?.name || '',
    amount: convexRequest.amount,
    paymentMethod: convexRequest.paymentMethod,
    status: convexRequest.status,
    createdBy: convexRequest.requestedBy,
    createdAt: new Date(convexRequest.createdAt),
    updatedAt: new Date(convexRequest.updatedAt),
    approvedBy: convexRequest.approvedBy,
    approvedAt: convexRequest.approvedAt ? new Date(convexRequest.approvedAt) : undefined,
    approvalReason: convexRequest.notes,
    rejectedBy: convexRequest.rejectedBy,
    rejectedAt: convexRequest.rejectedAt ? new Date(convexRequest.rejectedAt) : undefined,
    rejectionReason: convexRequest.rejectionReason,
    customerName: convexRequest.customerName,
    customerContact: convexRequest.customerPhone,
    ticketImage: convexRequest.ticketImageId,
    comments: convexRequest.notes,
    areaId: convexRequest.areaId,
    ticketId: convexRequest.ticketNumber,
    productId: undefined,
    networkProvider: convexRequest.mobileMoneyProvider,
    bankName: convexRequest.bankName,
    accountNumber: convexRequest.accountNumber,
    accountHolderName: convexRequest.accountName,
    resubmissionHistory: convexRequest.resubmissionHistory?.map((entry: any) => ({
      timestamp: new Date(entry.resubmittedAt),
      changes: {},
      notes: entry.reason,
      previousRejectionReason: entry.previousStatus,
    })),
  };
}

export const useRequestStore = create<RequestState>((set, get) => ({
  requests: [],
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchRequests: async (force = false) => {
    // Skip store queries when using WorkOS AuthKit (use public queries instead)
    console.log('fetchRequests called - skipping for WorkOS AuthKit mode');
    return;

    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION && state.requests.length > 0) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      const convexRequests = await convex.query(api.requests.getRequests, {});
      const requests = convexRequests.map(mapConvexRequestToRequest);
      set({ requests, isLoading: false, lastFetch: now });
    } catch (error: any) {
      console.error('Error fetching requests:', error);
      set({ error: error.message || 'Failed to fetch requests', isLoading: false });
      throw error;
    }
  },

  getRequestById: (id) => {
    return get().requests.find(request => request.id === id);
  },

  createRequest: async (requestData) => {
    set({ isLoading: true, error: null });

    try {
      const requestId = await convex.mutation(api.requests.createRequest, {
        title: requestData.title,
        ticketId: requestData.ticketId || `TKT-${Date.now()}`,
        productId: requestData.productId || '1',
        areaId: requestData.areaId as Id<"areas">,
        shopId: requestData.shopId as Id<"shops">,
        shopName: requestData.shopName,
        amount: requestData.amount,
        paymentMethod: requestData.paymentMethod,
        customerName: requestData.customerName || '',
        customerContact: requestData.customerContact || '',
        networkProvider: requestData.networkProvider,
        bankName: requestData.bankName,
        accountNumber: requestData.accountNumber,
        accountHolderName: requestData.accountHolderName,
        comments: requestData.comments,
        ticketImage: requestData.ticketImage,
        workosUserId: requestData.workosUserId,
      });

      // Create a basic request object without fetching (to avoid auth issues)
      const newRequest: Request = {
        id: requestId,
        title: requestData.title,
        shopId: requestData.shopId,
        shopName: requestData.shopName,
        amount: requestData.amount,
        paymentMethod: requestData.paymentMethod,
        status: 'pending',
        createdBy: requestId, // Placeholder - will be updated when data refreshes
        createdAt: new Date(),
        updatedAt: new Date(),
        customerName: requestData.customerName || '',
        customerContact: requestData.customerContact || '',
        ticketImage: requestData.ticketImage,
        comments: requestData.comments,
        areaId: requestData.areaId || '',
        ticketId: requestData.ticketId || `TKT-${Date.now()}`,
        productId: requestData.productId || '1',
        networkProvider: requestData.networkProvider,
        bankName: requestData.bankName,
        accountNumber: requestData.accountNumber,
        accountHolderName: requestData.accountHolderName,
      };
      
      set(state => ({
        requests: [...state.requests, newRequest],
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return newRequest;
    } catch (error: any) {
      console.error('Error creating request:', error);
      set({ error: error.message || 'Failed to create request', isLoading: false });
      throw error;
    }
  },

  updateRequest: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      // Note: Direct updates are not implemented in Convex
      // Use specific operations like approve/reject instead
      throw new Error('Direct request updates not supported. Use approve, reject, or resubmit operations.');
    } catch (error: any) {
      console.error('Error updating request:', error);
      set({ error: error.message || 'Failed to update request', isLoading: false });
      throw error;
    }
  },

  approveRequest: async (id, approvalReason) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Approving request', id, 'with reason', approvalReason);

      await convex.mutation(api.requests.approveRequest, {
        requestId: id as Id<"requests">,
        notes: approvalReason,
      });

      // Fetch updated request
      const updatedRequest = await convex.query(api.requests.getRequest, { 
        requestId: id as Id<"requests"> 
      });

      if (!updatedRequest) {
        throw new Error('Failed to retrieve approved request');
      }

      const approvedRequest = mapConvexRequestToRequest(updatedRequest);

      console.log('Store: Request approved successfully', approvedRequest);

      // Update local state and invalidate cache
      set(state => ({
        requests: state.requests.map(request => request.id === id ? approvedRequest : request),
        isLoading: false,
        lastFetch: null // Invalidate cache to force refresh on next fetch
      }));

      // Force refresh data to ensure all users get updated data
      setTimeout(() => {
        get().fetchRequests(true);
      }, 100);

      return approvedRequest;
    } catch (error: any) {
      console.error('Store: Error approving request', error);
      set({ error: error.message || 'Failed to approve request', isLoading: false });
      throw error;
    }
  },

  rejectRequest: async (id, rejectionReason) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Rejecting request', id, 'with reason', rejectionReason);

      await convex.mutation(api.requests.rejectRequest, {
        requestId: id as Id<"requests">,
        reason: rejectionReason,
      });

      // Fetch updated request
      const updatedRequest = await convex.query(api.requests.getRequest, { 
        requestId: id as Id<"requests"> 
      });

      if (!updatedRequest) {
        throw new Error('Failed to retrieve rejected request');
      }

      const rejectedRequest = mapConvexRequestToRequest(updatedRequest);

      console.log('Store: Request rejected successfully', rejectedRequest);

      // Update local state and invalidate cache
      set(state => ({
        requests: state.requests.map(request => request.id === id ? rejectedRequest : request),
        isLoading: false,
        lastFetch: null // Invalidate cache to force refresh on next fetch
      }));

      // Force refresh data to ensure all users get updated data
      setTimeout(() => {
        get().fetchRequests(true);
      }, 100);

      return rejectedRequest;
    } catch (error: any) {
      console.error('Store: Error rejecting request', error);
      set({ error: error.message || 'Failed to reject request', isLoading: false });
      throw error;
    }
  },

  resubmitRequest: async (id, updates, notes) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Resubmitting request', id);

      const newRequestId = await convex.mutation(api.requests.resubmitRequest, {
        originalRequestId: id as Id<"requests">,
        ticketNumber: updates.ticketId || `TKT-${Date.now()}`,
        amount: updates.amount || 0,
        paymentMethod: updates.paymentMethod === 'mobile_money' ? 'mobile_money' : 'bank_transfer',
        priority: 'medium',
        customerName: updates.customerName || '',
        customerPhone: updates.customerContact || '',
        mobileMoneyNumber: updates.paymentMethod === 'mobile_money' ? updates.customerContact : undefined,
        mobileMoneyProvider: updates.networkProvider,
        bankName: updates.bankName,
        accountNumber: updates.accountNumber,
        accountName: updates.accountHolderName,
        notes: updates.comments,
        resubmissionReason: notes,
      });

      // Fetch the resubmitted request
      const resubmittedRequest = await convex.query(api.requests.getRequest, { 
        requestId: newRequestId 
      });

      if (!resubmittedRequest) {
        throw new Error('Failed to retrieve resubmitted request');
      }

      const newRequest = mapConvexRequestToRequest(resubmittedRequest);

      console.log('Store: Request resubmitted successfully', newRequest);

      set(state => ({
        requests: [...state.requests, newRequest],
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));

      return newRequest;
    } catch (error: any) {
      console.error('Store: Error resubmitting request', error);
      set({ error: error.message || 'Failed to resubmit request', isLoading: false });
      throw error;
    }
  },

  deleteRequest: async (id) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Deleting request', id);

      await convex.mutation(api.requests.cancelRequest, {
        requestId: id as Id<"requests">,
        reason: 'Request deleted by user',
      });

      console.log('Store: Request deleted successfully');

      set(state => ({
        requests: state.requests.filter(request => request.id !== id),
        isLoading: false,
        lastFetch: null // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error deleting request', error);
      set({ error: error.message || 'Failed to delete request', isLoading: false });
      throw error;
    }
  }
}));

// React hooks for components using Convex real-time queries
export const useRequestsQuery = (filters?: any) => {
  return useQuery(api.requests.getRequests, filters || {});
};

export const useRequestQuery = (requestId: string) => {
  return useQuery(api.requests.getRequest, { 
    requestId: requestId as Id<"requests"> 
  });
};

export const useCreateRequestMutation = () => {
  return useMutation(api.requests.createRequest);
};

export const useApproveRequestMutation = () => {
  return useMutation(api.requests.approveRequest);
};

export const useRejectRequestMutation = () => {
  return useMutation(api.requests.rejectRequest);
};

export const useResubmitRequestMutation = () => {
  return useMutation(api.requests.resubmitRequest);
};

export const useCancelRequestMutation = () => {
  return useMutation(api.requests.cancelRequest);
};

export const useMarkAsPaidMutation = () => {
  return useMutation(api.requests.markAsPaid);
};
