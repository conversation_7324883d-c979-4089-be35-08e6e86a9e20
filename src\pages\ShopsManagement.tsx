
import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import { useAuth } from '@/lib/auth-context';
import PageTransition from '@/components/common/PageTransition';
import { Button } from '@/components/ui/button';
import { PlusCircle, RefreshCcw, Edit, Trash, Download, Upload } from 'lucide-react';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { shopSchema, areaSchema } from '@/lib/validations/shopValidation';
import * as z from 'zod';
import { toast } from '@/components/ui/use-toast';
import { Map, Store } from 'lucide-react';

interface Shop {
  _id: string;
  name: string;
  location: string;
  areaId: string;
  managerId: string;
}

interface CSVTemplate {
  'Shop Name': string;
  'Location': string;
  'Area': string;
}

const ShopsManagement: React.FC = () => {
  useRequireAuth(['accounts']);
  const { user: workosUser } = useAuth();

  // Use Convex queries for data with WorkOS integration
  const areas = useQuery(
    api.areas.getAreas,
    workosUser?.id ? { workosUserId: workosUser.id, isActive: true } : "skip"
  ) || [];
  const shops = useQuery(
    api.shops.getShops,
    workosUser?.id ? { workosUserId: workosUser.id, isActive: true } : "skip"
  ) || [];
  
  // Use Convex mutations for actions
  const createAreaMutation = useMutation(api.areas.createArea);
  const updateAreaMutation = useMutation(api.areas.updateArea);
  const deleteAreaMutation = useMutation(api.areas.deleteArea);
  const createShopMutation = useMutation(api.shops.createShop);
  const updateShopMutation = useMutation(api.shops.updateShop);
  const deleteShopMutation = useMutation(api.shops.deleteShop);
  
  const areasLoading = !areas;
  const shopsLoading = !shops;

  const [newAreaName, setNewAreaName] = useState('');
  const [isAreaDialogOpen, setIsAreaDialogOpen] = useState(false);
  const [isEditAreaDialogOpen, setIsEditAreaDialogOpen] = useState(false);
  const [isDeleteAreaDialogOpen, setIsDeleteAreaDialogOpen] = useState(false);
  const [selectedArea, setSelectedArea] = useState<{ _id: string; name: string } | null>(null);
  const [editAreaName, setEditAreaName] = useState('');

  const [newShop, setNewShop] = useState({
    name: '',
    location: '',
    areaId: '',
  });
  const [isShopDialogOpen, setIsShopDialogOpen] = useState(false);
  const [selectedShop, setSelectedShop] = useState<Shop | null>(null);
  const [isDeleteShopDialogOpen, setIsDeleteShopDialogOpen] = useState(false);
  const [isEditShopDialogOpen, setIsEditShopDialogOpen] = useState(false);
  const [editShop, setEditShop] = useState<{
    _id: string;
    name: string;
    location: string;
    areaId: string;
  } | null>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isBulkImportOpen, setIsBulkImportOpen] = useState(false);

  // Track if a refresh is already in progress
  const refreshTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Cleanup function to clear any pending timeouts
    return () => {
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }
    };
  }, []);

  // Handle refresh action with debounce
  const handleRefresh = async () => {
    // Prevent multiple refreshes
    if (isRefreshing) return;

    try {
      // Set refreshing state
      setIsRefreshing(true);
      console.log('Starting data refresh...');

      // Set a safety timeout to reset the refreshing state after 10 seconds
      // This prevents the button from being stuck in loading state if something goes wrong
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
      }

      refreshTimeoutRef.current = setTimeout(() => {
        if (isRefreshing) {
          console.warn('Refresh operation timed out, resetting state');
          setIsRefreshing(false);
        }
      }, 10000); // 10 second timeout

      // Convex queries automatically refresh, so we just simulate a refresh
      await new Promise(resolve => setTimeout(resolve, 500));

      console.log('Refresh completed successfully:', {
        areasCount: areas.length,
        shopsCount: shops.length
      });

      // Show success message
      toast({
        title: "Refreshed",
        description: "Shop and area data has been refreshed",
      });
    } catch (error) {
      console.error('Error refreshing data:', error);
      toast({
        title: "Refresh Failed",
        description: "There was an error refreshing the data",
        variant: "destructive"
      });
    } finally {
      // Clear the safety timeout
      if (refreshTimeoutRef.current) {
        clearTimeout(refreshTimeoutRef.current);
        refreshTimeoutRef.current = null;
      }

      // Reset refreshing state
      setIsRefreshing(false);
    }
  };

  const handleAddArea = async () => {
    try {
      // Validate the area name using Zod schema
      const validatedData = areaSchema.parse({
        name: newAreaName
      });

      // Set loading state
      setIsLoading(true);

      // Create the area with validated data
      await createAreaMutation({ 
        name: validatedData.name,
        workosUserId: workosUser?.id || ""
      });

      toast({
        title: "Area added",
        description: `Area "${validatedData.name}" has been added successfully`,
      });

      // Reset form and close dialog
      setNewAreaName('');
      setIsAreaDialogOpen(false);
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        // Format and display validation errors
        const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
        toast({
          title: "Validation Error",
          description: errorMessages,
          variant: "destructive",
        });
      } else {
        // Handle other errors
        console.error('Error adding area:', error);
        toast({
          title: "Error",
          description: "Failed to add area",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddShop = async () => {
    try {
      // Validate the shop data using Zod schema - do this before setting loading state
      const validatedData = shopSchema.parse({
        name: newShop.name,
        location: newShop.location,
        areaId: newShop.areaId
      });

      // Set loading state
      setIsLoading(true);

      // Close the dialog immediately after validation to improve perceived performance
      // This makes the UI feel more responsive
      const shopName = validatedData.name; // Store for toast message

      // Reset form and close dialog before the API call
      setNewShop({
        name: '',
        location: '',
        areaId: '',
      });
      setIsShopDialogOpen(false);

      // Create the shop with validated data
      await createShopMutation({
        name: validatedData.name,
        location: validatedData.location,
        areaId: validatedData.areaId,
        workosUserId: workosUser?.id || ""
      });

      // Convex queries automatically refresh

      // Show success toast
      toast({
        title: "Shop added",
        description: `Shop "${shopName}" has been added successfully`,
      });
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        // Format and display validation errors
        const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
        toast({
          title: "Validation Error",
          description: errorMessages,
          variant: "destructive",
        });
      } else {
        // Handle other errors
        console.error('Error adding shop:', error);
        toast({
          title: "Error",
          description: "Failed to add shop",
          variant: "destructive",
        });

        // If we closed the dialog but the operation failed, reopen it
        if (!isShopDialogOpen) {
          setIsShopDialogOpen(true);
        }
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteArea = async () => {
    if (!selectedArea) return;

    try {
      const areaShops = shops.filter(shop => shop.areaId === selectedArea._id);
      if (areaShops.length > 0) {
        toast({
          title: "Cannot delete area",
          description: "This area has associated shops. Please remove or reassign the shops first.",
          variant: "destructive",
        });
        return;
      }

      await deleteAreaMutation({ areaId: selectedArea._id, workosUserId: workosUser?.id || "" });
      toast({
        title: "Area deleted",
        description: "Area has been deleted successfully",
      });
      setIsDeleteAreaDialogOpen(false);
      setSelectedArea(null);
      // Remove fetchAreas() since the store already updates the areas list
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete area",
        variant: "destructive",
      });
    }
  };

  const [isDeletingShop, setIsDeletingShop] = useState(false);

  const handleDeleteShop = async () => {
    if (!selectedShop) return;

    try {
      // Set loading state
      setIsDeletingShop(true);
      console.log('Deleting shop:', selectedShop._id);

      // Delete the shop
      await deleteShopMutation({ shopId: selectedShop._id, workosUserId: workosUser?.id || "" });

      // Show success message
      toast({
        title: "Shop deleted",
        description: `Shop "${selectedShop.name}" has been deleted successfully`,
      });

      // Close dialog and reset form
      setIsDeleteShopDialogOpen(false);
      setSelectedShop(null);

      // Refresh the shops list
      await fetchShops();
    } catch (error) {
      console.error('Error deleting shop:', error);
      toast({
        title: "Error",
        description: "Failed to delete shop. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsDeletingShop(false);
    }
  };

  const handleEditArea = async () => {
    if (!selectedArea) return;

    try {
      // Validate the area name using Zod schema
      const validatedData = areaSchema.parse({
        name: editAreaName
      });

      // Set loading state
      setIsLoading(true);

      // Update the area with validated data
      await updateAreaMutation({ 
        areaId: selectedArea._id, 
        name: validatedData.name,
        workosUserId: workosUser?.id || ""
      });

      toast({
        title: "Area updated",
        description: "Area has been updated successfully",
      });

      // Close dialog and reset form
      setIsEditAreaDialogOpen(false);
      setSelectedArea(null);
      setEditAreaName('');
      // Store already updates the areas list
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        // Format and display validation errors
        const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
        toast({
          title: "Validation Error",
          description: errorMessages,
          variant: "destructive",
        });
      } else {
        // Handle other errors
        console.error('Error updating area:', error);
        toast({
          title: "Error",
          description: "Failed to update area",
          variant: "destructive",
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  const [isEditingShop, setIsEditingShop] = useState(false);

  const handleEditShop = async () => {
    if (!editShop) return;

    try {
      // Validate the shop data using Zod schema
      const validatedData = shopSchema.parse({
        name: editShop.name,
        location: editShop.location,
        areaId: editShop.areaId
      });

      // Set loading state
      setIsEditingShop(true);
      console.log('Updating shop:', editShop._id);

      // Update the shop with validated data
      await updateShopMutation({
        shopId: editShop._id,
        name: validatedData.name,
        location: validatedData.location,
        areaId: validatedData.areaId,
        workosUserId: workosUser?.id || ""
      });

      // Refresh the shops list
      await fetchShops();

      toast({
        title: "Success",
        description: `Shop "${validatedData.name}" updated successfully`,
      });

      // Close dialog and reset form
      setIsEditShopDialogOpen(false);
      setEditShop(null);
    } catch (error) {
      // Handle validation errors
      if (error instanceof z.ZodError) {
        // Format and display validation errors
        const errorMessages = error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join('\n');
        toast({
          title: "Validation Error",
          description: errorMessages,
          variant: "destructive",
        });
      } else {
        // Handle other errors
        console.error('Error updating shop:', error);
        toast({
          title: "Error",
          description: "Failed to update shop. Please try again.",
          variant: "destructive",
        });
      }
    } finally {
      setIsEditingShop(false);
    }
  };

  const downloadTemplate = () => {
    const headers = ['Shop Name', 'Location', 'Area'];
    const csv = [headers.join(',')].join('\n');
    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'shops-template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const rows = text.split('\n');
      const headers = rows[0].split(',');
      const data: CSVTemplate[] = [];

      for (let i = 1; i < rows.length; i++) {
        if (!rows[i].trim()) continue;
        const values = rows[i].split(',');
        data.push({
          'Shop Name': values[0].trim(),
          'Location': values[1].trim(),
          'Area': values[2].trim()
        });
      }

      // Process the data
      const uniqueAreas = [...new Set(data.map(row => row.Area))];

      // Create new areas first
      for (const areaName of uniqueAreas) {
        const existingArea = areas.find(a => a.name.toLowerCase() === areaName.toLowerCase());
        if (!existingArea) {
          await createAreaMutation({ 
            name: areaName,
            workosUserId: workosUser?.id || ""
          });
        }
      }

      // Fetch updated areas to get new IDs
      await fetchAreas();

      // Create shops
      for (const row of data) {
        const area = areas.find(a => a.name.toLowerCase() === row.Area.toLowerCase());
        if (area) {
          await createShopMutation({
            name: row['Shop Name'],
            location: row.Location,
            areaId: area._id,
            workosUserId: workosUser?.id || ""
          });
        }
      }

      toast({
        title: "Import Successful",
        description: `Imported ${data.length} shops with their respective areas.`
      });
      setIsBulkImportOpen(false);
    } catch (error) {
      toast({
        title: "Import Failed",
        description: "There was an error processing the CSV file.",
        variant: "destructive"
      });
    }
  };

  return (
    <Layout>
      <PageTransition>
        <div className="container py-6 space-y-6">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Shops Management</h1>
              <p className="text-muted-foreground mt-1">
                Manage areas and shops in the system
              </p>
            </div>

            <Button
              variant="outline"
              size="icon"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="relative"
              aria-label="Refresh data"
              title="Refresh shop and area data"
            >
              {isRefreshing ? (
                <>
                  <RefreshCcw className="h-4 w-4 animate-spin" />
                  <span className="sr-only">Refreshing data...</span>
                </>
              ) : (
                <RefreshCcw className="h-4 w-4" />
              )}
            </Button>
          </div>

          <Tabs defaultValue="areas" className="space-y-8">
            <TabsList className="grid w-full max-w-md grid-cols-2 p-1 bg-muted rounded-lg">
              <TabsTrigger
                value="areas"
                className="rounded-md data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm py-2.5 px-3 text-base font-medium transition-all"
              >
                <div className="flex items-center justify-center gap-2 w-full">
                  <Map className="h-4 w-4" />
                  Areas
                </div>
              </TabsTrigger>
              <TabsTrigger
                value="shops"
                className="rounded-md data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm py-2.5 px-3 text-base font-medium transition-all"
              >
                <div className="flex items-center justify-center gap-2 w-full">
                  <Store className="h-4 w-4" />
                  Shops
                </div>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="areas" className="mt-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div>
                    <CardTitle>Areas</CardTitle>
                    <CardDescription>
                      Manage geographical areas for shops
                    </CardDescription>
                  </div>

                  <Dialog open={isAreaDialogOpen} onOpenChange={setIsAreaDialogOpen}>
                    <DialogTrigger asChild>
                      <Button className="ml-auto">
                        <PlusCircle className="mr-2 h-4 w-4" />
                        Add Area
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Add New Area</DialogTitle>
                        <DialogDescription>
                          Create a new geographical area for organizing shops
                        </DialogDescription>
                      </DialogHeader>
                      <div className="space-y-4 py-4">
                        <div className="space-y-2">
                          <Label htmlFor="area-name">Area Name</Label>
                          <Input
                            id="area-name"
                            placeholder="Enter area name"
                            value={newAreaName}
                            onChange={(e) => setNewAreaName(e.target.value)}
                          />
                        </div>
                      </div>
                      <DialogFooter>
                        <Button variant="outline" onClick={() => setIsAreaDialogOpen(false)}>
                          Cancel
                        </Button>
                        <Button onClick={handleAddArea}>
                          Add Area
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Area Name</TableHead>
                        <TableHead>Shops</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {areas.map((area) => {
                        const areaShops = shops.filter(s => s.areaId === area._id);
                        return (
                          <TableRow key={area._id}>
                            <TableCell className="font-medium">{area._id}</TableCell>
                            <TableCell>{area.name}</TableCell>
                            <TableCell>{areaShops.length}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedArea(area);
                                    setEditAreaName(area.name);
                                    setIsEditAreaDialogOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedArea(area);
                                    setIsDeleteAreaDialogOpen(true);
                                  }}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="shops" className="mt-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between pb-2">
                  <div>
                    <CardTitle>Shops</CardTitle>
                    <CardDescription>
                      Manage shops and their areas
                    </CardDescription>
                  </div>

                  <div className="flex space-x-2">
                    <Button onClick={() => setIsShopDialogOpen(true)}>
                      Add Shop
                    </Button>
                    <Button variant="outline" onClick={() => setIsBulkImportOpen(true)}>
                      Bulk Import
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>ID</TableHead>
                        <TableHead>Shop Name</TableHead>
                        <TableHead>Location</TableHead>
                        <TableHead>Area</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {shops.map((shop) => {
                        const area = areas.find(a => a._id === shop.areaId);
                        return (
                          <TableRow key={shop._id}>
                            <TableCell className="font-medium">{shop._id}</TableCell>
                            <TableCell>{shop.name}</TableCell>
                            <TableCell>{shop.location}</TableCell>
                            <TableCell>{area?.name || 'Unassigned'}</TableCell>
                            <TableCell className="text-right">
                              <div className="flex justify-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setEditShop({
                                      _id: shop._id,
                                      name: shop.name,
                                      location: shop.location,
                                      areaId: shop.areaId || '', // Provide default empty string if areaId is undefined
                                    });
                                    setIsEditShopDialogOpen(true);
                                  }}
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => {
                                    setSelectedShop({
                                      _id: shop._id,
                                      name: shop.name,
                                      location: shop.location,
                                      areaId: shop.areaId || '', // Provide default empty string if areaId is undefined
                                      managerId: shop.managerId || '', // Provide default empty string if managerId is undefined
                                    });
                                    setIsDeleteShopDialogOpen(true);
                                  }}
                                >
                                  <Trash className="h-4 w-4" />
                                </Button>
                              </div>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </PageTransition>

      <Dialog open={isDeleteAreaDialogOpen} onOpenChange={setIsDeleteAreaDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Area</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedArea?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteAreaDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteArea}
              disabled={!selectedArea}
            >
              Delete Area
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isDeleteShopDialogOpen} onOpenChange={setIsDeleteShopDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Shop</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {selectedShop?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteShopDialogOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteShop}
              disabled={!selectedShop || isDeletingShop}
              className="min-w-[120px]"
            >
              {isDeletingShop ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></span>
                  Deleting...
                </>
              ) : (
                "Delete Shop"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditAreaDialogOpen} onOpenChange={setIsEditAreaDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Area</DialogTitle>
            <DialogDescription>
              Update the area name
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-area-name">Area Name</Label>
              <Input
                id="edit-area-name"
                placeholder="Enter area name"
                value={editAreaName}
                onChange={(e) => setEditAreaName(e.target.value)}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditAreaDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleEditArea}>
              Save Changes
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isEditShopDialogOpen} onOpenChange={setIsEditShopDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Shop</DialogTitle>
            <DialogDescription>
              Update shop details
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="edit-shop-name">Shop Name</Label>
              <Input
                id="edit-shop-name"
                placeholder="Enter shop name"
                value={editShop?.name || ''}
                onChange={(e) => setEditShop(prev => prev ? {...prev, name: e.target.value} : null)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-shop-location">Location</Label>
              <Input
                id="edit-shop-location"
                placeholder="Enter shop location"
                value={editShop?.location || ''}
                onChange={(e) => setEditShop(prev => prev ? {...prev, location: e.target.value} : null)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-shop-area">Area</Label>
              <Select
                value={editShop?.areaId || ''}
                onValueChange={(value) => setEditShop(prev => prev ? {...prev, areaId: value} : null)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an area" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map((area) => (
                    <SelectItem key={area._id} value={area._id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditShopDialogOpen(false);
              setEditShop(null);
            }}>
              Cancel
            </Button>
            <Button
              onClick={handleEditShop}
              disabled={isEditingShop}
              className="min-w-[120px]"
            >
              {isEditingShop ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></span>
                  Saving...
                </>
              ) : (
                "Save Changes"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog open={isBulkImportOpen} onOpenChange={setIsBulkImportOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Bulk Import Shops</DialogTitle>
            <DialogDescription>
              Download the template, fill it with your shops data, and upload it back.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <h4 className="mb-4 text-sm font-medium">CSV Template Structure:</h4>
            <div className="mb-4 p-2 bg-muted rounded-md text-sm">
              <code>Shop Name, Location, Area</code>
            </div>
            <div className="flex flex-col gap-4">
              <Button variant="outline" onClick={downloadTemplate}>
                <Download className="mr-2 h-4 w-4" />
                Download Template
              </Button>
              <div className="flex items-center gap-2">
                <input
                  type="file"
                  accept=".csv"
                  className="hidden"
                  id="csv-upload"
                  onChange={handleFileUpload}
                />
                <Button variant="default" className="w-full" onClick={() => document.getElementById('csv-upload')?.click()}>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload CSV
                </Button>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsBulkImportOpen(false)}>
              Cancel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add the individual shop dialog */}
      <Dialog open={isShopDialogOpen} onOpenChange={setIsShopDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Shop</DialogTitle>
            <DialogDescription>
              Create a new shop and assign it to an area
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="shop-name">Shop Name</Label>
              <Input
                id="shop-name"
                placeholder="Enter shop name"
                value={newShop.name}
                onChange={(e) => setNewShop({ ...newShop, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="shop-location">Location</Label>
              <Input
                id="shop-location"
                placeholder="Enter shop location"
                value={newShop.location}
                onChange={(e) => setNewShop({ ...newShop, location: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="shop-area">Area</Label>
              <Select
                value={newShop.areaId}
                onValueChange={(value) => setNewShop({ ...newShop, areaId: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select an area" />
                </SelectTrigger>
                <SelectContent>
                  {areas.map((area) => (
                    <SelectItem key={area._id} value={area._id}>
                      {area.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsShopDialogOpen(false);
              setNewShop({ name: '', location: '', areaId: '' });
            }}>
              Cancel
            </Button>
            <Button
              onClick={handleAddShop}
              disabled={isLoading}
              className="min-w-[100px]"
            >
              {isLoading ? (
                <>
                  <span className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></span>
                  Adding...
                </>
              ) : (
                "Add Shop"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Layout>
  );
};

export default ShopsManagement;
