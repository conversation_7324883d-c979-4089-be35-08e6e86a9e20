
import { create } from 'zustand';
import { User, Request, UserRole, RequestStatus, Shop, DashboardStats, AppNotification } from './types';

type AuthState = {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string, rememberMe?: boolean) => Promise<void>;
  logout: () => void;
  setUser: (user: User) => void;
};

export const useAuthStore = create<AuthState>((set) => ({
  user: null,
  isAuthenticated: false,
  login: async (email, password, rememberMe = false) => {
    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        // Mock different user roles for demo purposes
        let user: User;

        if (email.includes('manager')) {
          user = {
            id: '1',
            name: 'John <PERSON>',
            email,
            role: 'shop_manager',
            shop: 'Downtown Shop'
          };
        } else if (email.includes('support')) {
          user = {
            id: '2',
            name: '<PERSON>',
            email,
            role: 'shop_support'
          };
        } else if (email.includes('accounts')) {
          user = {
            id: '3',
            name: '<PERSON> Accountant',
            email,
            role: 'accounts'
          };
        } else {
          user = {
            id: '4',
            name: '<PERSON>',
            email,
            role: 'watcher'
          };
        }

        // Store both user data and authentication state
        if (rememberMe) {
          localStorage.setItem('auth', JSON.stringify({
            user,
            isAuthenticated: true
          }));
        } else {
          localStorage.removeItem('auth');
        }

        set({ user, isAuthenticated: true });
        resolve();
      }, 800);
    });
  },
  logout: () => {
    localStorage.removeItem('auth');
    set({ user: null, isAuthenticated: false });
  },
  setUser: (user) => {
    const isAuthenticated = !!user;
    set({ user, isAuthenticated });

    // Update localStorage if it exists (meaning "Remember Me" was checked)
    if (localStorage.getItem('auth')) {
      localStorage.setItem('auth', JSON.stringify({ user, isAuthenticated }));
    }
  },
}));

type AreaState = {
  areas: { id: string; name: string }[];
  isLoading: boolean;
  error: string | null;
  fetchAreas: () => Promise<{ id: string; name: string }[]>;
  createArea: (name: string) => Promise<void>;
  updateArea: (id: string, name: string) => Promise<void>;
  deleteArea: (id: string) => Promise<void>;
};

export const useAreaStore = create<AreaState>((set, get) => ({
  areas: [],
  isLoading: false,
  error: null,
  fetchAreas: async () => {
    set({ isLoading: true, error: null });

    // Remove the mock data and just return current state
    const currentAreas = get().areas;
    set({ isLoading: false });
    return currentAreas;
  },
  createArea: async (name: string) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const newArea = {
        id: Math.random().toString(36).substr(2, 9), // Generate random ID
        name
      };

      set(state => ({
        areas: [...state.areas, newArea],
        isLoading: false
      }));
    } catch (error) {
      set({ error: 'Failed to create area', isLoading: false });
      throw error;
    }
  },
  updateArea: async (id: string, name: string) => {
    set({ isLoading: true, error: null });

    return new Promise((resolve) => {
      setTimeout(() => {
        set((state) => ({
          areas: state.areas.map((area) =>
            area.id === id ? { ...area, name } : area
          ),
          isLoading: false,
        }));
        resolve();
      }, 800);
    });
  },
  deleteArea: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      // Simulating API call with timeout
      await new Promise(resolve => setTimeout(resolve, 500));

      set(state => ({
        areas: state.areas.filter(area => area.id !== id),
        isLoading: false,
      }));
    } catch (error) {
      set({ error: 'Failed to delete area', isLoading: false });
      throw error;
    }
  },
}));

type RequestStore = {
  requests: Request[];
  isLoading: boolean;
  error: string | null;
  createRequest: (request: Omit<Request, 'id' | 'createdAt' | 'updatedAt' | 'status'>) => Promise<Request>;
  updateRequest: (id: string, updates: Partial<Request>) => Promise<Request>;
  fetchRequests: () => Promise<void>;
  getRequestById: (id: string) => Request | undefined;
  approveRequest: (id: string, approverId: string, approvalReason: string) => Promise<Request>;
  rejectRequest: (id: string, rejecterId: string, reason: string) => Promise<Request>;
  resubmitRequest: (id: string, updates: Partial<Request>) => Promise<Request>;
  deleteRequest: (id: string) => Promise<void>;
};

export const useRequestStore = create<RequestStore>((set, get) => ({
  requests: [],
  isLoading: false,
  error: null,
  createRequest: async (requestData) => {
    set({ isLoading: true, error: null });

    try {
      // Check for existing pending request with same ticket ID
      const existingPendingRequest = get().requests.find(r =>
        r.ticketId === requestData.ticketId &&
        r.status === 'pending'
      );

      if (existingPendingRequest) {
        set({ isLoading: false });
        useNotificationStore.getState().addNotification({
          title: 'Duplicate Ticket ID',
          message: `A pending request with ticket ID ${requestData.ticketId} already exists. Please use a different ticket ID.`,
          type: 'error',
          recipientId: requestData.createdBy
        });
        throw new Error(`A pending request with ticket ID ${requestData.ticketId} already exists`);
      }

      // Continue with request creation if no pending duplicate found
      const newRequest: Request = {
        id: Math.random().toString(36).substring(2, 9),
        status: 'pending',
        createdAt: new Date(),
        updatedAt: new Date(),
        ...requestData,
      };

      // Update requests state
      set((state) => ({
        requests: [...state.requests, newRequest],
        isLoading: false,
      }));

      // Create notifications for admins and support staff
      const users = useUserStore.getState().users;
      const notificationTargets = users.filter(u =>
        ['accounts', 'shop_support'].includes(u.role)
      );

      notificationTargets.forEach(admin => {
        useNotificationStore.getState().addNotification({
          title: 'New Cash Request',
          message: `New request for ${requestData.amount} from ${requestData.shopName}`,
          type: 'request_created',
          requestId: newRequest.id,
          recipientId: admin.id,
        });
      });

      return newRequest;
    } catch (error) {
      set({ error: 'Failed to create request', isLoading: false });
      throw error;
    }
  },
  updateRequest: async (id, updates) => {
    set({ isLoading: true, error: null });

    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        set((state) => {
          const requestIndex = state.requests.findIndex(r => r.id === id);
          if (requestIndex === -1) {
            set({ error: 'Request not found', isLoading: false });
            return state;
          }

          const updatedRequest = {
            ...state.requests[requestIndex],
            ...updates,
            updatedAt: new Date(),
          };

          const updatedRequests = [...state.requests];
          updatedRequests[requestIndex] = updatedRequest;

          return {
            requests: updatedRequests,
            isLoading: false,
          };
        });

        const updatedRequest = get().requests.find(r => r.id === id);
        resolve(updatedRequest as Request);
      }, 800);
    });
  },
  fetchRequests: async () => {
    set({ isLoading: true, error: null });

    // Instead of using mock data, return the actual stored requests
    return new Promise((resolve) => {
      setTimeout(() => {
        const user = useAuthStore.getState().user;
        const currentRequests = get().requests;

        // Filter based on user role
        let filteredRequests = currentRequests;
        if (user?.role === 'shop_manager') {
          // Shop managers only see their own requests
          filteredRequests = currentRequests.filter(r => r.createdBy === user.id);
        }
        // Admin and other roles see all requests

        set({ requests: filteredRequests, isLoading: false });
        resolve();
      }, 800);
    });
  },
  getRequestById: (id) => {
    return get().requests.find(r => r.id === id);
  },
  approveRequest: async (id, approverId, approvalReason) => {
    set({ isLoading: true, error: null });

    try {
      const request = get().requests.find(r => r.id === id);
      if (!request) throw new Error('Request not found');

      const updatedRequest = {
        ...request,
        status: 'approved' as RequestStatus,
        approvedBy: approverId,
        approvedAt: new Date(),
        approvalReason,
        updatedAt: new Date(),
      };

      set((state) => ({
        requests: state.requests.map(r => r.id === id ? updatedRequest : r),
        isLoading: false,
      }));

      // Notify shop manager
      useNotificationStore.getState().addNotification({
        title: 'Request Approved',
        message: `Your request for ${request.amount} has been approved`,
        type: 'request_approved',
        requestId: id,
        recipientId: request.createdBy,
      });

      return updatedRequest;
    } catch (error) {
      set({ error: 'Failed to approve request', isLoading: false });
      throw error;
    }
  },
  rejectRequest: async (id, rejecterId, reason) => {
    set({ isLoading: true, error: null });

    try {
      const request = get().requests.find(r => r.id === id);
      if (!request) throw new Error('Request not found');

      const updatedRequest = {
        ...request,
        status: 'rejected' as RequestStatus,
        rejectedBy: rejecterId,
        rejectedAt: new Date(),
        rejectionReason: reason,
        updatedAt: new Date(),
      };

      set((state) => ({
        requests: state.requests.map(r => r.id === id ? updatedRequest : r),
        isLoading: false,
      }));

      // Notify shop manager
      useNotificationStore.getState().addNotification({
        title: 'Request Rejected',
        message: `Your request for ${request.amount} has been rejected: ${reason}`,
        type: 'request_rejected',
        requestId: id,
        recipientId: request.createdBy,
      });

      return updatedRequest;
    } catch (error) {
      set({ error: 'Failed to reject request', isLoading: false });
      throw error;
    }
  },
  resubmitRequest: async (id, updates) => {
    set({ isLoading: true, error: null });

    try {
      const originalRequest = get().requests.find(r => r.id === id);
      if (!originalRequest) throw new Error('Request not found');

      // Check for existing pending request with same ticket ID (excluding the current request)
      const existingPendingRequest = get().requests.find(r =>
        r.ticketId === updates.ticketId &&
        r.status === 'pending' &&
        r.id !== id
      );

      if (existingPendingRequest) {
        set({ isLoading: false });
        useNotificationStore.getState().addNotification({
          title: 'Duplicate Ticket ID',
          message: `A pending request with ticket ID ${updates.ticketId} already exists. Please use a different ticket ID.`,
          type: 'error',
          recipientId: originalRequest.createdBy
        });
        throw new Error(`A pending request with ticket ID ${updates.ticketId} already exists`);
      }

      const changes: Record<string, { from: any; to: any }> = {};
      Object.entries(updates).forEach(([key, value]) => {
        if (originalRequest[key] !== value) {
          changes[key] = {
            from: originalRequest[key],
            to: value
          };
        }
      });

      const resubmittedRequest: Request = {
        ...originalRequest,
        ...updates,
        status: 'resubmitted' as RequestStatus, // Changed from 'pending' to 'resubmitted'
        rejectedBy: undefined,
        rejectedAt: undefined,
        rejectionReason: undefined,
        updatedAt: new Date(),
        resubmissionHistory: [
          ...(originalRequest.resubmissionHistory || []),
          {
            timestamp: new Date(),
            changes,
            notes: updates.comments || '',
            previousRejectionReason: originalRequest.rejectionReason || ''
          }
        ]
      };

      set((state) => ({
        requests: state.requests.map(r => r.id === id ? resubmittedRequest : r),
        isLoading: false
      }));

      // Add notifications for admins and support staff
      const users = useUserStore.getState().users;
      const notificationTargets = users.filter(u =>
        ['accounts', 'shop_support'].includes(u.role)
      );

      notificationTargets.forEach(admin => {
        useNotificationStore.getState().addNotification({
          title: 'Request Resubmitted',
          message: `${resubmittedRequest.shopName} has resubmitted their request for ${resubmittedRequest.amount}`,
          type: 'request_resubmitted',
          requestId: resubmittedRequest.id,
          recipientId: admin.id,
        });
      });

      return resubmittedRequest;
    } catch (error) {
      set({ error: 'Failed to resubmit request', isLoading: false });
      throw error;
    }
  },
  deleteRequest: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      set(state => ({
        requests: state.requests.filter(request => request.id !== id),
        isLoading: false,
      }));
    } catch (error) {
      set({ error: 'Failed to delete request', isLoading: false });
      throw error;
    }
  },
}));

type ShopState = {
  shops: Shop[];
  isLoading: boolean;
  error: string | null;
  fetchShops: () => Promise<Shop[]>;
  createShop: (shop: Omit<Shop, 'id' | 'managerId'>) => Promise<void>;
  getShopById: (id: string) => Shop | undefined;
  getShopsByArea: (areaId: string) => Shop[];
  deleteShop: (id: string) => Promise<void>;
  updateShop: (id: string, updates: Partial<Omit<Shop, 'id'>>) => Promise<void>;
};

export const useShopStore = create<ShopState>((set, get) => ({
  shops: [],
  isLoading: false,
  error: null,
  createShop: async (shopData) => {
    set({ isLoading: true, error: null });

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));

      const newShop: Shop = {
        id: Math.random().toString(36).substr(2, 9),
        managerId: '', // Default empty string for now
        ...shopData
      };

      set(state => ({
        shops: [...state.shops, newShop],
        isLoading: false
      }));
    } catch (error) {
      set({ error: 'Failed to create shop', isLoading: false });
      throw error;
    }
  },
  fetchShops: async () => {
    set({ isLoading: true, error: null });

    try {
      // Remove the condition that reloads mock data
      // Just return the current state
      const currentShops = get().shops;
      set({ isLoading: false });
      return currentShops;

    } catch (error) {
      set({ error: 'Failed to fetch shops', isLoading: false });
      throw error;
    }
  },
  getShopById: (id) => {
    return get().shops.find(s => s.id === id);
  },
  getShopsByArea: (areaId) => {
    return get().shops.filter(s => s.areaId === areaId);
  },
  deleteShop: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      set(state => ({
        shops: state.shops.filter(shop => shop.id !== id),
        isLoading: false,
      }));
    } catch (error) {
      set({ error: 'Failed to delete shop', isLoading: false });
      throw error;
    }
  },
  updateShop: async (id: string, updates: Partial<Omit<Shop, 'id'>>) => {
    set({ isLoading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 500));

      set(state => ({
        shops: state.shops.map(shop =>
          shop.id === id
            ? { ...shop, ...updates, updatedAt: new Date() }
            : shop
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ error: 'Failed to update shop', isLoading: false });
      throw error;
    }
  },
}));

type StatsState = {
  stats: DashboardStats;
  isLoading: boolean;
  error: string | null;
  fetchStats: () => Promise<DashboardStats>;
};

export const useStatsStore = create<StatsState>((set) => ({
  stats: {
    pendingRequests: 0,
    approvedRequests: 0,
    rejectedRequests: 0,
    totalAmount: 0,
  },
  isLoading: false,
  error: null,
  fetchStats: async () => {
    set({ isLoading: true, error: null });

    // Simulate API call
    return new Promise((resolve) => {
      setTimeout(() => {
        // Calculate stats based on current requests
        const requests = useRequestStore.getState().requests;
        const user = useAuthStore.getState().user;

        // Filter requests based on user role
        let filteredRequests = requests;
        if (user?.role === 'shop_manager') {
          filteredRequests = requests.filter(r => r.createdBy === user.id);
        }

        const stats: DashboardStats = {
          totalAmount: filteredRequests.reduce((sum, r) => sum + (r.status === 'approved' ? r.amount : 0), 0),
          totalRequests: filteredRequests.length,
          pendingRequests: filteredRequests.filter(r => r.status === 'pending' || r.status === 'resubmitted').length,
          approvedRequests: filteredRequests.filter(r => r.status === 'approved').length,
          rejectedRequests: filteredRequests.filter(r => r.status === 'rejected').length,
          resubmittedRequests: filteredRequests.filter(r => r.status === 'resubmitted').length,
          mobileMoneyAmount: filteredRequests.filter(r => r.status === 'approved' && r.paymentMethod === 'mobile_money').reduce((sum, r) => sum + r.amount, 0),
          bankTransferAmount: filteredRequests.filter(r => r.status === 'approved' && r.paymentMethod === 'bank_transfer').reduce((sum, r) => sum + r.amount, 0),
          uniqueAreas: new Set(filteredRequests.map(r => r.areaId)).size,
          uniqueShops: new Set(filteredRequests.map(r => r.shopId)).size,
          monthlyStats: []
        };

        set({ stats, isLoading: false });
        resolve(stats);
      }, 800);
    });
  },
}));

export const useUserStore = create<{
  users: User[];
  isLoading: boolean;
  error: string | null;
  fetchUsers: () => Promise<User[]>;
  updateUser: (id: string, updates: Partial<Omit<User, 'id'>>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  bulkInviteUsers: (emails: string[], role: UserRole) => Promise<void>;
  inviteUser: (userData: {
    name: string;
    email: string;
    role: UserRole;
    shop?: string;
    areas?: string[];
  }) => Promise<void>;
}>((set) => ({
  users: [], // Initialize with empty array instead of sample users
  isLoading: false,
  error: null,

  fetchUsers: async () => {
    set({ isLoading: true, error: null });

    try {
      // Import the getUsers function from userService
      const { getUsers } = await import('@/services/userService');

      // Fetch users from the database
      const users = await getUsers();

      // Update the state
      set({ users, isLoading: false });

      return users;
    } catch (error) {
      set({ error: 'Failed to fetch users', isLoading: false });
      throw error;
    }
  },

  updateUser: async (id: string, updates: Partial<Omit<User, 'id'>>) => {
    set({ isLoading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      set(state => ({
        users: state.users.map(user =>
          user.id === id
            ? { ...user, ...updates }
            : user
        ),
        isLoading: false,
      }));
    } catch (error) {
      set({ error: 'Failed to update user', isLoading: false });
      throw error;
    }
  },

  deleteUser: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      set(state => ({
        users: state.users.filter(user => user.id !== id),
        isLoading: false,
      }));
    } catch (error) {
      set({ error: 'Failed to delete user', isLoading: false });
      throw error;
    }
  },

  bulkInviteUsers: async (emails: string[], role: UserRole) => {
    set({ isLoading: true, error: null });

    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API call

      // Here you would typically make an API call to invite users
      // For now, we'll just simulate success

      set({ isLoading: false });
    } catch (error) {
      set({ error: 'Failed to send bulk invitations', isLoading: false });
      throw error;
    }
  },

  inviteUser: async (userData) => {
    set({ isLoading: true, error: null });

    try {
      // Call the userService.inviteUser function to store the invitation in the database
      // We don't add the user to the users array here because they haven't accepted the invitation yet
      // They will be added to the users array when they accept the invitation and create their account

      // Just update the loading state
      set({ isLoading: false });
    } catch (error) {
      set({ error: 'Failed to invite user', isLoading: false });
      throw error;
    }
  },
}));

type NotificationStore = {
  notifications: AppNotification[];
  unreadCount: number;
  addNotification: (notification: Omit<AppNotification, 'id' | 'createdAt' | 'isRead'>) => void;
  markAsRead: (notificationId: string) => void;
  markAllAsRead: () => void;
  fetchNotifications: () => Promise<void>;
};

export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: [],
  unreadCount: 0,

  addNotification: (notification) => {
    const newNotification: AppNotification = {
      id: Math.random().toString(36).substring(2, 9),
      createdAt: new Date(),
      isRead: false,
      ...notification,
    };

    set((state) => {
      const newNotifications = [newNotification, ...state.notifications];
      return {
        notifications: newNotifications,
        unreadCount: newNotifications.filter(n => !n.isRead).length,
      };
    });
  },

  markAsRead: (notificationId) => {
    set((state) => {
      const updatedNotifications = state.notifications.map(n =>
        n.id === notificationId ? { ...n, isRead: true } : n
      );
      return {
        notifications: updatedNotifications,
        unreadCount: updatedNotifications.filter(n => !n.isRead).length,
      };
    });
  },

  markAllAsRead: () => {
    set((state) => ({
      notifications: state.notifications.map(n => ({ ...n, isRead: true })),
      unreadCount: 0,
    }));
  },

  fetchNotifications: async () => {
    const user = useAuthStore.getState().user;
    if (!user) return;

    set((state) => {
      const userNotifications = state.notifications.filter(n => n.recipientId === user.id);
      return {
        notifications: userNotifications,
        unreadCount: userNotifications.filter(n => !n.isRead).length,
      };
    });
  },
}));

// Settings store has been moved to src/stores/settingsStore.ts
