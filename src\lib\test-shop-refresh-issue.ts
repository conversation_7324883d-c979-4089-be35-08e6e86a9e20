/**
 * Test to debug shop refresh issue
 */

import { authenticatedConvex } from './convex-auth';

export const testShopRefreshIssue = async () => {
  try {
    console.log('🔍 Testing shop refresh issue...');
    
    // Test 1: Get all shops (including inactive)
    console.log('\n📋 Step 1: Getting all shops (including inactive)...');
    const allShops = await authenticatedConvex.getShops(undefined, false); // isActive: false to get all
    console.log(`✅ Found ${allShops.length} total shops (including inactive)`);
    
    // Test 2: Get only active shops
    console.log('\n📋 Step 2: Getting only active shops...');
    const activeShops = await authenticatedConvex.getShops(undefined, true); // isActive: true
    console.log(`✅ Found ${activeShops.length} active shops`);
    
    // Test 3: Get shops with default parameters (should be active only)
    console.log('\n📋 Step 3: Getting shops with default parameters...');
    const defaultShops = await authenticatedConvex.getShops(); // No parameters, should default to active
    console.log(`✅ Found ${defaultShops.length} shops with default parameters`);
    
    // Test 4: Show the last few shops
    console.log('\n📋 Step 4: Last 3 active shops:');
    const lastThreeShops = activeShops.slice(-3);
    lastThreeShops.forEach((shop, index) => {
      console.log(`${index + 1}. ${shop.name} (ID: ${shop._id}) - Active: ${shop.isActive}`);
    });
    
    return {
      success: true,
      totalShops: allShops.length,
      activeShops: activeShops.length,
      defaultShops: defaultShops.length,
      lastThreeShops: lastThreeShops.map(s => ({ name: s.name, id: s._id, isActive: s.isActive })),
      message: 'Shop refresh test completed',
      analysis: {
        issue: activeShops.length !== defaultShops.length ? 
          'Default parameters not working correctly' : 
          'Parameters working correctly',
        recommendation: activeShops.length === 0 ? 
          'No active shops found - check if shops are being created with isActive: true' :
          'Active shops exist - issue might be in frontend state management'
      }
    };
    
  } catch (error) {
    console.error('❌ Shop refresh test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Shop refresh test failed'
    };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testShopRefreshIssue = testShopRefreshIssue;
}