
import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth-context';

const LoginForm: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { signIn, user, isLoading: authLoading } = useAuth();
  const [isSigningIn, setIsSigningIn] = useState(false);

  const from = (location.state as any)?.from || '/dashboard';

  // Redirect if already authenticated
  useEffect(() => {
    if (user && !authLoading) {
      navigate(from);
    }
  }, [user, authLoading, navigate, from]);

  const handleSignIn = async () => {
    setIsSigningIn(true);
    try {
      await signIn();
    } catch (error) {
      console.error('Sign in error:', error);
      setIsSigningIn(false);
    }
  };


  // Show loading state during auth initialization
  if (authLoading) {
    return (
      <Card className="w-[calc(100%-2rem)] sm:w-full max-w-[480px] mx-auto shadow-md border-gray-200 bg-white rounded-lg p-4 sm:p-6">
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-sm text-gray-500">Loading...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-[calc(100%-2rem)] sm:w-full max-w-[480px] mx-auto shadow-md border-gray-200 bg-white rounded-lg p-4 sm:p-6">
      <CardHeader className="space-y-4 sm:space-y-6 pb-4 sm:pb-6">
        {/* Logo Section with border bottom */}
        <div className="flex justify-center pb-6 sm:pb-8 border-b border-gray-100">
          <img
            src="/lovable-uploads/d809f2fb-430a-4759-9377-9438f8aacc4f.png"
            alt="MyBet Africa"
            className="h-8 sm:h-10 w-auto"
          />
        </div>

        <div className="space-y-1 sm:space-y-2 text-center">
          <h1 className="text-xl sm:text-2xl font-semibold text-gray-800">Welcome Back</h1>
          <p className="text-sm text-gray-500">
            Sign in with your MyBet Africa account
          </p>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4 sm:space-y-6">
        <div className="text-center space-y-4">
          <p className="text-sm text-gray-600">
            Secure authentication powered by WorkOS
          </p>
          
          <Button
            onClick={handleSignIn}
            disabled={isSigningIn || authLoading}
            className="w-full bg-primary hover:bg-primary-dark text-primary-foreground font-medium py-5 sm:py-6 text-sm sm:text-base"
          >
            {isSigningIn ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Redirecting to WorkOS...
              </>
            ) : (
              "Continue with WorkOS"
            )}
          </Button>
          
          <p className="text-xs text-gray-500">
            You'll be redirected to a secure authentication page
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoginForm;
