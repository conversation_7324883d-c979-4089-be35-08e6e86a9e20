import { create } from 'zustand';
import { Settings } from '@/lib/types';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { convex } from '@/lib/convex';

interface SettingsState {
  settings: Settings | null;
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchSettings: (force?: boolean) => Promise<void>;
  updateSettings: (updates: {
    momoThreshold?: number;
    bankThreshold?: number;
    whitelistedDomains?: string[];
  }) => Promise<void>;
}

// Cache duration: 5 minutes for settings (they change infrequently)
const SETTINGS_CACHE_DURATION = 5 * 60 * 1000;

// Helper function to map Convex settings to Settings type
function mapConvexSettingsToSettings(convexSettings: any): Settings {
  return {
    id: 'settings',
    momoThreshold: convexSettings.mobile_money_approval_threshold || 5000,
    bankThreshold: convexSettings.bank_transfer_approval_threshold || 10000,
    whitelistedDomains: convexSettings.whitelisted_domains || ['kmkentertainment.com', 'mybet.africa'],
    createdAt: new Date(),
    updatedAt: new Date(),
  };
}

export const useSettingsStore = create<SettingsState>((set, get) => ({
  settings: null,
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchSettings: async (force = false) => {
    // Skip store queries when using WorkOS AuthKit (use public queries instead)
    console.log('fetchSettings called - skipping for WorkOS AuthKit mode');
    return;

    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < SETTINGS_CACHE_DURATION && state.settings) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      console.log('Store: Fetching settings');

      const convexSettings = await convex.query(api.settings.getSettings, {});
      const settings = mapConvexSettingsToSettings(convexSettings);
      
      console.log('Store: Settings fetched successfully', settings);
      set({ settings, isLoading: false, lastFetch: now });
    } catch (error: any) {
      console.error('Store: Error fetching settings', error);
      set({ error: error.message || 'Failed to fetch settings', isLoading: false });
      throw error;
    }
  },

  updateSettings: async (updates) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Updating settings', updates);

      // Map frontend field names to backend field names
      const settingsUpdate = {
        mobile_money_approval_threshold: updates.momoThreshold,
        bank_transfer_approval_threshold: updates.bankThreshold,
        whitelisted_domains: updates.whitelistedDomains,
      };

      const updatedSettings = await convex.mutation(api.settings.updateSettings, {
        settings: settingsUpdate,
      });

      // Fetch fresh settings after update
      await get().fetchSettings(true);
      
      console.log('Store: Settings updated successfully', updatedSettings);
      set({ isLoading: false, lastFetch: null });
    } catch (error: any) {
      console.error('Store: Error updating settings', error);
      set({ error: error.message || 'Failed to update settings', isLoading: false });
      throw error;
    }
  }
}));

// React hooks for components using Convex real-time queries
export const useSettingsQuery = () => {
  return useQuery(api.settings.getSettings, {});
};

export const useUpdateSettingsMutation = () => {
  return useMutation(api.settings.updateSettings);
};

// Additional hooks for the new settings functions
export const useSettingsWithMetadataQuery = (category?: string, isSystem?: boolean) => {
  return useQuery(api.settings.getSettingsWithMetadata, { 
    category, 
    isSystem 
  });
};

export const useUpdateSettingMutation = () => {
  return useMutation(api.settings.updateSetting);
};

export const useInitializeDefaultSettingsMutation = () => {
  return useMutation(api.settings.initializeDefaultSettings);
};

export const useResetSettingsToDefaultMutation = () => {
  return useMutation(api.settings.resetSettingsToDefault);
};
