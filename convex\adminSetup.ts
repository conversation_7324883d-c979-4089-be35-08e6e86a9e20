import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Create admin user with accounts role (for initial setup)
export const createAdminUser = mutation({
  args: {
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Check if user is authenticated
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    // Check if this is the specified admin email
    if (args.email !== "<EMAIL>") {
      throw new Error("Only <EMAIL> can be set as admin");
    }

    const now = Date.now();

    // Check if user already exists
    let user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (!user) {
      // Create user
      const userId = await ctx.db.insert("users", {
        workosId: identity.subject,
        email: args.email,
        firstName: args.firstName || "Admin",
        lastName: args.lastName || "User",
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      user = await ctx.db.get(userId);
    }

    // Ensure accounts role exists
    let accountsRole = await ctx.db
      .query("roles")
      .withIndex("by_name", (q) => q.eq("name", "accounts"))
      .first();

    if (!accountsRole) {
      // Create accounts role with full permissions
      const roleId = await ctx.db.insert("roles", {
        name: "accounts",
        description: "Accounts Team - Full system access for financial oversight",
        permissions: [
          "request:view:all",
          "request:approve:all", 
          "request:reject",
          "shop:view",
          "shop:create",
          "shop:update", 
          "shop:delete",
          "shop:assign_manager",
          "area:view",
          "area:create",
          "area:update",
          "area:delete", 
          "user:view",
          "user:create",
          "user:update",
          "user:delete",
          "user:invite",
          "user:assign_role",
          "user:assign_area",
          "settings:view",
          "settings:update",
          "reports:view:all",
          "reports:export"
        ],
        isActive: true,
        createdAt: now,
        updatedAt: now,
      });
      accountsRole = await ctx.db.get(roleId);
    }

    // Check if user already has accounts role
    const existingRole = await ctx.db
      .query("user_roles")
      .withIndex("by_user", (q) => q.eq("userId", user!._id))
      .filter((q) => q.eq(q.field("roleId"), accountsRole!._id))
      .first();

    if (!existingRole) {
      // Assign accounts role
      await ctx.db.insert("user_roles", {
        userId: user!._id,
        roleId: accountsRole!._id,
        assignedBy: user!._id, // Self-assigned for initial setup
        assignedAt: now,
        isActive: true,
      });
    }

    return {
      success: true,
      user: user,
      role: accountsRole,
      message: "Admin user created successfully with accounts role"
    };
  },
});

// Check if admin user exists
export const checkAdminUser = query({
  args: {},
  handler: async (ctx) => {
    // <NAME_EMAIL> exists and has accounts role
    const adminUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", "<EMAIL>"))
      .first();

    if (!adminUser) {
      return {
        exists: false,
        hasAccountsRole: false,
        message: "Admin user does not exist"
      };
    }

    // Check if user has accounts role
    const accountsRole = await ctx.db
      .query("roles")
      .withIndex("by_name", (q) => q.eq("name", "accounts"))
      .first();

    if (!accountsRole) {
      return {
        exists: true,
        hasAccountsRole: false,
        message: "Admin user exists but accounts role not found"
      };
    }

    const userRole = await ctx.db
      .query("user_roles")
      .withIndex("by_user", (q) => q.eq("userId", adminUser._id))
      .filter((q) => q.eq(q.field("roleId"), accountsRole._id))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    return {
      exists: true,
      hasAccountsRole: !!userRole,
      user: adminUser,
      message: userRole ? "Admin user is properly configured" : "Admin user exists but doesn't have accounts role"
    };
  },
});