/**
 * Convex authentication wrapper for WorkOS AuthKit integration
 * 
 * This module provides wrapper functions that automatically include
 * WorkOS user authentication when calling Convex mutations and queries.
 */

import { convex } from './convex';
import { getSession } from './workos-auth';
import { api } from '../../convex/_generated/api';

/**
 * Get the current WorkOS user ID from the session
 */
export const getCurrentWorkOSUserId = async (): Promise<string | null> => {
  try {
    const session = await getSession();
    return session?.user?.id || null;
  } catch (error) {
    console.error('Error getting WorkOS user ID:', error);
    return null;
  }
};

/**
 * Wrapper for Convex mutations that automatically includes WorkOS user ID
 */
export const authenticatedMutation = async <T extends Record<string, any>>(
  mutationFunction: any,
  args: T
): Promise<any> => {
  const workosUserId = await getCurrentWorkOSUserId();
  
  if (!workosUserId) {
    throw new Error('Authentication required: No WorkOS session found');
  }
  
  // Add workosUserId to the arguments
  const authenticatedArgs = {
    ...args,
    workosUserId,
  };
  
  return await convex.mutation(mutationFunction, authenticatedArgs);
};

/**
 * Wrapper for Convex queries that automatically includes WorkOS user ID
 */
export const authenticatedQuery = async <T extends Record<string, any>>(
  queryFunction: any,
  args: T
): Promise<any> => {
  const workosUserId = await getCurrentWorkOSUserId();
  
  if (!workosUserId) {
    throw new Error('Authentication required: No WorkOS session found');
  }
  
  // Add workosUserId to the arguments
  const authenticatedArgs = {
    ...args,
    workosUserId,
  };
  
  return await convex.query(queryFunction, authenticatedArgs);
};

/**
 * Specific authenticated functions for common operations
 */
export const authenticatedConvex = {
  // Area operations
  createArea: async (name: string, description?: string) => {
    return await authenticatedMutation(api.areas.createArea, {
      name,
      description,
    });
  },
  
  getAreas: async (isActive?: boolean, limit?: number, offset?: number) => {
    return await authenticatedQuery(api.areas.getAreas, {
      isActive: isActive !== undefined ? isActive : true, // Default to active areas only
      limit,
      offset,
    });
  },
  
  getArea: async (areaId: string) => {
    return await authenticatedQuery(api.areas.getArea, {
      areaId,
    });
  },
  
  updateArea: async (areaId: string, name?: string, description?: string, isActive?: boolean) => {
    return await authenticatedMutation(api.areas.updateArea, {
      areaId,
      name,
      description,
      isActive,
    });
  },
  
  deleteArea: async (areaId: string) => {
    return await authenticatedMutation(api.areas.deleteArea, {
      areaId,
    });
  },
  
  getAreaUsers: async (areaId: string) => {
    return await authenticatedQuery(api.areas.getAreaUsers, {
      areaId,
    });
  },
  
  getAreaShops: async (areaId: string, isActive?: boolean) => {
    return await authenticatedQuery(api.areas.getAreaShops, {
      areaId,
      isActive,
    });
  },
  
  getAreaStats: async (areaId?: string, startDate?: number, endDate?: number) => {
    return await authenticatedQuery(api.areas.getAreaStats, {
      areaId,
      startDate,
      endDate,
    });
  },
  
  getAccessibleAreas: async () => {
    return await authenticatedQuery(api.areas.getAccessibleAreas, {});
  },
  
  // Shop operations
  createShop: async (name: string, code: string, areaId: string, managerId?: string, address?: string, phone?: string) => {
    return await authenticatedMutation(api.shops.createShop, {
      name,
      code,
      areaId,
      managerId,
      address,
      phone,
    });
  },
  
  getShops: async (areaId?: string, isActive?: boolean, limit?: number, offset?: number) => {
    return await authenticatedQuery(api.shops.getShops, {
      areaId,
      isActive: isActive !== undefined ? isActive : true, // Default to active shops only
      limit,
      offset,
    });
  },
  
  getShop: async (shopId: string) => {
    return await authenticatedQuery(api.shops.getShop, {
      shopId,
    });
  },
  
  updateShop: async (shopId: string, name?: string, code?: string, areaId?: string, managerId?: string, address?: string, phone?: string, isActive?: boolean) => {
    return await authenticatedMutation(api.shops.updateShop, {
      shopId,
      name,
      code,
      areaId,
      managerId,
      address,
      phone,
      isActive,
    });
  },
  
  deleteShop: async (shopId: string) => {
    return await authenticatedMutation(api.shops.deleteShop, {
      shopId,
    });
  },
  
  // User operations
  createUser: async (userData: any) => {
    return await authenticatedMutation(api.users.createUser, userData);
  },
  
  updateUser: async (userId: string, userData: any) => {
    return await authenticatedMutation(api.users.updateUser, {
      userId,
      ...userData,
    });
  },
  
  // User operations
  getUsers: async (limit?: number, offset?: number) => {
    return await authenticatedQuery(api.users.getUsers, {
      limit,
      offset,
    });
  },
  
  getUser: async (userId: string) => {
    return await authenticatedQuery(api.users.getUser, {
      userId,
    });
  },
  
  getRoles: async () => {
    return await authenticatedQuery(api.users.getRoles, {});
  },
  
  getAreas: async () => {
    return await authenticatedQuery(api.users.getAreas, {});
  },
  
  inviteUser: async (email: string, roleId: string, areaIds?: string[]) => {
    return await authenticatedMutation(api.users.inviteUser, {
      email,
      roleId,
      areaIds,
    });
  },
  
  updateUser: async (userId: string, firstName?: string, lastName?: string, isActive?: boolean) => {
    return await authenticatedMutation(api.users.updateUser, {
      userId,
      firstName,
      lastName,
      isActive,
    });
  },
  
  // Add more authenticated operations as needed
};

/**
 * Hook for React components to use authenticated Convex operations
 */
export const useAuthenticatedConvex = () => {
  return {
    authenticatedMutation,
    authenticatedQuery,
    ...authenticatedConvex,
  };
};

export default authenticatedConvex;