/**
 * WorkOS AuthKit integration
 * 
 * This module provides the WorkOS AuthKit integration for hosted authentication.
 * It handles the authentication flow, user session management, and token handling.
 */

import { createClient, type User, type AuthenticationResponse } from '@workos-inc/authkit-js';
import { WORKOS_CONFIG } from '@/config';

// Initialize WorkOS AuthKit client
let workosClient: Awaited<ReturnType<typeof createClient>> | null = null;

const getWorkOSClient = async () => {
  if (!workosClient) {
    workosClient = await createClient(WORKOS_CONFIG.CLIENT_ID, {
      apiHostname: WORKOS_CONFIG.API_HOSTNAME,
      redirectUri: WORKOS_CONFIG.REDIRECT_URI,
    });
  }
  return workosClient;
};

// WorkOS User interface (extending the package's User type)
export interface WorkOSUser extends User {}

// WorkOS Session interface
export interface WorkOSSession {
  user: WorkOSUser;
  accessToken: string;
  refreshToken: string;
  organizationId?: string;
  role?: string;
  permissions?: string[];
}

/**
 * Get the current authenticated user session
 */
export const getSession = async (): Promise<WorkOSSession | null> => {
  try {
    const client = await getWorkOSClient();
    const user = client.getUser();
    
    if (!user) {
      return null;
    }

    const accessToken = await client.getAccessToken();

    return {
      user,
      accessToken,
      refreshToken: '', // AuthKit handles refresh automatically
      organizationId: undefined, // Would need to parse from access token if needed
      role: undefined, // Would need to parse from access token if needed
      permissions: [], // Would need to parse from access token if needed
    };
  } catch (error) {
    console.error('Error getting WorkOS session:', error);
    return null;
  }
};

/**
 * Sign in with WorkOS hosted authentication
 */
export const signIn = async (options?: {
  state?: any;
  invitationToken?: string;
  passwordResetToken?: string;
}): Promise<void> => {
  try {
    const client = await getWorkOSClient();
    await client.signIn(options);
  } catch (error) {
    console.error('Error signing in with WorkOS:', error);
    throw error;
  }
};

/**
 * Sign up with WorkOS hosted authentication
 */
export const signUp = async (options?: {
  state?: any;
  invitationToken?: string;
  passwordResetToken?: string;
}): Promise<void> => {
  try {
    const client = await getWorkOSClient();
    await client.signUp(options);
  } catch (error) {
    console.error('Error signing up with WorkOS:', error);
    throw error;
  }
};

/**
 * Sign out the current user
 */
export const signOut = async (): Promise<void> => {
  try {
    const client = await getWorkOSClient();
    client.signOut();
  } catch (error) {
    console.error('Error signing out with WorkOS:', error);
    throw error;
  }
};

/**
 * Handle the authentication callback
 */
export const handleAuthCallback = async (): Promise<WorkOSSession | null> => {
  try {
    // WorkOS AuthKit automatically handles the callback
    // We just need to get the session after the redirect
    return await getSession();
  } catch (error) {
    console.error('Error handling auth callback:', error);
    throw error;
  }
};

/**
 * Check if user is authenticated
 */
export const isAuthenticated = async (): Promise<boolean> => {
  try {
    const session = await getSession();
    return !!session;
  } catch (error) {
    console.error('Error checking authentication status:', error);
    return false;
  }
};

/**
 * Get the access token for API calls
 */
export const getAccessToken = async (): Promise<string | null> => {
  try {
    const client = await getWorkOSClient();
    return await client.getAccessToken();
  } catch (error) {
    console.error('Error getting access token:', error);
    return null;
  }
};

/**
 * Refresh the user session
 */
export const refreshSession = async (): Promise<WorkOSSession | null> => {
  try {
    // WorkOS AuthKit handles token refresh automatically
    return await getSession();
  } catch (error) {
    console.error('Error refreshing session:', error);
    return null;
  }
};

export default getWorkOSClient;