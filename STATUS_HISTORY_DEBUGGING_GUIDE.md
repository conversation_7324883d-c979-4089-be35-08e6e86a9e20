# Status History Debugging Guide

## Current Status ✅
- ✅ **Migration Complete**: 5 requests with 13 status entries
- ✅ **Timeline Code Fixed**: Removed hardcoded `false` condition
- ✅ **Debug Logging Added**: Console logs to track data flow
- ✅ **Query Structure**: Both basic and enhanced queries available

## Root Cause Analysis

The status history timeline is not showing because the **enhanced query** (`getRequestWithHistory`) is likely not being called or not returning data due to:

1. **Authentication Issues**: Enhanced query requires proper WorkOS authentication
2. **Permission Issues**: User may not have required permissions
3. **Query Conditions**: Enhanced query has strict conditions to be called
4. **Fallback Logic**: Always falling back to basic query without status history

## Debugging Steps

### Step 1: Check Browser Console
When you view a request details page, look for these debug logs:

```
=== REQUEST DATA DEBUG ===
Request ID: [some-id]
Request source: Enhanced (with history) OR Basic (fallback)
Status History exists: true/false
Status History length: [number]
Status History data: [array or undefined]
========================
```

### Step 2: Identify the Issue

#### If you see "Basic (fallback)":
- The enhanced query is not being called
- Check authentication state
- Check if user has proper permissions

#### If you see "Enhanced (with history)" but "Status History length: 0":
- Enhanced query is working but no status history found
- Check if the request has status history in database

#### If you see "Enhanced (with history)" and "Status History length: > 0":
- Everything is working correctly
- Timeline should display

## Common Issues & Solutions

### Issue 1: Enhanced Query Not Called
**Symptoms**: Always shows "Basic (fallback)"

**Causes**:
- `workosUser?.id` is undefined
- `convexRequests` doesn't contain the request
- Authentication not properly established

**Solution**: Check authentication flow in browser console

### Issue 2: Authentication Errors
**Symptoms**: Enhanced query fails with auth errors

**Causes**:
- WorkOS authentication not working
- User doesn't have required permissions
- `workosUserId` parameter not passed correctly

**Solution**: Verify WorkOS auth state and user permissions

### Issue 3: No Status History Data
**Symptoms**: Enhanced query works but no status history

**Causes**:
- Request wasn't migrated properly
- Status history not created for new requests
- Database query issues

**Solution**: Check migration status and create new test request

## Quick Fixes to Try

### Fix 1: Force Enhanced Query (Temporary)
If you want to test if the enhanced query works, temporarily modify the condition:

```tsx
// In RequestDetails.tsx, change this:
(workosUser?.id && id && convexRequests?.some(r => r._id === id)) 

// To this (temporarily):
(workosUser?.id && id)
```

### Fix 2: Add More Debug Logging
Add this to see query states:

```tsx
console.log('Enhanced query conditions:');
console.log('- workosUser?.id:', !!workosUser?.id);
console.log('- id:', !!id);
console.log('- convexRequests:', convexRequests?.length || 0);
console.log('- request found in convexRequests:', convexRequests?.some(r => r._id === id));
```

### Fix 3: Test with New Request
Create a brand new request to see if status history is recorded for new requests.

## Expected Timeline Display

Once working, you should see:
1. **Request Created** - [User] - [Timestamp]
2. **Rejected** - [Admin] - [Timestamp] - "[Rejection reason]"
3. **Resubmitted** - [User] - [Timestamp] - "[Change notes]"
4. **Approved** - [Admin] - [Timestamp] - "[Approval notes]"

## Next Steps

1. **Check Console Logs**: Look for the debug output when viewing a request
2. **Identify Pattern**: Determine if it's always "Basic" or sometimes "Enhanced"
3. **Test Authentication**: Verify WorkOS auth is working properly
4. **Create New Request**: Test with a fresh request to see real-time tracking
5. **Report Findings**: Share what you see in the console logs

The status history system is fully implemented and migrated - we just need to identify why the enhanced query isn't being used consistently.