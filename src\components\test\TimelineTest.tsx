import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Check, X, User, ClipboardPen } from 'lucide-react';
import { format, formatDistanceToNow } from 'date-fns';

// Test component to showcase the improved timeline
const TimelineTest: React.FC = () => {
  // Mock request data for testing different timeline states
  const approvedRequest = {
    id: 'test-1',
    createdByName: '<PERSON>',
    createdAt: new Date('2024-02-01T10:00:00'),
    approvedByName: 'Admin User',
    approvedAt: new Date('2024-02-02T14:30:00'),
    approvalReason: 'Approved for weekly operations',
    status: 'approved' as const
  };

  const rejectedRequest = {
    id: 'test-2',
    createdByName: '<PERSON>',
    createdAt: new Date('2024-02-10T09:15:00'),
    rejectedByName: 'Admin User',
    rejectedAt: new Date('2024-02-11T16:45:00'),
    rejectionReason: 'Budget exceeded for this month',
    status: 'rejected' as const
  };

  const resubmittedRequest = {
    id: 'test-3',
    createdByName: 'Bob Johnson',
    createdAt: new Date('2024-02-15T11:20:00'),
    status: 'resubmitted' as const,
    resubmissionHistory: [
      {
        timestamp: new Date('2024-02-18T13:45:00'),
        notes: 'Reduced amount as requested and updated documentation',
        changes: { amount: { from: 20000, to: 15000 } },
        previousRejectionReason: 'Amount too high'
      }
    ]
  };

  const renderTimeline = (request: any, title: string) => (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-1">
          <div className="relative">
            {/* Creation Event */}
            <div className="relative flex items-start space-x-4 pb-8">
              <div className="flex-shrink-0 relative z-10">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-lg ring-4 ring-white">
                  <User className="w-5 h-5 text-white" />
                </div>
              </div>
              <div className="flex-1 min-w-0 pt-1">
                <div className="flex items-start justify-between mb-2">
                  <div>
                    <h3 className="text-base font-semibold text-gray-900 mb-1">Request Created</h3>
                    <div className="flex items-center space-x-2 text-sm text-gray-600">
                      <span>Created by</span>
                      <span className="font-medium text-gray-900 bg-gray-100 px-2 py-0.5 rounded-md">
                        {request.createdByName}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <time className="text-sm font-medium text-gray-900 block">
                      {format(new Date(request.createdAt), 'MMM dd, yyyy')}
                    </time>
                    <time className="text-xs text-gray-500">
                      {format(new Date(request.createdAt), 'HH:mm')}
                    </time>
                  </div>
                </div>
                <p className="text-xs text-gray-500 font-medium">
                  {formatDistanceToNow(new Date(request.createdAt), { addSuffix: true })}
                </p>
              </div>
            </div>

            {/* Approval Event */}
            {request.status === 'approved' && request.approvedByName && request.approvedAt && (
              <div className="relative flex items-start space-x-4 pb-8">
                <div className="flex-shrink-0 relative z-10">
                  <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center shadow-lg ring-4 ring-white">
                    <Check className="w-5 h-5 text-white" />
                  </div>
                </div>
                <div className="flex-1 min-w-0 pt-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="text-base font-semibold text-gray-900 mb-1">Request Approved</h3>
                      <div className="flex items-center space-x-2 text-sm text-gray-600">
                        <span>Approved by</span>
                        <span className="font-medium text-gray-900 bg-emerald-100 px-2 py-0.5 rounded-md">
                          {request.approvedByName}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <time className="text-sm font-medium text-gray-900 block">
                        {format(new Date(request.approvedAt), 'MMM dd, yyyy')}
                      </time>
                      <time className="text-xs text-gray-500">
                        {format(new Date(request.approvedAt), 'HH:mm')}
                      </time>
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 font-medium mb-3">
                    {formatDistanceToNow(new Date(request.approvedAt), { addSuffix: true })}
                  </p>
                  {request.approvalReason && (
                    <div className="bg-gradient-to-r from-emerald-50 to-emerald-100 border border-emerald-200 rounded-lg p-3">
                      <div className="flex items-start space-x-2">
                        <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="text-xs font-medium text-emerald-800 mb-1">Approval Reason</p>
                          <p className="text-sm text-emerald-700">{request.approvalReason}</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Rejection Event */}
            {request.status === 'rejected' && request.rejectedByName && request.rejectedAt && (
              <div className="flex items-start space-x-4 pb-6">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                    <X className="w-4 h-4 text-red-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h3 className="text-sm font-medium text-gray-900">Request Rejected</h3>
                    <time className="text-xs text-muted-foreground">
                      {format(new Date(request.rejectedAt), 'MMM dd, yyyy HH:mm')}
                    </time>
                  </div>
                  <p className="text-sm text-muted-foreground mt-1">
                    Rejected by <span className="font-medium text-gray-900">{request.rejectedByName}</span>
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    {formatDistanceToNow(new Date(request.rejectedAt), { addSuffix: true })}
                  </p>
                  {request.rejectionReason && (
                    <div className="mt-2 p-2 bg-red-50 rounded-md">
                      <p className="text-xs text-red-800">
                        <span className="font-medium">Reason:</span> {request.rejectionReason}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Resubmission Events */}
            {request.resubmissionHistory && request.resubmissionHistory.length > 0 && (
              <>
                {request.resubmissionHistory.map((resubmission: any, index: number) => (
                  <div key={index} className="flex items-start space-x-4 pb-6">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                        <ClipboardPen className="w-4 h-4 text-orange-600" />
                      </div>
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <h3 className="text-sm font-medium text-gray-900">Request Resubmitted</h3>
                        <time className="text-xs text-muted-foreground">
                          {format(new Date(resubmission.timestamp), 'MMM dd, yyyy HH:mm')}
                        </time>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">
                        Resubmitted by <span className="font-medium text-gray-900">{request.createdByName}</span>
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {formatDistanceToNow(new Date(resubmission.timestamp), { addSuffix: true })}
                      </p>
                      {resubmission.notes && (
                        <div className="mt-2 p-2 bg-orange-50 rounded-md">
                          <p className="text-xs text-orange-800">
                            <span className="font-medium">Notes:</span> {resubmission.notes}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </>
            )}

            {/* Timeline line - only show if there are multiple events */}
            {((request.status === 'approved' && request.approvedByName) ||
              (request.status === 'rejected' && request.rejectedByName) ||
              (request.resubmissionHistory && request.resubmissionHistory.length > 0)) && (
              <div className="absolute left-4 top-8 bottom-6 w-0.5 bg-gray-200"></div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container max-w-4xl py-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold">Timeline Test - Improved Request Details</h1>
        <p className="text-muted-foreground">Testing the improved timeline with actual names and timestamps</p>
      </div>

      {renderTimeline(approvedRequest, "Approved Request Timeline")}
      {renderTimeline(rejectedRequest, "Rejected Request Timeline")}
      {renderTimeline(resubmittedRequest, "Resubmitted Request Timeline")}
    </div>
  );
};

export default TimelineTest;
