/**
 * Test user invitation flow
 */

import { authenticatedConvex } from './convex-auth';

export const testUserInvitation = async () => {
  try {
    console.log('🔍 Testing user invitation flow...');
    
    const testEmail = `test.invitation.${Date.now()}@example.com`;
    const results = [];
    
    // Test 1: Get available roles
    console.log('\n📋 Step 1: Getting available roles...');
    const roles = await authenticatedConvex.getRoles();
    console.log(`✅ Found ${roles.length} roles:`, roles.map(r => r.name));
    results.push({ operation: 'getRoles', success: roles.length > 0, count: roles.length });
    
    // Test 2: Get available areas
    console.log('\n📋 Step 2: Getting available areas...');
    const areas = await authenticatedConvex.getAreas();
    console.log(`✅ Found ${areas.length} areas:`, areas.map(a => a.name));
    results.push({ operation: 'getAreas', success: areas.length > 0, count: areas.length });
    
    // Test 3: Invite a user
    console.log('\n📋 Step 3: Inviting user...');
    const accountsRole = roles.find(r => r.name === 'accounts');
    if (!accountsRole) {
      throw new Error('Accounts role not found');
    }
    
    const invitationResult = await authenticatedConvex.inviteUser(
      testEmail,
      accountsRole._id,
      areas.length > 0 ? [areas[0]._id] : undefined
    );
    
    console.log('✅ User invited successfully:', invitationResult);
    results.push({ 
      operation: 'inviteUser', 
      success: !!invitationResult, 
      invitationId: invitationResult,
      email: testEmail,
      role: accountsRole.name
    });
    
    // Test 4: Verify invitation was created
    console.log('\n📋 Step 4: Verifying invitation...');
    // We would need to add a getInvitations function to verify this
    
    // Summary
    const successCount = results.filter(r => r.success).length;
    const totalTests = results.length;
    
    console.log(`\n=== Test Summary ===`);
    console.log(`✅ ${successCount}/${totalTests} operations successful`);
    
    return {
      success: successCount === totalTests,
      totalTests,
      successCount,
      results,
      testEmail,
      message: successCount === totalTests ? 
        '🎉 User invitation flow working perfectly!' : 
        `⚠️ ${totalTests - successCount} operation(s) failed`
    };
    
  } catch (error) {
    console.error('❌ User invitation test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'User invitation test failed'
    };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testUserInvitation = testUserInvitation;
}