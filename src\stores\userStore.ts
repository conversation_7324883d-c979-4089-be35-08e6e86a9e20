import { create } from 'zustand';
import { User, UserRole } from '@/lib/types';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { convex } from '@/lib/convex';
import { authenticatedConvex } from '@/lib/convex-auth';
import { useWorkOSUserId } from '@/lib/authenticated-hooks';

interface UserState {
  users: User[];
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchUsers: (force?: boolean) => Promise<User[]>;
  updateUser: (id: string, updates: Partial<Omit<User, 'id'>>) => Promise<void>;
  deleteUser: (id: string) => Promise<void>;
  bulkInviteUsers: (emails: string[], role: UserRole) => Promise<void>;
  inviteUser: (userData: {
    name: string;
    email: string;
    role: UserRole;
    shop?: string;
    areas?: string[];
  }) => Promise<void>;
  assignRole: (userId: string, role: UserRole) => Promise<void>;
  removeRole: (userId: string, role: UserRole) => Promise<void>;
  assignArea: (userId: string, areaId: string) => Promise<void>;
  removeArea: (userId: string, areaId: string) => Promise<void>;
}

// Cache duration: 5 minutes
const CACHE_DURATION = 5 * 60 * 1000;

// Helper function to map Convex user to User type
function mapConvexUserToUser(convexUser: any): User {
  const fullName = [convexUser.firstName, convexUser.lastName]
    .filter(Boolean)
    .join(' ') || convexUser.email;

  return {
    id: convexUser._id,
    name: fullName,
    email: convexUser.email,
    role: convexUser.roles?.[0]?.name as UserRole || 'watcher',
    shop: undefined, // Not directly mapped in Convex schema
    areas: convexUser.areas?.map((area: any) => area.name) || [],
    avatar: convexUser.profilePicture,
    phone: undefined, // Not in Convex schema
  };
}

export const useUserStore = create<UserState>((set, get) => ({
  users: [],
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchUsers: async (force = false) => {
    // Skip store queries when using WorkOS AuthKit (use public queries instead)
    console.log('fetchUsers called - skipping for WorkOS AuthKit mode');
    return [];

    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < CACHE_DURATION && state.users.length > 0) {
      return state.users;
    }

    set({ isLoading: true, error: null });

    try {
      const convexUsers = await convex.query(api.users.getUsers, {});
      const users = convexUsers.map(mapConvexUserToUser);
      set({ users, isLoading: false, lastFetch: now });
      return users;
    } catch (error: any) {
      console.error('Error fetching users:', error);
      set({ error: error.message || 'Failed to fetch users', isLoading: false });
      throw error;
    }
  },

  updateUser: async (id: string, updates: Partial<Omit<User, 'id'>>) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Updating user', id, 'with updates', updates);

      await convex.mutation(api.users.updateUser, {
        userId: id as Id<"users">,
        firstName: updates.name?.split(' ')[0],
        lastName: updates.name?.split(' ').slice(1).join(' '),
        isActive: true, // Keep user active unless specifically deactivating
      });

      // Fetch updated user to get full data
      const updatedUser = await convex.query(api.users.getUser, { 
        userId: id as Id<"users"> 
      });

      if (!updatedUser) {
        throw new Error('Failed to retrieve updated user');
      }

      const mappedUser = mapConvexUserToUser(updatedUser);

      console.log('Store: User updated successfully', mappedUser);

      set(state => ({
        users: state.users.map(user =>
          user.id === id ? mappedUser : user
        ),
        isLoading: false,
        lastFetch: null, // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error updating user', error);
      set({ error: error.message || 'Failed to update user', isLoading: false });
      throw error;
    }
  },

  deleteUser: async (id: string) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Deleting user', id);

      await convex.mutation(api.users.updateUser, {
        userId: id as Id<"users">,
        isActive: false,
      });

      console.log('Store: User deleted successfully');

      set(state => ({
        users: state.users.filter(user => user.id !== id),
        isLoading: false,
        lastFetch: null, // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error deleting user', error);
      set({ error: error.message || 'Failed to delete user', isLoading: false });
      throw error;
    }
  },

  bulkInviteUsers: async (emails: string[], role: UserRole) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Bulk inviting users', emails, 'with role', role);

      // Get role ID
      const roles = await convex.query(api.users.getRoles, {});
      const roleObj = roles.find(r => r.name === role);
      
      if (!roleObj) {
        throw new Error(`Role ${role} not found`);
      }

      // Invite each user
      const invitePromises = emails.map(email => 
        convex.mutation(api.users.inviteUser, {
          email,
          roleId: roleObj._id,
        })
      );

      await Promise.all(invitePromises);

      console.log('Store: Bulk invitations sent successfully');

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Store: Error sending bulk invitations', error);
      set({ error: error.message || 'Failed to send bulk invitations', isLoading: false });
      throw error;
    }
  },

  inviteUser: async (userData) => {
    set({ isLoading: true, error: null });
    console.log('userStore.inviteUser called with:', userData);

    try {
      // Get role ID using authenticated wrapper
      const roles = await authenticatedConvex.getRoles();
      const role = roles.find(r => r.name === userData.role);
      
      if (!role) {
        throw new Error(`Role ${userData.role} not found`);
      }

      // Get area IDs if specified using authenticated wrapper
      let areaIds: Id<"areas">[] | undefined;
      if (userData.areas && userData.areas.length > 0) {
        const areas = await authenticatedConvex.getAreas();
        areaIds = userData.areas
          .map(areaName => areas.find(a => a.name === areaName)?._id)
          .filter(Boolean) as Id<"areas">[];
      }

      console.log('Calling authenticated inviteUser mutation...');
      const result = await authenticatedConvex.inviteUser(
        userData.email,
        role._id,
        areaIds
      );

      console.log('Convex inviteUser completed successfully', result);

      // In a real implementation, you would integrate with WorkOS here
      // For now, we'll show instructions for manual invitation
      console.log(`User invitation created for ${userData.email}. Invitation token:`, result.token);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Error in userStore.inviteUser:', error);
      set({ error: error.message || 'Failed to invite user', isLoading: false });
      throw error;
    }
  },

  assignRole: async (userId: string, role: UserRole) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Assigning role', role, 'to user', userId);

      // Get role ID
      const roles = await convex.query(api.users.getRoles, {});
      const roleObj = roles.find(r => r.name === role);
      
      if (!roleObj) {
        throw new Error(`Role ${role} not found`);
      }

      await convex.mutation(api.users.assignRole, {
        userId: userId as Id<"users">,
        roleId: roleObj._id,
      });

      console.log('Store: Role assigned successfully');

      // Refresh users to get updated data
      await get().fetchUsers(true);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Store: Error assigning role', error);
      set({ error: error.message || 'Failed to assign role', isLoading: false });
      throw error;
    }
  },

  removeRole: async (userId: string, role: UserRole) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Removing role', role, 'from user', userId);

      // Get role ID
      const roles = await convex.query(api.users.getRoles, {});
      const roleObj = roles.find(r => r.name === role);
      
      if (!roleObj) {
        throw new Error(`Role ${role} not found`);
      }

      await convex.mutation(api.users.removeRole, {
        userId: userId as Id<"users">,
        roleId: roleObj._id,
      });

      console.log('Store: Role removed successfully');

      // Refresh users to get updated data
      await get().fetchUsers(true);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Store: Error removing role', error);
      set({ error: error.message || 'Failed to remove role', isLoading: false });
      throw error;
    }
  },

  assignArea: async (userId: string, areaId: string) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Assigning area', areaId, 'to user', userId);

      await convex.mutation(api.users.assignArea, {
        userId: userId as Id<"users">,
        areaId: areaId as Id<"areas">,
      });

      console.log('Store: Area assigned successfully');

      // Refresh users to get updated data
      await get().fetchUsers(true);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Store: Error assigning area', error);
      set({ error: error.message || 'Failed to assign area', isLoading: false });
      throw error;
    }
  },

  removeArea: async (userId: string, areaId: string) => {
    set({ isLoading: true, error: null });

    try {
      console.log('Store: Removing area', areaId, 'from user', userId);

      await convex.mutation(api.users.removeArea, {
        userId: userId as Id<"users">,
        areaId: areaId as Id<"areas">,
      });

      console.log('Store: Area removed successfully');

      // Refresh users to get updated data
      await get().fetchUsers(true);

      set({ isLoading: false });
    } catch (error: any) {
      console.error('Store: Error removing area', error);
      set({ error: error.message || 'Failed to remove area', isLoading: false });
      throw error;
    }
  },
}));

// React hooks for components using Convex real-time queries
export const useUsersQuery = () => {
  return useQuery(api.users.getUsers, {});
};

export const useUserQuery = (userId: string) => {
  return useQuery(api.users.getUser, { 
    userId: userId as Id<"users"> 
  });
};

export const useCurrentUserQuery = () => {
  return useQuery(api.auth.getCurrentUser, {});
};

export const useSessionInfoQuery = () => {
  return useQuery(api.auth.getSessionInfo, {});
};

export const useRolesQuery = () => {
  const { workosUserId, isLoading: userLoading } = useWorkOSUserId();
  
  const query = useQuery(
    api.users.getRoles, 
    workosUserId ? { workosUserId } : "skip"
  );
  
  // Return the query result directly (which is the roles array)
  // Don't spread it since it's already an array
  return query;
};

export const useUpdateUserMutation = () => {
  return useMutation(api.users.updateUser);
};

export const useInviteUserMutation = () => {
  return useMutation(api.users.inviteUser);
};

export const useDeleteUserMutation = () => {
  return useMutation(api.users.deleteUser);
};

export const useResendInvitationMutation = () => {
  return useMutation(api.users.resendInvitation);
};

export const useCancelInvitationMutation = () => {
  return useMutation(api.users.cancelInvitation);
};

export const useAssignRoleMutation = () => {
  return useMutation(api.users.assignRole);
};

export const useRemoveRoleMutation = () => {
  return useMutation(api.users.removeRole);
};

export const useAssignAreaMutation = () => {
  return useMutation(api.users.assignArea);
};

export const useRemoveAreaMutation = () => {
  return useMutation(api.users.removeArea);
};

// New hooks for invitations and combined data
export const useInvitationsQuery = () => {
  return useQuery(api.users.getInvitations, {});
};

export const useUsersAndInvitationsQuery = () => {
  const { workosUserId, isLoading: userLoading } = useWorkOSUserId();
  
  const query = useQuery(
    api.users.getUsersAndInvitations, 
    workosUserId ? { workosUserId } : "skip"
  );
  
  return {
    ...query,
    isLoading: userLoading || query === undefined,
  };
};
