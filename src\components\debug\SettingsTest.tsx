import React from 'react';
import { useSettings } from '@/hooks/useSettings';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2 } from 'lucide-react';

/**
 * Debug component to test settings integration
 * This component can be temporarily added to verify that settings are working correctly
 */
export const SettingsTest: React.FC = () => {
  const {
    isLoading,
    mobileMoneyThreshold,
    bankTransferThreshold,
    maxTicketImageSize,
    whitelistedDomains,
    settings,
    canApproveAmount,
    isValidFileSize,
    isWhitelistedDomain
  } = useSettings();

  if (isLoading) {
    return (
      <Card className="w-full max-w-2xl">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading settings...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          🔧 Settings Integration Test
          <Badge variant="outline">
            {settings ? 'Connected' : 'Using Fallbacks'}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Threshold Settings */}
        <div>
          <h3 className="font-semibold mb-2">Approval Thresholds</h3>
          <div className="grid grid-cols-2 gap-4">
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-sm text-muted-foreground">Mobile Money</div>
              <div className="text-lg font-semibold">¢{mobileMoneyThreshold.toLocaleString()}</div>
            </div>
            <div className="p-3 bg-muted rounded-lg">
              <div className="text-sm text-muted-foreground">Bank Transfer</div>
              <div className="text-lg font-semibold">¢{bankTransferThreshold.toLocaleString()}</div>
            </div>
          </div>
        </div>

        {/* File Size Settings */}
        <div>
          <h3 className="font-semibold mb-2">File Upload Limits</h3>
          <div className="p-3 bg-muted rounded-lg">
            <div className="text-sm text-muted-foreground">Max Ticket Image Size</div>
            <div className="text-lg font-semibold">
              {(maxTicketImageSize / (1024 * 1024)).toFixed(1)} MB
            </div>
          </div>
        </div>

        {/* Whitelisted Domains */}
        <div>
          <h3 className="font-semibold mb-2">Whitelisted Domains</h3>
          <div className="flex flex-wrap gap-2">
            {whitelistedDomains.map((domain, index) => (
              <Badge key={index} variant="secondary">
                {domain}
              </Badge>
            ))}
          </div>
        </div>

        {/* Test Functions */}
        <div>
          <h3 className="font-semibold mb-2">Function Tests</h3>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span>Can shop_support approve ¢3,000 mobile money?</span>
              <Badge variant={canApproveAmount('mobile_money', 3000, ['shop_support']) ? 'default' : 'destructive'}>
                {canApproveAmount('mobile_money', 3000, ['shop_support']) ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Can shop_support approve ¢7,000 mobile money?</span>
              <Badge variant={canApproveAmount('mobile_money', 7000, ['shop_support']) ? 'default' : 'destructive'}>
                {canApproveAmount('mobile_money', 7000, ['shop_support']) ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Is 3MB ticket image valid?</span>
              <Badge variant={isValidFileSize(3 * 1024 * 1024, 'ticket') ? 'default' : 'destructive'}>
                {isValidFileSize(3 * 1024 * 1024, 'ticket') ? 'Yes' : 'No'}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span>Is <EMAIL> whitelisted?</span>
              <Badge variant={isWhitelistedDomain('<EMAIL>') ? 'default' : 'destructive'}>
                {isWhitelistedDomain('<EMAIL>') ? 'Yes' : 'No'}
              </Badge>
            </div>
          </div>
        </div>

        {/* Raw Settings Data */}
        <div>
          <h3 className="font-semibold mb-2">Raw Settings Data</h3>
          <pre className="text-xs bg-muted p-3 rounded-lg overflow-auto max-h-40">
            {JSON.stringify(settings, null, 2)}
          </pre>
        </div>
      </CardContent>
    </Card>
  );
};
