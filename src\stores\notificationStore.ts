import { create } from 'zustand';
import { AppNotification } from '@/lib/types';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { convex } from '@/lib/convex';

interface NotificationState {
  notifications: AppNotification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  lastFetch: number | null;
  fetchNotifications: (force?: boolean) => Promise<void>;
  fetchUnreadCount: () => Promise<void>;
  markAsRead: (id: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  createNotification: (notification: {
    title: string;
    message: string;
    type: string;
    recipientId: string;
    requestId?: string;
  }) => Promise<AppNotification>;
  deleteNotification: (id: string) => Promise<void>;
}

// Cache duration: 30 seconds for notifications (they change frequently)
const NOTIFICATION_CACHE_DURATION = 30 * 1000;

// Helper function to map Convex notification to AppNotification type
function mapConvexNotificationToNotification(convexNotification: any): AppNotification {
  return {
    id: convexNotification._id,
    title: convexNotification.title,
    message: convexNotification.message,
    type: convexNotification.type,
    recipientId: convexNotification.userId,
    requestId: convexNotification.relatedEntityType === 'request' ? convexNotification.relatedEntityId : undefined,
    createdAt: new Date(convexNotification.createdAt),
    isRead: convexNotification.isRead,
  };
}

export const useNotificationStore = create<NotificationState>((set, get) => ({
  notifications: [],
  unreadCount: 0,
  isLoading: false,
  error: null,
  lastFetch: null,

  fetchNotifications: async (force = false) => {
    const state = get();
    const now = Date.now();

    // Use cache if data is fresh and not forced
    if (!force && state.lastFetch && (now - state.lastFetch) < NOTIFICATION_CACHE_DURATION) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      console.log('Store: Fetching notifications');

      const convexNotifications = await convex.query(api.notifications.getUserNotifications, {});
      const notifications = convexNotifications.map(mapConvexNotificationToNotification);
      const unreadCount = notifications.filter(n => !n.isRead).length;

      console.log('Store: Notifications fetched successfully', notifications.length, 'total,', unreadCount, 'unread');

      set({ 
        notifications, 
        unreadCount,
        isLoading: false, 
        lastFetch: now 
      });
    } catch (error: any) {
      console.error('Store: Error fetching notifications', error);
      set({ error: error.message || 'Failed to fetch notifications', isLoading: false });
    }
  },

  fetchUnreadCount: async () => {
    try {
      console.log('Store: Fetching unread count');

      const count = await convex.query(api.notifications.getUnreadCount, {});

      console.log('Store: Unread count fetched successfully', count);

      set({ unreadCount: count });
    } catch (error: any) {
      console.error('Store: Error fetching unread count', error);
      // Don't set error state for this operation as it's not critical
    }
  },

  markAsRead: async (id) => {
    try {
      console.log('Store: Marking notification as read', id);

      await convex.mutation(api.notifications.markAsRead, {
        notificationId: id as Id<"notifications">,
      });

      console.log('Store: Notification marked as read successfully');

      set(state => ({
        notifications: state.notifications.map(notification =>
          notification.id === id
            ? { ...notification, isRead: true }
            : notification
        ),
        unreadCount: Math.max(0, state.unreadCount - 1),
        lastFetch: null // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error marking notification as read', error);
      set({ error: error.message || 'Failed to mark notification as read' });
      throw error;
    }
  },

  markAllAsRead: async () => {
    try {
      console.log('Store: Marking all notifications as read');

      await convex.mutation(api.notifications.markAllAsRead, {});

      console.log('Store: All notifications marked as read successfully');

      set(state => ({
        notifications: state.notifications.map(notification => ({ ...notification, isRead: true })),
        unreadCount: 0,
        lastFetch: null // Invalidate cache
      }));
    } catch (error: any) {
      console.error('Store: Error marking all notifications as read', error);
      set({ error: error.message || 'Failed to mark all notifications as read' });
      throw error;
    }
  },

  createNotification: async (notification) => {
    try {
      console.log('Store: Creating notification', notification);

      // Use system notification creation for admin-created notifications
      const notificationIds = await convex.mutation(api.notifications.createSystemNotification, {
        title: notification.title,
        message: notification.message,
        targetUserIds: [notification.recipientId as Id<"users">],
      });

      if (notificationIds.length === 0) {
        throw new Error('Failed to create notification');
      }

      // Fetch the created notification to get full data
      const createdNotification = await convex.query(api.notifications.getNotification, {
        notificationId: notificationIds[0],
      });

      if (!createdNotification) {
        throw new Error('Failed to retrieve created notification');
      }

      const newNotification = mapConvexNotificationToNotification(createdNotification);

      console.log('Store: Notification created successfully', newNotification);

      // Add to state if it's for the current user
      set(state => ({
        notifications: [newNotification, ...state.notifications],
        unreadCount: state.unreadCount + 1,
        lastFetch: null // Invalidate cache
      }));

      return newNotification;
    } catch (error: any) {
      console.error('Store: Error creating notification', error);
      set({ error: error.message || 'Failed to create notification' });
      throw error;
    }
  },

  deleteNotification: async (id) => {
    try {
      console.log('Store: Deleting notification', id);

      await convex.mutation(api.notifications.deleteNotification, {
        notificationId: id as Id<"notifications">,
      });

      console.log('Store: Notification deleted successfully');

      set(state => {
        const deletedNotification = state.notifications.find(n => n.id === id);
        const wasUnread = deletedNotification && !deletedNotification.isRead;

        return {
          notifications: state.notifications.filter(notification => notification.id !== id),
          unreadCount: wasUnread ? Math.max(0, state.unreadCount - 1) : state.unreadCount,
          lastFetch: null // Invalidate cache
        };
      });
    } catch (error: any) {
      console.error('Store: Error deleting notification', error);
      set({ error: error.message || 'Failed to delete notification' });
      throw error;
    }
  }
}));

// React hooks for components using Convex real-time queries
export const useNotificationsQuery = (limit?: number) => {
  return useQuery(api.notifications.getUserNotifications, { limit });
};

export const useUnreadNotificationsCountQuery = () => {
  return useQuery(api.notifications.getUnreadCount, {});
};

export const useNotificationQuery = (notificationId: string) => {
  return useQuery(api.notifications.getNotification, {
    notificationId: notificationId as Id<"notifications">,
  });
};

export const useEntityNotificationsQuery = (entityType: string, entityId: string) => {
  return useQuery(api.notifications.getEntityNotifications, {
    entityType,
    entityId,
  });
};

export const useMarkAsReadMutation = () => {
  return useMutation(api.notifications.markAsRead);
};

export const useMarkAllAsReadMutation = () => {
  return useMutation(api.notifications.markAllAsRead);
};

export const useDeleteNotificationMutation = () => {
  return useMutation(api.notifications.deleteNotification);
};

export const useCreateSystemNotificationMutation = () => {
  return useMutation(api.notifications.createSystemNotification);
};
