/**
 * Test authentication integration
 */

import { getSession } from './workos-auth';
import { authenticatedConvex } from './convex-auth';

export const testFrontendAuth = async () => {
  try {
    console.log('=== Testing Frontend Authentication ===');
    
    // Test 1: Check WorkOS session
    console.log('1. Checking WorkOS session...');
    const session = await getSession();
    
    if (!session) {
      console.error('❌ No WorkOS session found');
      return { success: false, error: 'No WorkOS session' };
    }
    
    console.log('✅ WorkOS session found:', {
      userId: session.user.id,
      email: session.user.email,
      firstName: session.user.firstName,
      lastName: session.user.lastName
    });
    
    // Test 2: Test authenticated area creation
    console.log('2. Testing authenticated area creation...');
    const testAreaName = `Test Area ${Date.now()}`;
    
    try {
      const result = await authenticatedConvex.createArea(testAreaName, 'Test area created from frontend');
      console.log('✅ Area created successfully:', result);
      
      return {
        success: true,
        session: {
          userId: session.user.id,
          email: session.user.email
        },
        areaCreated: result,
        message: 'Frontend authentication working correctly'
      };
      
    } catch (authError) {
      console.error('❌ Area creation failed:', authError);
      return {
        success: false,
        session: {
          userId: session.user.id,
          email: session.user.email
        },
        error: authError.message,
        message: 'Authentication wrapper failed'
      };
    }
    
  } catch (error) {
    console.error('❌ Frontend auth test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Frontend authentication test failed'
    };
  }
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  (window as any).testFrontendAuth = testFrontendAuth;
}