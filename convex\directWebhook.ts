import { action } from "./_generated/server";
import { api } from "./_generated/api";
import { v } from "convex/values";

/**
 * Direct webhook action that can be called via Convex API
 * This bypasses HTTP routing issues and allows us to test webhook functionality
 */
export const processWorkOSWebhook = action({
  args: {
    event: v.string(),
    data: v.any(),
    signature: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log("Processing WorkOS webhook:", args.event);
    
    try {
      // Route to appropriate handler based on event type
      switch (args.event) {
        case "user.created":
          await ctx.runMutation(api.webhooks.handleUserCreated, { 
            userData: args.data 
          });
          break;

        case "user.updated":
          await ctx.runMutation(api.webhooks.handleUserUpdated, { 
            userData: args.data 
          });
          break;

        case "user.deleted":
          await ctx.runMutation(api.webhooks.handleUserDeleted, { 
            userData: args.data 
          });
          break;

        case "organization.created":
          await ctx.runMutation(api.webhooks.handleOrganizationCreated, { 
            organizationData: args.data 
          });
          break;

        case "organization.updated":
          await ctx.runMutation(api.webhooks.handleOrganizationUpdated, { 
            organizationData: args.data 
          });
          break;

        case "organization.deleted":
          await ctx.runMutation(api.webhooks.handleOrganizationDeleted, { 
            organizationData: args.data 
          });
          break;

        default:
          console.log("Unhandled webhook event:", args.event);
      }

      return { success: true, message: "Webhook processed successfully" };
    } catch (error) {
      console.error("Error processing webhook:", error);
      return { success: false, error: error.message };
    }
  },
});