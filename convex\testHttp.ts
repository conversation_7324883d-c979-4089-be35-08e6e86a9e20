import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";

const http = httpRouter();

http.route({
  path: "/hello",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    console.log("Hello endpoint called!");
    return new Response("Hello World!", {
      status: 200,
      headers: {
        "Content-Type": "text/plain",
      },
    });
  }),
});

export default http;