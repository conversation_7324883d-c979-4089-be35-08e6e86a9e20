import { query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";

// Get users by area
export const getUsersByArea = query({
  args: { areaId: v.id("areas") },
  handler: async (ctx, args) => {
    // Get user area assignments
    const assignments = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_area_active", (q) => 
        q.eq("areaId", args.areaId).eq("isActive", true)
      )
      .collect();

    // Get user details
    const users = await Promise.all(
      assignments.map(async (assignment) => {
        const user = await ctx.db.get(assignment.userId);
        return user;
      })
    );

    // Filter out null users and return only active ones
    return users.filter(user => user && user.isActive);
  },
});

// Get users by role
export const getUsersByRole = query({
  args: { roleName: v.string() },
  handler: async (ctx, args) => {
    // Find the role
    const role = await ctx.db
      .query("roles")
      .withIndex("by_name", (q) => q.eq("name", args.roleName))
      .first();

    if (!role) {
      return [];
    }

    // Get user role assignments
    const assignments = await ctx.db
      .query("user_roles")
      .withIndex("by_role_active", (q) => 
        q.eq("roleId", role._id).eq("isActive", true)
      )
      .collect();

    // Get user details
    const users = await Promise.all(
      assignments.map(async (assignment) => {
        const user = await ctx.db.get(assignment.userId);
        return user;
      })
    );

    // Filter out null users and return only active ones
    return users.filter(user => user && user.isActive);
  },
});

// Get users with approval permissions in an area
export const getApprovalUsersInArea = query({
  args: { areaId: v.id("areas") },
  handler: async (ctx, args) => {
    // Get all users in the area
    const areaUsers = await ctx.runQuery(ctx.db.system.query("getUsersByArea"), {
      areaId: args.areaId
    });

    // Get accounts team members (they can approve all requests)
    const accountsUsers = await ctx.runQuery(ctx.db.system.query("getUsersByRole"), {
      roleName: "accounts"
    });

    // Get shop support users (they can approve mobile money under threshold)
    const shopSupportUsers = await ctx.runQuery(ctx.db.system.query("getUsersByRole"), {
      roleName: "shop_support"
    });

    // Combine and deduplicate users
    const allUsers = [...areaUsers, ...accountsUsers, ...shopSupportUsers];
    const uniqueUsers = allUsers.filter((user, index, self) => 
      index === self.findIndex(u => u._id === user._id)
    );

    return uniqueUsers.filter(user => user.isActive);
  },
});