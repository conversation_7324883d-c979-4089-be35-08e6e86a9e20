
import React, { useState } from 'react';
import { useNavigate, Link, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  FileText,
  PlusCircle,
  Settings,
  LogOut,
  Menu,
  Bell,
  User,
  BarChart3,
  Search,
  ChevronDown,
  ChevronRight,
  List
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/lib/auth-context';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { Sheet, SheetContent, SheetTrigger } from '@/components/ui/sheet';
import { ScrollArea } from '@/components/ui/scroll-area';
import { canCreateRequests, canManageUsers } from '@/lib/auth';
import { useIsMobile } from '@/hooks/use-mobile';
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { Toaster } from "@/components/ui/toaster";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuItem,
} from "@/components/ui/dropdown-menu";

interface NavSubItemProps {
  to: string;
  label: string;
  onClick?: () => void;
  active?: boolean;
}

interface NavItemProps {
  to?: string;
  icon: React.ReactNode;
  label: string;
  onClick?: () => void;
  active?: boolean;
  children?: NavSubItemProps[];
  expanded?: boolean;
  onToggle?: () => void;
}

const NavSubItem: React.FC<NavSubItemProps> = ({
  to,
  label,
  onClick,
  active = false
}) => {
  return (
    <Link
      to={to}
      className={`flex items-center gap-3 py-2 rounded-lg transition-colors pl-12 text-sm ${
        active
          ? 'text-primary font-medium'
          : 'text-muted-foreground hover:text-foreground'
      }`}
      onClick={onClick}
    >
      <span>{label}</span>
    </Link>
  );
};

const NavItem: React.FC<NavItemProps> = ({
  to,
  icon,
  label,
  onClick,
  active = false,
  children,
  expanded,
  onToggle
}) => {
  const hasChildren = Array.isArray(children) && children.length > 0;

  if (hasChildren) {
    return (
      <div className="flex flex-col">
        <button
          className={`flex items-center justify-between gap-3 px-4 py-3 rounded-lg transition-colors w-full ${
            active
              ? 'bg-primary/10 text-primary font-medium'
              : 'text-muted-foreground hover:bg-muted hover:text-foreground'
          }`}
          onClick={onToggle}
        >
          <div className="flex items-center gap-3">
            {icon}
            <span>{label}</span>
          </div>
          {expanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
        </button>

        {expanded && (
          <div className="mt-1 space-y-1">
            {children.map((child, index) => (
              <NavSubItem
                key={index}
                to={child.to}
                label={child.label}
                active={child.active}
                onClick={child.onClick}
              />
            ))}
          </div>
        )}
      </div>
    );
  }

  return (
    <Link
      to={to!}
      className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-colors ${
        active
          ? 'bg-primary/10 text-primary font-medium'
          : 'text-muted-foreground hover:bg-muted hover:text-foreground'
      }`}
      onClick={onClick}
    >
      {icon}
      <span>{label}</span>
    </Link>
  );
};

const Layout: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { user, userWithRoles, signOut } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const isMobile = useIsMobile();
  const [expandedMenus, setExpandedMenus] = useState<{ [key: string]: boolean }>({
    requests: true,
  });
  const [unreadCount, setUnreadCount] = useState(0);
  const [notifications, setNotifications] = useState<any[]>([]);

  const handleNotificationClick = (notification: any) => {
    // Mock implementation for demonstration
    if (notification.requestId) {
      navigate(`/requests/${notification.requestId}`);
    }
  };

  if (!user) {
    return <>{children}</>;
  }

  const handleLogout = () => {
    signOut();
  };

  const toggleMenu = (menuId: string) => {
    setExpandedMenus(prev => ({
      ...prev,
      [menuId]: !prev[menuId]
    }));
  };

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(`${path}/`);
  };

  const menuItems = [
    {
      id: 'dashboard',
      to: '/dashboard',
      icon: <LayoutDashboard size={20} />,
      label: 'Dashboard',
      show: true,
    },
    {
      id: 'requests',
      icon: <FileText size={20} />,
      label: 'Requests',
      show: true,
      children: [
        {
          to: '/requests',
          label: 'All Requests',
          show: true,
        },
        {
          to: '/create-request',
          label: 'New Request',
          show: canCreateRequests(userWithRoles?.roles?.map((role: any) => role.name) || []),
        }
      ]
    },
    {
      id: 'shop-manager-report',
      to: '/shop-manager-report',
      icon: <BarChart3 size={20} />,
      label: 'Report',
      show: userWithRoles?.roles?.some((role: any) => role.name === 'shop_manager') || false,
    },
    {
      id: 'reports',
      to: '/reports',
      icon: <BarChart3 size={20} />,
      label: 'Reports',
      show: !userWithRoles?.roles?.some((role: any) => role.name === 'shop_manager') || false,
    },
    {
      id: 'management',
      icon: <List size={20} />,
      label: 'Management',
      show: userWithRoles?.roles?.some((role: any) => role.name === 'accounts') || false,
      children: [
        {
          to: '/shops-management',
          label: 'Shops Management',
          show: userWithRoles?.roles?.some((role: any) => role.name === 'accounts') || false,
        },
        {
          to: '/user-management',
          label: 'User Management',
          show: userWithRoles?.roles?.some((role: any) => role.name === 'accounts') || false,
        }
      ]
    },
    {
      id: 'settings',
      to: '/settings',
      icon: <Settings size={20} />,
      label: 'Settings',
      show: canManageUsers(userWithRoles?.roles?.map((role: any) => role.name) || []),
    },
  ].filter(item => item.show && (!item.children || item.children.some(child => child.show)));

  const renderNavItems = (onItemClick?: () => void) => (
    <>
      {menuItems.map((item) => {
        const hasChildren = item.children && item.children.filter(child => child.show).length > 0;
        const isItemActive = item.to ? isActive(item.to) : (hasChildren && item.children!.some(child => isActive(child.to)));

        return (
          <NavItem
            key={item.id}
            to={item.to}
            icon={item.icon}
            label={item.label}
            active={isItemActive}
            expanded={expandedMenus[item.id]}
            onToggle={() => toggleMenu(item.id)}
            onClick={onItemClick}
            children={hasChildren ? item.children!
              .filter(child => child.show)
              .map(child => ({
                ...child,
                active: isActive(child.to),
                onClick: onItemClick
              })) : undefined}
          />
        );
      })}
    </>
  );

  return (
    <div className="flex min-h-screen bg-app-background overflow-x-hidden">
      {!isMobile && (
        <aside className="fixed left-0 top-0 z-20 h-full w-64 border-r bg-card/30 backdrop-blur-sm pt-20">
          <div className="flex h-full flex-col">
            <ScrollArea className="flex-1 px-3 py-4">
              <nav className="flex flex-col gap-1">
                {renderNavItems()}
              </nav>
            </ScrollArea>
          </div>
        </aside>
      )}

      <header className="fixed top-0 left-0 right-0 z-30 h-20 bg-green-500 shadow-md flex items-center">
        <div className="flex h-full items-center justify-between w-full">
          <div className="flex items-center h-full">
            {isMobile && (
              <Sheet>
                <SheetTrigger asChild>
                  <Button size="icon" variant="ghost" className="text-white ml-2 mr-1">
                    <Menu />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="p-0">
                  <div className="flex h-full flex-col">
                    <div className="px-4 py-6 border-b">
                      <div className="flex items-center justify-center">
                        <img
                          src="/lovable-uploads/d809f2fb-430a-4759-9377-9438f8aacc4f.png"
                          alt="mybet.africa"
                          className="h-8"
                        />
                      </div>
                    </div>

                    <ScrollArea className="flex-1 px-3 py-4">
                      <nav className="flex flex-col gap-1">
                        {renderNavItems(() => {})}
                      </nav>
                    </ScrollArea>
                  </div>
                </SheetContent>
              </Sheet>
            )}

            <div className="flex items-center h-full">
              <div className="flex items-center justify-center bg-white h-20 w-64">
                <div className="flex items-center justify-center w-full">
                  <img
                    src="/lovable-uploads/d809f2fb-430a-4759-9377-9438f8aacc4f.png"
                    alt="mybet.africa"
                    className="h-8 object-contain"
                  />
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-center gap-4 mr-4">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <div className="text-white relative cursor-pointer p-2 rounded-full hover:bg-white/10 transition-colors">
                  <Bell size={20} />
                  {unreadCount > 0 && (
                    <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                      {unreadCount}
                    </div>
                  )}
                </div>
              </DropdownMenuTrigger>

              <DropdownMenuContent align="end" className="w-96">
                {notifications.length === 0 ? (
                  <div className="p-4 text-center text-muted-foreground">
                    No new notifications
                  </div>
                ) : (
                  notifications.map((notification) => (
                    <DropdownMenuItem
                      key={notification.id}
                      onClick={() => handleNotificationClick(notification)}
                      className={`p-3 cursor-pointer ${!notification.isRead ? 'bg-muted/50' : ''}`}
                    >
                      <div className="flex flex-col gap-1">
                        <div className="font-medium">
                          {notification.title}
                        </div>
                        <div className="text-sm text-muted-foreground">
                          {notification.message}
                        </div>
                        {!notification.isRead && (
                          <div className="text-xs text-blue-500 mt-1">
                            Click to review request
                          </div>
                        )}
                      </div>
                    </DropdownMenuItem>
                  ))
                )}
              </DropdownMenuContent>
            </DropdownMenu>

            <HoverCard openDelay={0} closeDelay={200}>
              <HoverCardTrigger asChild>
                <div className="cursor-pointer">
                  <Avatar className="h-9 w-9 border-2 border-white/40 hover:border-white/60 transition-colors">
                    <AvatarImage src={
                      // Temporary fix: Don't show profile picture for ebenezer.eshun
                      user?.email === '<EMAIL>'
                        ? undefined
                        : (user?.profilePictureUrl || undefined)
                    } />
                    <AvatarFallback className="bg-primary-dark text-white">
                      {(() => {
                        // Special case for ebenezer.eshun to show correct initials
                        if (user?.email === '<EMAIL>') {
                          return 'EB';
                        }

                        // Debug logging for avatar generation
                        console.log('Avatar Debug:', {
                          email: user?.email,
                          firstName: user?.firstName,
                          lastName: user?.lastName,
                          fullName: user?.firstName ? (user.firstName + (user.lastName || '')) : 'no firstName'
                        });

                        if (user?.firstName) {
                          const combined = user.firstName + (user.lastName || '');
                          const initials = combined.substring(0, 2).toUpperCase();
                          console.log('Generated initials:', initials);
                          return initials;
                        } else {
                          const emailInitials = user.email?.substring(0, 2).toUpperCase();
                          console.log('Using email initials:', emailInitials);
                          return emailInitials;
                        }
                      })()}
                    </AvatarFallback>
                  </Avatar>
                </div>
              </HoverCardTrigger>
              <HoverCardContent
                className="w-80 p-0 bg-card shadow-lg border rounded-md"
                side="bottom"
                align="end"
                sideOffset={16}
              >
                <div className="flex items-center p-4 border-b">
                  <Avatar className="h-10 w-10 mr-3">
                    <AvatarImage src={
                      // Temporary fix: Don't show profile picture for ebenezer.eshun
                      user?.email === '<EMAIL>'
                        ? undefined
                        : (user?.profilePictureUrl || undefined)
                    } />
                    <AvatarFallback className="bg-primary-dark text-white">
                      {(() => {
                        // Special case for ebenezer.eshun to show correct initials
                        if (user?.email === '<EMAIL>') {
                          return 'EB';
                        }

                        // Debug logging for avatar generation
                        console.log('Avatar Debug (hover card):', {
                          email: user?.email,
                          firstName: user?.firstName,
                          lastName: user?.lastName,
                          fullName: user?.firstName ? (user.firstName + (user.lastName || '')) : 'no firstName'
                        });

                        if (user?.firstName) {
                          const combined = user.firstName + (user.lastName || '');
                          const initials = combined.substring(0, 2).toUpperCase();
                          console.log('Generated initials (hover card):', initials);
                          return initials;
                        } else {
                          const emailInitials = user.email?.substring(0, 2).toUpperCase();
                          console.log('Using email initials (hover card):', emailInitials);
                          return emailInitials;
                        }
                      })()}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex flex-col">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : user.email}</span>
                    </div>
                    <span className="text-xs text-muted-foreground">
                      {user?.email}
                    </span>
                    <span className="text-xs text-muted-foreground capitalize">
                      {userWithRoles?.roles?.map((role: any) => role.name).join(', ').replace('_', ' ') || 'User'}
                    </span>
                  </div>
                </div>

                <div className="p-2">
                  <div
                    className="flex items-center gap-2 p-3 rounded-md hover:bg-accent cursor-pointer"
                    onClick={() => navigate("/profile")}
                  >
                    <User className="h-4 w-4" />
                    <span>Profile Details</span>
                  </div>

                  <Separator className="my-2" />

                  <div
                    className="flex items-center gap-2 p-3 rounded-md hover:bg-accent text-red-500 cursor-pointer"
                    onClick={handleLogout}
                  >
                    <LogOut className="h-4 w-4" />
                    <span>Logout</span>
                  </div>
                </div>
              </HoverCardContent>
            </HoverCard>
          </div>
        </div>
      </header>

      <main className={`flex-1 ${!isMobile ? 'ml-64' : ''} pt-20 bg-app-background`}>
        <div className="container mx-auto p-6">
          {children}
        </div>
      </main>
    </div>
  );
};

export default Layout;
