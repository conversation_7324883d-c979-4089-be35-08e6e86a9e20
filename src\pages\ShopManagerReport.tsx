import React, { useEffect, useState } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import PageTransition from '@/components/common/PageTransition';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell, Legend, LineChart, Line } from 'recharts';
import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { Button } from '@/components/ui/button';
import { Download, TrendingUp, TrendingDown, FileText, CheckCircle, XCircle, Clock } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { CalendarIcon } from 'lucide-react';
import { format, endOfDay, subMonths, addMonths, startOfMonth, endOfMonth, isWithinInterval } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { formatAmount, formatYAxisValue } from '@/lib/currency';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useAuth } from '@/lib/auth-context';

// Generate monthly data for trends
const generateMonthlyData = (dateRange: DateRange | undefined, requests: any[]) => {
  if (!requests || requests.length === 0) return [];

  const startDate = dateRange?.from || subMonths(new Date(), 11);
  const endDate = dateRange?.to || new Date();

  const months: Date[] = [];
  let currentDate = startDate;

  while (currentDate <= endDate) {
    months.push(new Date(currentDate));
    currentDate = addMonths(currentDate, 1);
  }

  return months.map(date => {
    const monthStart = startOfMonth(date);
    const monthEnd = endOfMonth(date);

    const monthRequests = requests.filter(request => {
      const requestDate = new Date(request.createdAt);
      return isWithinInterval(requestDate, { start: monthStart, end: monthEnd });
    });

    const totalRequests = monthRequests.length;
    const approvedRequests = monthRequests.filter(r => r.status === 'approved').length;
    const totalAmount = monthRequests.reduce((sum, r) => sum + r.amount, 0);
    const approvedAmount = monthRequests.filter(r => r.status === 'approved').reduce((sum, r) => sum + r.amount, 0);

    return {
      month: format(date, 'MMM'),
      totalRequests,
      approvedRequests,
      totalAmount,
      approvedAmount,
      approvalRate: totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0
    };
  });
};

// Custom tooltip for charts
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    const data = payload[0].payload;
    return (
      <div className="rounded-lg bg-white shadow-lg border p-3 text-sm">
        <div className="font-medium mb-2">{label}</div>
        <div className="space-y-1">
          {payload.map((entry: any, index: number) => (
            <div key={index} className="flex justify-between gap-4">
              <span className="text-gray-600">{entry.name}:</span>
              <span className="font-medium" style={{ color: entry.color }}>
                {entry.name.includes('Amount') ? formatAmount(entry.value) : entry.value}
                {entry.name.includes('Rate') && '%'}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

const ShopManagerReport: React.FC = () => {
  // Check authentication - only shop managers
  useRequireAuth(['shop_manager']);

  const { user } = useAuth();
  
  // Use Convex query to get requests for shop manager
  const convexRequests = useQuery(
    api.publicQueries.getRequestsForUser,
    user?.id ? { workosUserId: user.id } : "skip"
  );
  
  const requests = convexRequests || [];
  const isLoading = !convexRequests;
  const [timeRange, setTimeRange] = useState('all');
  const [dateRangeValue, setDateRangeValue] = useState<DateRange | undefined>();

  // Data fetching is now handled by Convex queries automatically

  // Show loading state
  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-[60vh]">
          <div className="animate-pulse flex flex-col items-center">
            <div className="h-8 w-64 bg-muted rounded mb-4"></div>
            <div className="h-4 w-48 bg-muted rounded"></div>
          </div>
        </div>
      </Layout>
    );
  }

  const filterRequestsByTime = () => {
    if (timeRange === 'all') return requests;

    const now = new Date();
    let startDate = new Date();

    switch (timeRange) {
      case 'today':
        startDate = new Date();
        startDate.setHours(0, 0, 0, 0);
        return requests.filter(r => new Date(r.createdAt) >= startDate);
      case 'yesterday':
        startDate = new Date();
        startDate.setDate(now.getDate() - 1);
        startDate.setHours(0, 0, 0, 0);
        const endDate = new Date(startDate);
        endDate.setHours(23, 59, 59, 999);
        return requests.filter(r => {
          const date = new Date(r.createdAt);
          return date >= startDate && date <= endDate;
        });
      case 'last_week':
        startDate.setDate(now.getDate() - 7);
        break;
      case 'last_month':
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'custom':
        if (dateRangeValue?.from) {
          const customEnd = dateRangeValue.to ? endOfDay(dateRangeValue.to) : endOfDay(dateRangeValue.from);
          return requests.filter(r => {
            const date = new Date(r.createdAt);
            return date >= dateRangeValue.from && date <= customEnd;
          });
        }
        return requests;
      default:
        return requests;
    }

    return requests.filter(r => new Date(r.createdAt) >= startDate);
  };

  const filteredRequests = filterRequestsByTime();

  // If there are no requests, show empty state
  if (!requests.length) {
    return (
      <Layout>
        <PageTransition>
          <div className="animate-fade-in">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">My Reports</h1>
                <p className="text-muted-foreground mt-1">
                  Personal analytics and insights for your cash requests
                </p>
              </div>
            </div>
            <Card className="glass-panel">
              <CardContent className="flex flex-col items-center justify-center py-12">
                <p className="text-muted-foreground text-center">
                  No requests data available to generate reports.
                </p>
              </CardContent>
            </Card>
          </div>
        </PageTransition>
      </Layout>
    );
  }

  // Calculate statistics
  const totalRequests = filteredRequests.length;
  const approvedRequests = filteredRequests.filter(r => r.status === 'approved').length;
  const rejectedRequests = filteredRequests.filter(r => r.status === 'rejected').length;
  const pendingRequests = filteredRequests.filter(r => r.status === 'pending').length;
  const resubmittedRequests = filteredRequests.filter(r => r.status === 'resubmitted').length;

  const totalRequestedAmount = filteredRequests.reduce((sum, r) => sum + r.amount, 0);
  const totalApprovedAmount = filteredRequests.filter(r => r.status === 'approved').reduce((sum, r) => sum + r.amount, 0);
  const approvalRate = totalRequests > 0 ? (approvedRequests / totalRequests) * 100 : 0;

  // Prepare data for status chart
  const statusData = [
    { name: 'Approved', value: approvedRequests, color: '#10B981' },
    { name: 'Pending', value: pendingRequests, color: '#F59E0B' },
    { name: 'Rejected', value: rejectedRequests, color: '#EF4444' },
    { name: 'Resubmitted', value: resubmittedRequests, color: '#8B5CF6' },
  ].filter(item => item.value > 0);

  // Prepare data for payment method chart
  const paymentMethodData = [
    {
      name: 'Mobile Money',
      value: filteredRequests.filter(r => r.paymentMethod === 'mobile_money').length,
      amount: filteredRequests.filter(r => r.paymentMethod === 'mobile_money' && r.status === 'approved').reduce((sum, r) => sum + r.amount, 0)
    },
    {
      name: 'Bank Transfer',
      value: filteredRequests.filter(r => r.paymentMethod === 'bank_transfer').length,
      amount: filteredRequests.filter(r => r.paymentMethod === 'bank_transfer' && r.status === 'approved').reduce((sum, r) => sum + r.amount, 0)
    },
  ].filter(item => item.value > 0);

  const monthlyData = generateMonthlyData(dateRangeValue, filteredRequests);

  const exportToCSV = () => {
    const headers = ['Date', 'Status', 'Amount', 'Payment Method', 'Shop'];
    const data = filteredRequests.map(request => [
      format(new Date(request.createdAt), 'yyyy-MM-dd'),
      request.status,
      request.amount,
      request.paymentMethod === 'mobile_money' ? 'Mobile Money' : 'Bank Transfer',
      request.shopName || 'N/A'
    ]);

    const csv = [
      headers.join(','),
      ...data.map(row => row.join(','))
    ].join('\n');

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `my-requests-report-${format(new Date(), 'yyyy-MM-dd')}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <Layout>
      <PageTransition>
        <div className="animate-fade-in">
          <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">My Reports</h1>
              <p className="text-muted-foreground mt-1">
                Personal analytics and insights for your cash requests
              </p>
            </div>

            <div className="flex flex-col md:flex-row gap-4">
              <Select value={timeRange} onValueChange={setTimeRange}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Select time range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="last_week">Last Week</SelectItem>
                  <SelectItem value="last_month">Last Month</SelectItem>
                  <SelectItem value="all">All Time</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>

              {timeRange === "custom" && (
                <Popover>
                  <PopoverTrigger asChild>
                    <Button variant="outline" className="w-[300px]">
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {dateRangeValue?.from ? (
                        dateRangeValue.to ? (
                          <>
                            {format(dateRangeValue.from, "LLL dd, y")} -{" "}
                            {format(dateRangeValue.to, "LLL dd, y")}
                          </>
                        ) : (
                          format(dateRangeValue.from, "LLL dd, y")
                        )
                      ) : (
                        <span>Pick a date range</span>
                      )}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      initialFocus
                      mode="range"
                      defaultMonth={dateRangeValue?.from}
                      selected={dateRangeValue}
                      onSelect={setDateRangeValue}
                      numberOfMonths={2}
                    />
                  </PopoverContent>
                </Popover>
              )}

              <Button onClick={exportToCSV} className="flex items-center gap-2">
                <Download className="h-4 w-4" />
                <span>Export CSV</span>
              </Button>
            </div>
          </div>

          {/* Personal Statistics Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-6">
            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Total Requests
                </CardTitle>
                <CardDescription>All your submissions</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold">{totalRequests}</div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  Approved
                </CardTitle>
                <CardDescription>Successfully approved requests</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-green-600">{approvedRequests}</div>
                <div className="text-sm text-muted-foreground mt-1">
                  {formatAmount(totalApprovedAmount)} approved
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  Pending
                </CardTitle>
                <CardDescription>Awaiting review</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="text-3xl font-bold text-yellow-600">{pendingRequests + resubmittedRequests}</div>
                <div className="text-sm text-muted-foreground mt-1">
                  {resubmittedRequests} resubmitted
                </div>
              </CardContent>
            </Card>

            <Card className="glass-card">
              <CardHeader className="pb-2">
                <CardTitle className="text-base flex items-center gap-2">
                  {approvalRate >= 70 ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  Approval Rate
                </CardTitle>
                <CardDescription>Success percentage</CardDescription>
              </CardHeader>
              <CardContent>
                <div className={`text-3xl font-bold ${approvalRate >= 70 ? 'text-green-600' : 'text-red-600'}`}>
                  {approvalRate.toFixed(1)}%
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts Section */}
          <div className="grid gap-6 md:grid-cols-2 mb-6">
            <Card className="glass-panel">
              <CardHeader>
                <CardTitle>Request Status Distribution</CardTitle>
                <CardDescription>
                  Breakdown of your request statuses
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {statusData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <PieChart>
                        <Pie
                          data={statusData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="value"
                        >
                          {statusData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={entry.color} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value) => [`${value} requests`, 'Count']} />
                        <Legend />
                      </PieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="glass-panel">
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>
                  Your preferred payment methods
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px]">
                  {paymentMethodData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart data={paymentMethodData}>
                        <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                        <XAxis dataKey="name" />
                        <YAxis />
                        <Tooltip
                          formatter={(value, name) => [
                            name === 'value' ? `${value} requests` : formatAmount(value as number),
                            name === 'value' ? 'Requests' : 'Approved Amount'
                          ]}
                          contentStyle={{ background: 'var(--background)', border: '1px solid var(--border)' }}
                        />
                        <Bar dataKey="value" name="Requests" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="flex items-center justify-center h-full">
                      <p className="text-muted-foreground">No data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Performance Trends */}
          <Card className="glass-panel mb-6">
            <CardHeader>
              <CardTitle>Performance Trends</CardTitle>
              <CardDescription>
                Monthly submission and approval trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="requests" className="w-full">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="requests">Request Volume</TabsTrigger>
                  <TabsTrigger value="amounts">Amount Trends</TabsTrigger>
                </TabsList>

                <TabsContent value="requests" className="space-y-4">
                  <div className="h-[400px] w-full">
                    {monthlyData.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <LineChart data={monthlyData}>
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="month" />
                          <YAxis />
                          <Tooltip content={<CustomTooltip />} />
                          <Line
                            type="monotone"
                            dataKey="totalRequests"
                            stroke="#3b82f6"
                            strokeWidth={2}
                            name="Total Requests"
                          />
                          <Line
                            type="monotone"
                            dataKey="approvedRequests"
                            stroke="#10b981"
                            strokeWidth={2}
                            name="Approved Requests"
                          />
                        </LineChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground">No trend data available</p>
                      </div>
                    )}
                  </div>
                </TabsContent>

                <TabsContent value="amounts" className="space-y-4">
                  <div className="h-[400px] w-full">
                    {monthlyData.length > 0 ? (
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart data={monthlyData}>
                          <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
                          <XAxis dataKey="month" />
                          <YAxis tickFormatter={formatYAxisValue} />
                          <Tooltip content={<CustomTooltip />} />
                          <Bar
                            dataKey="totalAmount"
                            fill="#3b82f6"
                            radius={[4, 4, 0, 0]}
                            name="Total Amount"
                          />
                          <Bar
                            dataKey="approvedAmount"
                            fill="#10b981"
                            radius={[4, 4, 0, 0]}
                            name="Approved Amount"
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    ) : (
                      <div className="flex items-center justify-center h-full">
                        <p className="text-muted-foreground">No trend data available</p>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Summary Insights */}
          <Card className="glass-panel">
            <CardHeader>
              <CardTitle>Performance Summary</CardTitle>
              <CardDescription>
                Key insights about your request patterns
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <h4 className="font-medium">Request Statistics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Requested:</span>
                      <span className="font-medium">{formatAmount(totalRequestedAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Approved:</span>
                      <span className="font-medium text-green-600">{formatAmount(totalApprovedAmount)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Request:</span>
                      <span className="font-medium">
                        {totalRequests > 0 ? formatAmount(totalRequestedAmount / totalRequests) : formatAmount(0)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Success Rate:</span>
                      <span className={`font-medium ${approvalRate >= 70 ? 'text-green-600' : 'text-red-600'}`}>
                        {approvalRate.toFixed(1)}%
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Payment Preferences</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Mobile Money Requests:</span>
                      <span className="font-medium">
                        {filteredRequests.filter(r => r.paymentMethod === 'mobile_money').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Bank Transfer Requests:</span>
                      <span className="font-medium">
                        {filteredRequests.filter(r => r.paymentMethod === 'bank_transfer').length}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Most Used Method:</span>
                      <span className="font-medium">
                        {paymentMethodData.length > 0
                          ? paymentMethodData.reduce((a, b) => a.value > b.value ? a : b).name
                          : 'N/A'
                        }
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </PageTransition>
    </Layout>
  );
};

export default ShopManagerReport;
