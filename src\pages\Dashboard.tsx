
import React from 'react';
import Layout from '@/components/layout/Layout';
import { useAuth } from '@/lib/auth-context';
import DashboardContent from '@/components/dashboard/Dashboard';
import PageTransition from '@/components/common/PageTransition';

const Dashboard: React.FC = () => {
  // Use auth context directly for simpler authentication check
  const { user, isLoading } = useAuth();

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading dashboard...</p>
          </div>
        </div>
      </Layout>
    );
  }

  // If no user, show message (shouldn't happen due to routing)
  if (!user) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-[60vh]">
          <div className="text-center">
            <p className="text-muted-foreground">No user found. Please login.</p>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <PageTransition>
        <div className="mt-8 mx-6 mb-6">
          <DashboardContent />
        </div>
      </PageTransition>
    </Layout>
  );
};

export default Dashboard;
