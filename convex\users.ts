import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { api } from "./_generated/api";
import {
  requireAuth,
  requirePermission,
  requirePermissionWithWorkOSUser,
  requireAnyPermission,
  requireAnyPermissionWithWorkOSUser,
  getCurrentUserWithPermissions,
  PERMISSIONS,
  canAccessArea,
} from "./permissions";

// Get all users (with role-based filtering)
export const getUsers = query({
  args: {
    limit: v.optional(v.number()),
    offset: v.optional(v.number()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_VIEW);

    const users = await ctx.db
      .query("users")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .order("desc")
      .take(args.limit || 50);

    // Get roles and areas for each user
    const usersWithRoles = await Promise.all(
      users.map(async (user) => {
        const userRoles = await ctx.db
          .query("user_roles")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", user._id).eq("isActive", true)
          )
          .collect();

        const roles = await Promise.all(
          userRoles.map(async (userRole) => {
            const role = await ctx.db.get(userRole.roleId);
            return role;
          })
        );

        const userAreas = await ctx.db
          .query("user_area_assignments")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", user._id).eq("isActive", true)
          )
          .collect();

        const areas = await Promise.all(
          userAreas.map(async (userArea) => {
            const area = await ctx.db.get(userArea.areaId);
            return area;
          })
        );

        return {
          ...user,
          roles: roles.filter(Boolean),
          areas: areas.filter(Boolean),
        };
      })
    );

    return usersWithRoles;
  },
});

// Get user by ID
export const getUser = query({
  args: { userId: v.id("users") },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.USER_VIEW);

    const user = await ctx.db.get(args.userId);
    if (!user) {
      return null;
    }

    // Get user roles
    const userRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .collect();

    const roles = await Promise.all(
      userRoles.map(async (userRole) => {
        const role = await ctx.db.get(userRole.roleId);
        return role;
      })
    );

    // Get user areas
    const userAreas = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .collect();

    const areas = await Promise.all(
      userAreas.map(async (userArea) => {
        const area = await ctx.db.get(userArea.areaId);
        return area;
      })
    );

    return {
      ...user,
      roles: roles.filter(Boolean),
      areas: areas.filter(Boolean),
    };
  },
});

// Update user
export const updateUser = mutation({
  args: {
    userId: v.id("users"),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
    syncToWorkOS: v.optional(v.boolean()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_UPDATE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_UPDATE);

    const { userId, syncToWorkOS, workosUserId, ...updates } = args;
    
    await ctx.db.patch(userId, {
      ...updates,
      updatedAt: Date.now(),
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "user_updated",
      entityType: "user",
      entityId: userId,
      userId: currentUser.user._id,
      newValues: updates,
      timestamp: Date.now(),
    });

    // Sync to WorkOS if requested
    if (syncToWorkOS !== false) {
      try {
        await ctx.scheduler.runAfter(0, api.workosApi.syncUserToWorkOS, {
          userId,
          operation: "update",
        });
      } catch (error) {
        console.error("Failed to schedule WorkOS sync:", error);
        // Don't fail the user update if WorkOS sync fails
      }
    }

    return userId;
  },
});

// Update current user's own profile
export const updateCurrentUserProfile = mutation({
  args: {
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    phone: v.optional(v.string()),
    profilePicture: v.optional(v.string()),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    // Get current user - either through WorkOS or regular auth
    const currentUser = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await requireAuth(ctx);

    if (!currentUser || !currentUser.user) {
      throw new Error("Authentication required");
    }

    const { workosUserId, ...updates } = args;

    // Filter out undefined values
    const cleanUpdates = Object.fromEntries(
      Object.entries(updates).filter(([_, value]) => value !== undefined)
    );

    if (Object.keys(cleanUpdates).length === 0) {
      throw new Error("No updates provided");
    }

    await ctx.db.patch(currentUser.user._id, {
      ...cleanUpdates,
      updatedAt: Date.now(),
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "profile_updated",
      entityType: "user",
      entityId: currentUser.user._id,
      userId: currentUser.user._id,
      newValues: cleanUpdates,
      timestamp: Date.now(),
    });

    return {
      success: true,
      message: "Profile updated successfully",
      updatedFields: Object.keys(cleanUpdates),
    };
  },
});

// Change user password (via WorkOS password reset)
export const changePassword = mutation({
  args: {
    workosUserId: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Get current user
    const currentUser = args.workosUserId
      ? await getCurrentUserWithPermissions(ctx, args.workosUserId)
      : await requireAuth(ctx);

    if (!currentUser || !currentUser.user) {
      throw new Error("Authentication required");
    }

    // Since WorkOS AuthKit handles password management through their hosted UI,
    // we need to trigger a password reset flow for the user
    try {
      // Schedule a password reset request to WorkOS
      await ctx.scheduler.runAfter(0, api.workosApi.requestPasswordReset, {
        email: currentUser.user.email,
        userId: currentUser.user._id,
      });

      // Create audit log entry
      await ctx.db.insert("audit_logs", {
        action: "password_change_requested",
        entityType: "user",
        entityId: currentUser.user._id,
        userId: currentUser.user._id,
        timestamp: Date.now(),
      });

      return {
        success: true,
        message: "Password reset email sent. Please check your email to complete the password change.",
      };
    } catch (error) {
      console.error("Password change request failed:", error);
      throw new Error("Failed to initiate password change. Please try again.");
    }
  },
});

// Assign role to user
export const assignRole = mutation({
  args: {
    userId: v.id("users"),
    roleId: v.id("roles"),
  },
  handler: async (ctx, args) => {
    const currentUser = await requirePermission(ctx, PERMISSIONS.USER_ASSIGN_ROLE);

    // Check if user already has this role
    const existingAssignment = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .filter((q) => q.eq(q.field("roleId"), args.roleId))
      .first();

    if (existingAssignment) {
      throw new Error("User already has this role");
    }

    const now = Date.now();
    const assignmentId = await ctx.db.insert("user_roles", {
      userId: args.userId,
      roleId: args.roleId,
      assignedBy: currentUser.user._id,
      assignedAt: now,
      isActive: true,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "role_assigned",
      entityType: "user_role",
      entityId: assignmentId,
      userId: currentUser.user._id,
      newValues: args,
      timestamp: now,
    });

    return assignmentId;
  },
});

// Remove role from user
export const removeRole = mutation({
  args: {
    userId: v.id("users"),
    roleId: v.id("roles"),
  },
  handler: async (ctx, args) => {
    const currentUser = await requirePermission(ctx, PERMISSIONS.USER_ASSIGN_ROLE);

    const assignment = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .filter((q) => q.eq(q.field("roleId"), args.roleId))
      .first();

    if (!assignment) {
      throw new Error("User does not have this role");
    }

    await ctx.db.patch(assignment._id, {
      isActive: false,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "role_removed",
      entityType: "user_role",
      entityId: assignment._id,
      userId: currentUser.user._id,
      oldValues: assignment,
      timestamp: Date.now(),
    });

    return assignment._id;
  },
});

// Assign area to user
export const assignArea = mutation({
  args: {
    userId: v.id("users"),
    areaId: v.id("areas"),
  },
  handler: async (ctx, args) => {
    const currentUser = await requirePermission(ctx, PERMISSIONS.USER_ASSIGN_AREA);

    // Check if current user can access this area
    const canAccess = await canAccessArea(ctx, args.areaId);
    if (!canAccess) {
      throw new Error("Cannot assign area you don't have access to");
    }

    // Check if user already has this area
    const existingAssignment = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .filter((q) => q.eq(q.field("areaId"), args.areaId))
      .first();

    if (existingAssignment) {
      throw new Error("User already assigned to this area");
    }

    const now = Date.now();
    const assignmentId = await ctx.db.insert("user_area_assignments", {
      userId: args.userId,
      areaId: args.areaId,
      assignedBy: currentUser.user._id,
      assignedAt: now,
      isActive: true,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "area_assigned",
      entityType: "user_area_assignment",
      entityId: assignmentId,
      userId: currentUser.user._id,
      newValues: args,
      timestamp: now,
    });

    return assignmentId;
  },
});

// Remove area from user
export const removeArea = mutation({
  args: {
    userId: v.id("users"),
    areaId: v.id("areas"),
  },
  handler: async (ctx, args) => {
    const currentUser = await requirePermission(ctx, PERMISSIONS.USER_ASSIGN_AREA);

    const assignment = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .filter((q) => q.eq(q.field("areaId"), args.areaId))
      .first();

    if (!assignment) {
      throw new Error("User is not assigned to this area");
    }

    await ctx.db.patch(assignment._id, {
      isActive: false,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "area_removed",
      entityType: "user_area_assignment",
      entityId: assignment._id,
      userId: currentUser.user._id,
      oldValues: assignment,
      timestamp: Date.now(),
    });

    return assignment._id;
  },
});

// Invite user
export const inviteUser = mutation({
  args: {
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    roleId: v.id("roles"),
    areaIds: v.optional(v.array(v.id("areas"))),
    shopId: v.optional(v.id("shops")), // For shop manager assignments
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_INVITE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_INVITE);

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .first();

    if (existingUser) {
      throw new Error("User with this email already exists");
    }

    // Check if there's already a pending invitation
    const existingInvitation = await ctx.db
      .query("invitations")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    if (existingInvitation) {
      throw new Error("Pending invitation already exists for this email");
    }

    // Get role information for WorkOS mapping
    const role = await ctx.db.get(args.roleId);
    if (!role) {
      throw new Error("Role not found");
    }

    // Generate invitation token
    const token = crypto.randomUUID();
    const now = Date.now();
    const expiresAt = now + (7 * 24 * 60 * 60 * 1000); // 7 days

    const invitationId = await ctx.db.insert("invitations", {
      email: args.email,
      roleId: args.roleId,
      areaIds: args.areaIds,
      shopId: args.shopId,
      invitedBy: currentUser.user._id,
      invitedAt: now,
      expiresAt,
      status: "pending",
      token,
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "user_invited",
      entityType: "invitation",
      entityId: invitationId,
      userId: currentUser.user._id,
      newValues: { email: args.email, roleId: args.roleId },
      timestamp: now,
    });

    // Schedule WorkOS invitation creation
    try {
      await ctx.scheduler.runAfter(0, api.workosApi.createWorkOSInvitation, {
        email: args.email,
        firstName: args.firstName || "",
        lastName: args.lastName || "",
        roleSlug: role.name, // Use role name as slug
      });
    } catch (error) {
      console.error("Failed to schedule WorkOS invitation:", error);
      // Update invitation status to indicate WorkOS creation failed
      await ctx.db.patch(invitationId, {
        status: "failed" as any,
        metadata: {
          error: "Failed to create WorkOS invitation",
          details: error.message,
        },
      });
      throw new Error("Failed to create WorkOS invitation. Please try again.");
    }

    return { invitationId, token };
  },
});

// Delete user (soft delete)
export const deleteUser = mutation({
  args: {
    userId: v.id("users"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_DELETE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_DELETE);

    const user = await ctx.db.get(args.userId);
    if (!user) {
      throw new Error("User not found");
    }

    // Soft delete by setting isActive to false
    await ctx.db.patch(args.userId, {
      isActive: false,
      updatedAt: Date.now(),
    });

    // Deactivate all user roles
    const userRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .collect();

    for (const userRole of userRoles) {
      await ctx.db.patch(userRole._id, { isActive: false });
    }

    // Deactivate all area assignments
    const userAreas = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", args.userId).eq("isActive", true)
      )
      .collect();

    for (const userArea of userAreas) {
      await ctx.db.patch(userArea._id, { isActive: false });
    }

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "user_deleted",
      entityType: "user",
      entityId: args.userId,
      userId: currentUser.user._id,
      oldValues: user,
      timestamp: Date.now(),
    });

    // Schedule WorkOS user deletion
    if (user.workosId) {
      try {
        await ctx.scheduler.runAfter(0, api.workosApi.deleteWorkOSUser, {
          workosUserId: user.workosId,
        });
      } catch (error) {
        console.error("Failed to schedule WorkOS user deletion:", error);
        // Don't fail the Convex deletion if WorkOS deletion fails
        // Log the error for manual cleanup if needed
        await ctx.db.insert("audit_logs", {
          action: "workos_deletion_failed",
          entityType: "user",
          entityId: args.userId,
          userId: currentUser.user._id,
          metadata: {
            error: "Failed to delete user from WorkOS",
            details: error.message,
            workosId: user.workosId,
          },
          timestamp: Date.now(),
        });
      }
    }

    return args.userId;
  },
});

// Resend invitation
export const resendInvitation = mutation({
  args: {
    invitationId: v.id("invitations"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_INVITE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_INVITE);

    const invitation = await ctx.db.get(args.invitationId);
    if (!invitation) {
      throw new Error("Invitation not found");
    }

    if (invitation.status !== "pending" && invitation.status !== "failed") {
      throw new Error("Can only resend pending or failed invitations");
    }

    // Update invitation with new expiry and reset status
    const now = Date.now();
    const expiresAt = now + (7 * 24 * 60 * 60 * 1000); // 7 days

    await ctx.db.patch(args.invitationId, {
      status: "pending",
      expiresAt,
      metadata: undefined, // Clear any error metadata
    });

    // Get role information for WorkOS
    const role = await ctx.db.get(invitation.roleId);
    if (!role) {
      throw new Error("Role not found");
    }

    // Schedule WorkOS invitation creation
    try {
      await ctx.scheduler.runAfter(0, api.workosApi.createWorkOSInvitation, {
        email: invitation.email,
        firstName: "",
        lastName: "",
        roleSlug: role.name,
      });
    } catch (error) {
      console.error("Failed to schedule WorkOS invitation:", error);
      await ctx.db.patch(args.invitationId, {
        status: "failed",
        metadata: {
          error: "Failed to create WorkOS invitation",
          details: error.message,
        },
      });
      throw new Error("Failed to resend WorkOS invitation. Please try again.");
    }

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "invitation_resent",
      entityType: "invitation",
      entityId: args.invitationId,
      userId: currentUser.user._id,
      timestamp: now,
    });

    return args.invitationId;
  },
});

// Cancel invitation
export const cancelInvitation = mutation({
  args: {
    invitationId: v.id("invitations"),
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_INVITE, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_INVITE);

    const invitation = await ctx.db.get(args.invitationId);
    if (!invitation) {
      throw new Error("Invitation not found");
    }

    if (invitation.status !== "pending") {
      throw new Error("Can only cancel pending invitations");
    }

    // Update invitation status to cancelled
    await ctx.db.patch(args.invitationId, {
      status: "cancelled",
    });

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "invitation_cancelled",
      entityType: "invitation",
      entityId: args.invitationId,
      userId: currentUser.user._id,
      timestamp: Date.now(),
    });

    return args.invitationId;
  },
});

// Get all roles
export const getRoles = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    if (args.workosUserId) {
      // For WorkOS users, get user with permissions
      const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
      if (!userWithPermissions) {
        throw new Error("Authentication required");
      }
    } else {
      // For Convex Auth users, use the old method
      await requireAuth(ctx);
    }

    return await ctx.db
      .query("roles")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();
  },
});

// Get all areas
export const getAreas = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    if (args.workosUserId) {
      // For WorkOS users, get user with permissions
      const userWithPermissions = await getCurrentUserWithPermissions(ctx, args.workosUserId);
      if (!userWithPermissions) {
        throw new Error("Authentication required");
      }
    } else {
      // For Convex Auth users, use the old method
      await requireAuth(ctx);
    }

    return await ctx.db
      .query("areas")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();
  },
});

// Get all invitations
export const getInvitations = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_VIEW);

    const invitations = await ctx.db
      .query("invitations")
      .order("desc")
      .collect();

    // Get role and area information for each invitation
    const invitationsWithDetails = await Promise.all(
      invitations.map(async (invitation) => {
        const role = await ctx.db.get(invitation.roleId);
        
        let areas: any[] = [];
        if (invitation.areaIds) {
          areas = await Promise.all(
            invitation.areaIds.map(async (areaId) => {
              const area = await ctx.db.get(areaId);
              return area;
            })
          );
          areas = areas.filter(Boolean);
        }

        const invitedBy = await ctx.db.get(invitation.invitedBy);

        return {
          ...invitation,
          role,
          areas,
          invitedBy,
        };
      })
    );

    return invitationsWithDetails;
  },
});

// Get users and invitations combined
export const getUsersAndInvitations = query({
  args: {
    workosUserId: v.optional(v.string()), // For WorkOS AuthKit integration
  },
  handler: async (ctx, args) => {
    const currentUser = args.workosUserId
      ? await requirePermissionWithWorkOSUser(ctx, PERMISSIONS.USER_VIEW, args.workosUserId)
      : await requirePermission(ctx, PERMISSIONS.USER_VIEW);

    // Get active users
    const users = await ctx.db
      .query("users")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .order("desc")
      .collect();

    // Get users with roles and areas
    const usersWithRoles = await Promise.all(
      users.map(async (user) => {
        const userRoles = await ctx.db
          .query("user_roles")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", user._id).eq("isActive", true)
          )
          .collect();

        const roles = await Promise.all(
          userRoles.map(async (userRole) => {
            const role = await ctx.db.get(userRole.roleId);
            return role;
          })
        );

        const userAreas = await ctx.db
          .query("user_area_assignments")
          .withIndex("by_user_active", (q) => 
            q.eq("userId", user._id).eq("isActive", true)
          )
          .collect();

        const areas = await Promise.all(
          userAreas.map(async (userArea) => {
            const area = await ctx.db.get(userArea.areaId);
            return area;
          })
        );

        return {
          ...user,
          roles: roles.filter(Boolean),
          areas: areas.filter(Boolean),
          type: "user" as const,
        };
      })
    );

    // Get pending invitations
    const invitations = await ctx.db
      .query("invitations")
      .filter((q) => q.eq(q.field("status"), "pending"))
      .order("desc")
      .collect();

    const invitationsWithDetails = await Promise.all(
      invitations.map(async (invitation) => {
        const role = await ctx.db.get(invitation.roleId);
        
        let areas: any[] = [];
        if (invitation.areaIds) {
          areas = await Promise.all(
            invitation.areaIds.map(async (areaId) => {
              const area = await ctx.db.get(areaId);
              return area;
            })
          );
          areas = areas.filter(Boolean);
        }

        const invitedBy = await ctx.db.get(invitation.invitedBy);

        return {
          ...invitation,
          role,
          areas,
          invitedBy,
          type: "invitation" as const,
        };
      })
    );

    return {
      users: usersWithRoles,
      invitations: invitationsWithDetails,
    };
  },
});
