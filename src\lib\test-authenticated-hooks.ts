/**
 * Test authenticated React hooks
 */

import { getCurrentWorkOSUserId } from './convex-auth';

export const testAuthenticatedHooks = async () => {
  try {
    console.log('🔍 Testing authenticated React hooks...');
    
    // Test 1: Check WorkOS user ID
    console.log('\n📋 Step 1: Getting WorkOS user ID...');
    const workosUserId = await getCurrentWorkOSUserId();
    
    if (!workosUserId) {
      return {
        success: false,
        error: 'No WorkOS user ID found',
        message: 'User not logged in or session expired'
      };
    }
    
    console.log('✅ WorkOS user ID:', workosUserId);
    
    return {
      success: true,
      workosUserId,
      message: 'Authenticated hooks setup working correctly',
      instructions: [
        '1. The authenticated hooks are now implemented',
        '2. All area queries will automatically include WorkOS authentication',
        '3. Frontend components using useAreaQuery, useAreasQuery, etc. should now work',
        '4. Test by creating, updating, or deleting areas from the UI'
      ]
    };
    
  } catch (error) {
    console.error('❌ Authenticated hooks test failed:', error);
    return {
      success: false,
      error: error.message,
      message: 'Authenticated hooks test failed'
    };
  }
};

// Make it available globally for browser console testing
if (typeof window !== 'undefined') {
  (window as any).testAuthenticatedHooks = testAuthenticatedHooks;
}