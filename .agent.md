# Workspace notes

- note that you have access to context7 mcp tool. You that to check workos documentation and best practices and code implementation if when possible. You can do the same with convex as well, it has uptodate documentation of their api
- note that you have access to convex mcp as well as context7 mcptools as part of your working tools to get more context and direction to work. Make sure to use it when needed.
- after making changes or update, you need to test it first and when its successful that's when you can commit to git. Don't create git right afte making an edit.
- use the workos credentials in the .env file with curl to interact with workos API during this development when necessary.
- note add user authentication is strictly using workos and workos authkit, under no circumstance should you use convex auth without approval
- I always prefare to execute npm run dev manually by myself. Always ask me to do it and don't try to do it by yourself.
- If a bug is reported to you to fix, take your time. Think plan a solution step by step before implementing.
