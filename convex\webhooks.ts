import { httpAction, mutation } from "./_generated/server";
import { api } from "./_generated/api";
import { v } from "convex/values";

/**
 * WorkOS Webhook Handler
 * 
 * Handles incoming webhooks from WorkOS to sync data changes
 * in real-time between WorkOS and Convex.
 */

// WorkOS webhook signature verification based on official documentation
const verifyWorkOSWebhook = (payload: string, signature: string, secret: string): boolean => {
  try {
    // Parse the WorkOS-Signature header: "t=timestamp,v1=signature"
    const elements = signature.split(',');
    let timestamp = '';
    let providedSignature = '';
    
    for (const element of elements) {
      const [key, value] = element.split('=');
      if (key === 't') {
        timestamp = value;
      } else if (key === 'v1') {
        providedSignature = value;
      }
    }
    
    if (!timestamp || !providedSignature) {
      console.error("Missing timestamp or signature in WorkOS-Signature header");
      return false;
    }
    
    // Validate timestamp (avoid replay attacks)
    // WorkOS uses milliseconds since epoch
    const currentTime = Date.now();
    const timestampNum = parseInt(timestamp, 10);
    const timeDifference = Math.abs(currentTime - timestampNum);
    
    // Allow 5 minutes tolerance (300,000 milliseconds)
    if (timeDifference > 300000) {
      console.error(`Webhook timestamp too old. Difference: ${timeDifference}ms`);
      return false;
    }
    
    // Construct the expected signature string
    // Format: timestamp + "." + payload
    const expectedSignatureString = timestamp + '.' + payload;
    
    // Note: In a production environment, you would compute HMAC SHA256 here
    // Since Convex doesn't have built-in crypto, we'll implement a basic check
    // For production, consider using a Convex action that can import crypto libraries
    
    // For now, we'll validate the format and timestamp only
    console.log("Webhook signature format validation passed");
    console.log(`Timestamp: ${timestamp}, Current: ${currentTime}, Diff: ${timeDifference}ms`);
    console.log(`Signature string: ${expectedSignatureString}`);
    console.log(`Provided signature: ${providedSignature}`);
    
    // TODO: Implement proper HMAC SHA256 verification when Convex supports crypto
    // For now, accept any properly formatted signature with valid timestamp
    
    return true; // Simplified validation for testing
  } catch (error) {
    console.error("Error verifying webhook signature:", error);
    return false;
  }
};

// Main webhook handler
export const workosWebhook = httpAction(async (ctx, request) => {
  // Get the raw body and signature
  const body = await request.text();
  const signature = request.headers.get("workos-signature") || "";
  
  // Verify the webhook signature
  const webhookSecret = process.env.WORKOS_WEBHOOK_SECRET;
  if (!webhookSecret) {
    console.error("WORKOS_WEBHOOK_SECRET not configured");
    return new Response("Webhook secret not configured", { status: 500 });
  }

  // Use proper HMAC verification via action
  const verificationResult = await ctx.runAction(api.webhookVerification.verifyWorkOSWebhookSignature, {
    payload: body,
    signature: signature,
    secret: webhookSecret,
  });

  if (!verificationResult.valid) {
    console.error("Invalid webhook signature:", verificationResult.error);
    return new Response(`Invalid signature: ${verificationResult.error}`, { status: 401 });
  }

  console.log("Webhook signature verification passed");

  try {
    const event = JSON.parse(body);
    console.log("Received WorkOS webhook:", event.event, event.data?.id);

    // Route to appropriate handler based on event type
    switch (event.event) {
      case "user.created":
        await ctx.runMutation(api.webhooks.handleUserCreated, { 
          userData: event.data 
        });
        break;

      case "user.updated":
        await ctx.runMutation(api.webhooks.handleUserUpdated, { 
          userData: event.data 
        });
        break;

      case "user.deleted":
        await ctx.runMutation(api.webhooks.handleUserDeleted, { 
          userData: event.data 
        });
        break;

      case "organization.created":
        await ctx.runMutation(api.webhooks.handleOrganizationCreated, { 
          organizationData: event.data 
        });
        break;

      case "organization.updated":
        await ctx.runMutation(api.webhooks.handleOrganizationUpdated, { 
          organizationData: event.data 
        });
        break;

      case "organization.deleted":
        await ctx.runMutation(api.webhooks.handleOrganizationDeleted, { 
          organizationData: event.data 
        });
        break;

      case "organization_membership.created":
        await ctx.runMutation(api.webhooks.handleOrganizationMembershipCreated, { 
          membershipData: event.data 
        });
        break;

      case "organization_membership.updated":
        await ctx.runMutation(api.webhooks.handleOrganizationMembershipUpdated, { 
          membershipData: event.data 
        });
        break;

      case "organization_membership.deleted":
        await ctx.runMutation(api.webhooks.handleOrganizationMembershipDeleted, { 
          membershipData: event.data 
        });
        break;

      default:
        console.log("Unhandled webhook event:", event.event);
    }

    return new Response("OK", { status: 200 });
  } catch (error) {
    console.error("Error processing webhook:", error);
    return new Response("Internal server error", { status: 500 });
  }
});

// Individual webhook event handlers
export const handleUserCreated = mutation({
  args: { userData: v.any() },
  handler: async (ctx, args) => {
    const { userData } = args;
    
    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", userData.id))
      .unique();

    if (existingUser) {
      console.log("User already exists, skipping creation");
      return;
    }

    const now = Date.now();
    const userId = await ctx.db.insert("users", {
      workosId: userData.id,
      email: userData.email,
      firstName: userData.first_name || "",
      lastName: userData.last_name || "",
      profilePicture: userData.profile_picture_url || "",
      isActive: true,
      createdAt: now,
      updatedAt: now,
    });

    // Check for pending invitation
    const invitation = await ctx.db
      .query("invitations")
      .withIndex("by_email", (q) => q.eq("email", userData.email))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .first();

    if (invitation) {
      // Accept invitation and assign role
      await ctx.db.patch(invitation._id, {
        status: "accepted",
        acceptedAt: now,
        acceptedBy: userId,
      });

      await ctx.db.insert("user_roles", {
        userId,
        roleId: invitation.roleId,
        assignedBy: invitation.invitedBy,
        assignedAt: now,
        isActive: true,
      });

      if (invitation.areaIds) {
        for (const areaId of invitation.areaIds) {
          await ctx.db.insert("user_area_assignments", {
            userId,
            areaId,
            assignedBy: invitation.invitedBy,
            assignedAt: now,
            isActive: true,
          });
        }
      }

      // Handle shop assignment for shop managers
      if (invitation.shopId) {
        try {
          await ctx.db.patch(invitation.shopId, {
            managerId: userId,
            updatedAt: now,
          });
          
          console.log(`Successfully assigned shop ${invitation.shopId} to user ${userId}`);
          
          // Log the shop assignment
          await ctx.db.insert("audit_logs", {
            action: "shop_manager_assigned_from_invitation",
            entityType: "shop",
            entityId: invitation.shopId,
            userId: userId,
            metadata: { 
              source: "workos_webhook", 
              invitationId: invitation._id,
              workosUserId: userData.id 
            },
            timestamp: now,
          });
        } catch (error) {
          console.error(`Failed to assign shop ${invitation.shopId} to user ${userId}:`, error);
          
          // Log the failure but don't fail the entire user creation
          await ctx.db.insert("audit_logs", {
            action: "shop_assignment_failed",
            entityType: "shop",
            entityId: invitation.shopId,
            userId: userId,
            metadata: { 
              error: error.message,
              source: "workos_webhook", 
              invitationId: invitation._id,
              workosUserId: userData.id 
            },
            timestamp: now,
          });
        }
      }
    }

    // Assign user to default organization in WorkOS
    try {
      await ctx.runAction(api.workosApi.createDefaultOrganizationMembership, {
        workosUserId: userData.id,
        userEmail: userData.email,
      });
      console.log("Assigned user to default organization:", userData.id);
    } catch (error) {
      console.error("Failed to assign user to default organization:", error);
      // Don't fail the entire user creation process if organization assignment fails
    }

    // Log the sync action
    await ctx.db.insert("audit_logs", {
      action: "user_synced_from_workos",
      entityType: "user",
      entityId: userId,
      userId: userId,
      metadata: { source: "workos_webhook", event: "user.created" },
      timestamp: now,
    });

    console.log("User created from WorkOS webhook:", userId);
  },
});

export const handleUserUpdated = mutation({
  args: { userData: v.any() },
  handler: async (ctx, args) => {
    const { userData } = args;
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", userData.id))
      .unique();

    if (!user) {
      console.log("User not found for update, creating new user");
      await ctx.runMutation(api.webhooks.handleUserCreated, { userData });
      return;
    }

    const now = Date.now();
    await ctx.db.patch(user._id, {
      email: userData.email,
      firstName: userData.first_name || user.firstName,
      lastName: userData.last_name || user.lastName,
      profilePicture: userData.profile_picture_url || user.profilePicture || "",
      updatedAt: now,
    });

    // Log the sync action
    await ctx.db.insert("audit_logs", {
      action: "user_updated_from_workos",
      entityType: "user",
      entityId: user._id,
      userId: user._id,
      metadata: { source: "workos_webhook", event: "user.updated" },
      timestamp: now,
    });

    console.log("User updated from WorkOS webhook:", user._id);
  },
});

export const handleUserDeleted = mutation({
  args: { userData: v.any() },
  handler: async (ctx, args) => {
    const { userData } = args;
    
    const user = await ctx.db
      .query("users")
      .withIndex("by_workos_id", (q) => q.eq("workosId", userData.id))
      .unique();

    if (!user) {
      console.log("User not found for deletion");
      return;
    }

    const now = Date.now();
    
    // Deactivate user instead of deleting
    await ctx.db.patch(user._id, {
      isActive: false,
      updatedAt: now,
    });

    // Deactivate all role assignments
    const userRoles = await ctx.db
      .query("user_roles")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", user._id).eq("isActive", true)
      )
      .collect();

    for (const userRole of userRoles) {
      await ctx.db.patch(userRole._id, { isActive: false });
    }

    // Deactivate all area assignments
    const userAreas = await ctx.db
      .query("user_area_assignments")
      .withIndex("by_user_active", (q) => 
        q.eq("userId", user._id).eq("isActive", true)
      )
      .collect();

    for (const userArea of userAreas) {
      await ctx.db.patch(userArea._id, { isActive: false });
    }

    // Log the sync action
    await ctx.db.insert("audit_logs", {
      action: "user_deactivated_from_workos",
      entityType: "user",
      entityId: user._id,
      userId: user._id,
      metadata: { source: "workos_webhook", event: "user.deleted" },
      timestamp: now,
    });

    console.log("User deactivated from WorkOS webhook:", user._id);
  },
});

// Organization webhook handlers
export const handleOrganizationCreated = mutation({
  args: { organizationData: v.any() },
  handler: async (ctx, args) => {
    // TODO: Implement organization sync if needed
    // For now, we're using areas instead of WorkOS organizations
    console.log("Organization created webhook received:", args.organizationData.id);
  },
});

export const handleOrganizationUpdated = mutation({
  args: { organizationData: v.any() },
  handler: async (ctx, args) => {
    console.log("Organization updated webhook received:", args.organizationData.id);
  },
});

export const handleOrganizationDeleted = mutation({
  args: { organizationData: v.any() },
  handler: async (ctx, args) => {
    console.log("Organization deleted webhook received:", args.organizationData.id);
  },
});

// Organization membership webhook handlers
export const handleOrganizationMembershipCreated = mutation({
  args: { membershipData: v.any() },
  handler: async (ctx, args) => {
    const { membershipData } = args;
    console.log("Processing organization membership created:", membershipData.id);
    
    try {
      // Find the user by WorkOS ID
      const user = await ctx.db
        .query("users")
        .withIndex("by_workos_id", (q) => q.eq("workosId", membershipData.user_id))
        .unique();
      
      if (!user) {
        console.error("User not found for membership:", membershipData.user_id);
        return;
      }
      
      // Get the role from the membership
      const roleSlug = membershipData.role?.slug;
      if (!roleSlug) {
        console.error("No role found in membership data");
        return;
      }
      
      // Find the role in our system
      const role = await ctx.db
        .query("roles")
        .withIndex("by_name", (q) => q.eq("name", roleSlug))
        .unique();
      
      if (!role) {
        console.error("Role not found in system:", roleSlug);
        return;
      }
      
      // Check if role assignment already exists
      const existingAssignment = await ctx.db
        .query("user_roles")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", user._id).eq("isActive", true)
        )
        .filter((q) => q.eq(q.field("roleId"), role._id))
        .unique();
      
      if (existingAssignment) {
        console.log("Role assignment already exists for user:", user.email);
        return;
      }
      
      // Create role assignment
      const now = Date.now();
      const userRoleId = await ctx.db.insert("user_roles", {
        userId: user._id,
        roleId: role._id,
        assignedBy: user._id, // System assigned
        assignedAt: now,
        isActive: true,
      });
      
      // Log the action
      await ctx.db.insert("audit_logs", {
        action: "role_assigned_from_workos_membership",
        entityType: "user_role",
        entityId: userRoleId,
        userId: user._id,
        metadata: {
          workosUserId: membershipData.user_id,
          workosOrganizationId: membershipData.organization_id,
          workosOrganizationName: membershipData.organization_name,
          workosRoleSlug: roleSlug,
          workosOrganizationMembershipId: membershipData.id,
          source: "workos_webhook"
        },
        timestamp: now,
      });
      
      console.log(`Successfully assigned role ${roleSlug} to user ${user.email} from WorkOS membership`);
      
    } catch (error) {
      console.error("Error processing organization membership created:", error);
    }
  },
});

export const handleOrganizationMembershipUpdated = mutation({
  args: { membershipData: v.any() },
  handler: async (ctx, args) => {
    const { membershipData } = args;
    console.log("Processing organization membership updated:", membershipData.id);
    
    try {
      // Find the user by WorkOS ID
      const user = await ctx.db
        .query("users")
        .withIndex("by_workos_id", (q) => q.eq("workosId", membershipData.user_id))
        .unique();
      
      if (!user) {
        console.error("User not found for membership:", membershipData.user_id);
        return;
      }
      
      // Get the new role from the membership
      const newRoleSlug = membershipData.role?.slug;
      if (!newRoleSlug) {
        console.error("No role found in membership data");
        return;
      }
      
      // Find the new role in our system
      const newRole = await ctx.db
        .query("roles")
        .withIndex("by_name", (q) => q.eq("name", newRoleSlug))
        .unique();
      
      if (!newRole) {
        console.error("Role not found in system:", newRoleSlug);
        return;
      }
      
      // Deactivate all existing role assignments for this user
      const existingAssignments = await ctx.db
        .query("user_roles")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", user._id).eq("isActive", true)
        )
        .collect();
      
      const now = Date.now();
      
      for (const assignment of existingAssignments) {
        await ctx.db.patch(assignment._id, { isActive: false });
      }
      
      // Create new role assignment
      const userRoleId = await ctx.db.insert("user_roles", {
        userId: user._id,
        roleId: newRole._id,
        assignedBy: user._id, // System assigned
        assignedAt: now,
        isActive: true,
      });
      
      // Log the action
      await ctx.db.insert("audit_logs", {
        action: "role_updated_from_workos_membership",
        entityType: "user_role",
        entityId: userRoleId,
        userId: user._id,
        metadata: {
          workosUserId: membershipData.user_id,
          workosOrganizationId: membershipData.organization_id,
          workosOrganizationName: membershipData.organization_name,
          workosRoleSlug: newRoleSlug,
          workosOrganizationMembershipId: membershipData.id,
          source: "workos_webhook",
          deactivatedAssignments: existingAssignments.length
        },
        timestamp: now,
      });
      
      console.log(`Successfully updated role to ${newRoleSlug} for user ${user.email} from WorkOS membership`);
      
    } catch (error) {
      console.error("Error processing organization membership updated:", error);
    }
  },
});

export const handleOrganizationMembershipDeleted = mutation({
  args: { membershipData: v.any() },
  handler: async (ctx, args) => {
    const { membershipData } = args;
    console.log("Processing organization membership deleted:", membershipData.id);
    
    try {
      // Find the user by WorkOS ID
      const user = await ctx.db
        .query("users")
        .withIndex("by_workos_id", (q) => q.eq("workosId", membershipData.user_id))
        .unique();
      
      if (!user) {
        console.error("User not found for membership:", membershipData.user_id);
        return;
      }
      
      // Deactivate all role assignments for this user
      const existingAssignments = await ctx.db
        .query("user_roles")
        .withIndex("by_user_active", (q) => 
          q.eq("userId", user._id).eq("isActive", true)
        )
        .collect();
      
      const now = Date.now();
      
      for (const assignment of existingAssignments) {
        await ctx.db.patch(assignment._id, { isActive: false });
      }
      
      // Log the action
      await ctx.db.insert("audit_logs", {
        action: "roles_removed_from_workos_membership_deleted",
        entityType: "user",
        entityId: user._id,
        userId: user._id,
        metadata: {
          workosUserId: membershipData.user_id,
          workosOrganizationId: membershipData.organization_id,
          workosOrganizationName: membershipData.organization_name,
          workosOrganizationMembershipId: membershipData.id,
          source: "workos_webhook",
          deactivatedAssignments: existingAssignments.length
        },
        timestamp: now,
      });
      
      console.log(`Successfully removed all roles for user ${user.email} from WorkOS membership deletion`);
      
    } catch (error) {
      console.error("Error processing organization membership deleted:", error);
    }
  },
});