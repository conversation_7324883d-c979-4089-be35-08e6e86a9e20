import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  // User management
  users: defineTable({
    workosId: v.string(),
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    phone: v.optional(v.string()),
    profilePicture: v.optional(v.string()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_workos_id", ["workosId"])
    .index("by_email", ["email"])
    .index("by_active", ["isActive"]),

  // Role-based access control
  roles: defineTable({
    name: v.string(),
    description: v.string(),
    permissions: v.array(v.string()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_name", ["name"])
    .index("by_active", ["isActive"]),

  user_roles: defineTable({
    userId: v.id("users"),
    roleId: v.id("roles"),
    assignedBy: v.id("users"),
    assignedAt: v.number(),
    isActive: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_role", ["roleId"])
    .index("by_user_active", ["userId", "isActive"])
    .index("by_role_active", ["roleId", "isActive"]),

  // Geographic organization
  areas: defineTable({
    name: v.string(),
    description: v.optional(v.string()),
    workosOrganizationId: v.optional(v.string()), // WorkOS organization ID for sync
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
    createdBy: v.id("users"),
  })
    .index("by_name", ["name"])
    .index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"])
    .index("by_workos_org_id", ["workosOrganizationId"]),

  shops: defineTable({
    name: v.string(),
    code: v.string(),
    areaId: v.id("areas"),
    managerId: v.optional(v.id("users")),
    address: v.optional(v.string()),
    phone: v.optional(v.string()),
    isActive: v.boolean(),
    createdAt: v.number(),
    updatedAt: v.number(),
    createdBy: v.id("users"),
  })
    .index("by_name", ["name"])
    .index("by_code", ["code"])
    .index("by_area", ["areaId"])
    .index("by_manager", ["managerId"])
    .index("by_active", ["isActive"])
    .index("by_area_active", ["areaId", "isActive"])
    .index("by_created_by", ["createdBy"]),

  user_area_assignments: defineTable({
    userId: v.id("users"),
    areaId: v.id("areas"),
    assignedBy: v.id("users"),
    assignedAt: v.number(),
    isActive: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_area", ["areaId"])
    .index("by_user_active", ["userId", "isActive"])
    .index("by_area_active", ["areaId", "isActive"]),

  // Core business entities
  requests: defineTable({
    ticketNumber: v.string(),
    amount: v.number(),
    paymentMethod: v.union(v.literal("mobile_money"), v.literal("bank_transfer")),
    status: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("resubmitted"),
      v.literal("paid"),
      v.literal("cancelled")
    ),
    priority: v.union(v.literal("low"), v.literal("medium"), v.literal("high")),
    
    // Customer information
    customerName: v.string(),
    customerPhone: v.string(),
    customerIdNumber: v.optional(v.string()),
    
    // Payment details
    mobileMoneyNumber: v.optional(v.string()),
    mobileMoneyProvider: v.optional(v.string()),
    bankName: v.optional(v.string()),
    accountNumber: v.optional(v.string()),
    accountName: v.optional(v.string()),
    
    // Request metadata
    shopId: v.id("shops"),
    areaId: v.id("areas"),
    requestedBy: v.id("users"),
    workosUserId: v.optional(v.string()), // WorkOS user ID for comparison
    requestedAt: v.number(),
    
    // Approval workflow
    approvedBy: v.optional(v.id("users")),
    approvedAt: v.optional(v.number()),
    rejectedBy: v.optional(v.id("users")),
    rejectedAt: v.optional(v.number()),
    rejectionReason: v.optional(v.string()),
    
    // Additional fields
    notes: v.optional(v.string()),
    ticketImageId: v.optional(v.id("_storage")),
    isResubmission: v.boolean(),
    originalRequestId: v.optional(v.id("requests")),
    resubmissionHistory: v.optional(v.array(v.object({
      resubmittedAt: v.number(),
      resubmittedBy: v.id("users"),
      previousStatus: v.string(),
      reason: v.optional(v.string()),
    }))),
    
    // Timestamps
    createdAt: v.number(),
    updatedAt: v.number(),
  })
    .index("by_ticket_number", ["ticketNumber"])
    .index("by_status", ["status"])
    .index("by_shop", ["shopId"])
    .index("by_area", ["areaId"])
    .index("by_requested_by", ["requestedBy"])
    .index("by_approved_by", ["approvedBy"])
    .index("by_payment_method", ["paymentMethod"])
    .index("by_priority", ["priority"])
    .index("by_shop_status", ["shopId", "status"])
    .index("by_area_status", ["areaId", "status"])
    .index("by_status_created", ["status", "createdAt"])
    .index("by_requested_by_status", ["requestedBy", "status"])
    .index("by_is_resubmission", ["isResubmission"])
    .index("by_original_request", ["originalRequestId"])
    .index("by_created_at", ["createdAt"])
    .index("by_updated_at", ["updatedAt"]),

  // Request status history tracking
  request_status_history: defineTable({
    requestId: v.id("requests"),
    fromStatus: v.optional(v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("resubmitted"),
      v.literal("paid"),
      v.literal("cancelled")
    )),
    toStatus: v.union(
      v.literal("pending"),
      v.literal("approved"),
      v.literal("rejected"),
      v.literal("resubmitted"),
      v.literal("paid"),
      v.literal("cancelled")
    ),
    changedBy: v.id("users"),
    changedAt: v.number(),
    reason: v.optional(v.string()),
    metadata: v.optional(v.any()), // For additional context like approval notes, rejection reasons, etc.
  })
    .index("by_request", ["requestId"])
    .index("by_request_date", ["requestId", "changedAt"])
    .index("by_user", ["changedBy"])
    .index("by_status", ["toStatus"])
    .index("by_changed_at", ["changedAt"]),

  // Notifications system
  notifications: defineTable({
    type: v.union(
      v.literal("request_submitted"),
      v.literal("request_approved"),
      v.literal("request_rejected"),
      v.literal("request_resubmitted"),
      v.literal("user_invited"),
      v.literal("system_alert")
    ),
    title: v.string(),
    message: v.string(),
    userId: v.id("users"),
    relatedEntityType: v.optional(v.union(
      v.literal("request"),
      v.literal("user"),
      v.literal("shop"),
      v.literal("area")
    )),
    relatedEntityId: v.optional(v.string()),
    isRead: v.boolean(),
    readAt: v.optional(v.number()),
    createdAt: v.number(),
    expiresAt: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_user_read", ["userId", "isRead"])
    .index("by_type", ["type"])
    .index("by_created_at", ["createdAt"])
    .index("by_expires_at", ["expiresAt"])
    .index("by_related_entity", ["relatedEntityType", "relatedEntityId"]),

  // Application settings
  settings: defineTable({
    key: v.string(),
    value: v.any(),
    description: v.optional(v.string()),
    category: v.string(),
    isSystem: v.boolean(),
    updatedBy: v.optional(v.id("users")), // Make optional for system initialization
    updatedAt: v.number(),
    createdAt: v.number(),
  })
    .index("by_key", ["key"])
    .index("by_category", ["category"])
    .index("by_is_system", ["isSystem"])
    .index("by_updated_by", ["updatedBy"]),

  // Audit logging
  audit_logs: defineTable({
    action: v.string(),
    entityType: v.string(),
    entityId: v.string(),
    userId: v.id("users"),
    oldValues: v.optional(v.any()),
    newValues: v.optional(v.any()),
    metadata: v.optional(v.any()),
    ipAddress: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    timestamp: v.number(),
  })
    .index("by_user", ["userId"])
    .index("by_entity", ["entityType", "entityId"])
    .index("by_action", ["action"])
    .index("by_timestamp", ["timestamp"])
    .index("by_user_timestamp", ["userId", "timestamp"]),

  // File metadata (for ticket images, avatars, etc.)
  file_metadata: defineTable({
    storageId: v.id("_storage"),
    filename: v.string(),
    contentType: v.string(),
    size: v.number(),
    uploadedBy: v.id("users"),
    relatedEntityType: v.optional(v.string()),
    relatedEntityId: v.optional(v.string()),
    isPublic: v.boolean(),
    uploadedAt: v.number(),
  })
    .index("by_storage_id", ["storageId"])
    .index("by_uploaded_by", ["uploadedBy"])
    .index("by_related_entity", ["relatedEntityType", "relatedEntityId"])
    .index("by_uploaded_at", ["uploadedAt"]),

  // User invitations
  invitations: defineTable({
    email: v.string(),
    roleId: v.id("roles"),
    areaIds: v.optional(v.array(v.id("areas"))),
    shopId: v.optional(v.id("shops")), // For shop manager assignments
    invitedBy: v.id("users"),
    invitedAt: v.number(),
    expiresAt: v.number(),
    acceptedAt: v.optional(v.number()),
    acceptedBy: v.optional(v.id("users")),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("expired"),
      v.literal("cancelled"),
      v.literal("failed")
    ),
    token: v.string(),
    metadata: v.optional(v.any()),
  })
    .index("by_email", ["email"])
    .index("by_token", ["token"])
    .index("by_invited_by", ["invitedBy"])
    .index("by_status", ["status"])
    .index("by_expires_at", ["expiresAt"])
    .index("by_role", ["roleId"]),

});
