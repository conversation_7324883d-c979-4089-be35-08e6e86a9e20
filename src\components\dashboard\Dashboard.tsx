import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, Card<PERSON>itle, CardFooter, CardDescription } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

import ActivityTimeline from './ActivityTimeline';
import {
  CheckCircle,
  AlertCircle,
  ArrowRight,
  CalendarIcon,
  ClipboardPen,
  RefreshCw,
  PlusCircle,
  MoreVertical,
  Clock,
  X,
  Check,
  XCircle,
  Eye,
  User,
  MapPin
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Link, useNavigate } from 'react-router-dom';
// Removed store imports - now using Convex queries directly
import { useAuth } from '@/lib/auth-context';
import { canCreateRequests } from '@/lib/auth';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../../convex/_generated/api';

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format, subMonths, endOfDay, subDays, parseISO, isValid } from "date-fns";
import { cn } from "@/lib/utils";
import { DateRange } from "react-day-picker";

import { useToast } from "@/components/ui/use-toast";
import { formatAmount } from "@/lib/currency";





// Robust date parsing utility to handle different date formats and timezones
const parseRequestDate = (dateInput: any): Date => {
  if (!dateInput) return new Date();

  // If it's already a Date object
  if (dateInput instanceof Date) {
    return isValid(dateInput) ? dateInput : new Date();
  }

  // If it's a string, try different parsing methods
  if (typeof dateInput === 'string') {
    // Try ISO string parsing first
    if (dateInput.includes('T') || dateInput.includes('Z')) {
      const parsed = parseISO(dateInput);
      if (isValid(parsed)) return parsed;
    }

    // Try standard Date parsing
    const parsed = new Date(dateInput);
    if (isValid(parsed)) return parsed;
  }

  // If it's a number (timestamp)
  if (typeof dateInput === 'number') {
    const parsed = new Date(dateInput);
    if (isValid(parsed)) return parsed;
  }

  // Fallback to current date
  console.warn('Invalid date format:', dateInput, 'using current date');
  return new Date();
};

// Normalize status values to handle case variations
const normalizeStatus = (status: any): string => {
  if (!status || typeof status !== 'string') return 'pending';
  return status.toLowerCase().trim();
};

// Robust date filtering function
const isDateInRange = (requestDate: Date, fromDate?: Date, toDate?: Date): boolean => {
  if (!fromDate || !toDate) return true;

  // Ensure we're comparing dates in the same timezone (local)
  // This normalizes all dates to midnight local time for accurate day-based comparison
  const normalizedRequestDate = new Date(requestDate.getFullYear(), requestDate.getMonth(), requestDate.getDate());
  const normalizedFromDate = new Date(fromDate.getFullYear(), fromDate.getMonth(), fromDate.getDate());
  const normalizedToDate = new Date(toDate.getFullYear(), toDate.getMonth(), toDate.getDate());

  return normalizedRequestDate >= normalizedFromDate && normalizedRequestDate <= normalizedToDate;
};



const Dashboard: React.FC = React.memo(() => {
  const navigate = useNavigate();
  const { toast } = useToast();
  // Removed store usage - now using Convex queries directly
  const { user, appUser, userWithRoles } = useAuth();

  // Use public queries that work with WorkOS AuthKit
  const publicRequests = useQuery(
    api.publicQueries.getRequestsForUser,
    user?.id ? { workosUserId: user.id } : "skip"
  );
  const publicStats = useQuery(
    api.publicQueries.getRequestStatsForUser,
    user?.id ? { workosUserId: user.id } : "skip"
  );

  // Use Convex mutations for approve/reject
  const convexApproveRequest = useMutation(api.requests.approveRequest);
  const convexRejectRequest = useMutation(api.requests.rejectRequest);

  // Use public data if available, fallback to empty data
  const effectiveRequests = publicRequests || [];
  const effectiveStats = publicStats || {
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
    paid: 0,
  };

  // Debug logging to see what data we're getting
  console.log('Dashboard Debug:', {
    user: user?.id,
    publicRequests: publicRequests?.length,
    publicStats,
    effectiveRequests: effectiveRequests.length,
    effectiveStats
  });

  // Essential state only
  const [dateRange, setDateRange] = useState<string>("all_time");
  const [calendarMonth, setCalendarMonth] = useState<Date>(new Date());
  const [dateRangeValue, setDateRangeValue] = useState<DateRange | undefined>(undefined);
  const [tempDateRange, setTempDateRange] = useState<DateRange | undefined>();
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [pendingCount, setPendingCount] = useState(0);
  const [approvedCount, setApprovedCount] = useState(0);
  const [rejectedCount, setRejectedCount] = useState(0);
  const [resubmittedCount, setResubmittedCount] = useState(0);
  const [isRefreshing, setIsRefreshing] = useState(false);



  // Calculate counts from Convex requests data
  useEffect(() => {
    if (!effectiveRequests || effectiveRequests.length === 0) {
      setPendingCount(0);
      setApprovedCount(0);
      setRejectedCount(0);
      setResubmittedCount(0);
      return;
    }

    const filtered = effectiveRequests.filter(request => {
      const requestDate = parseRequestDate(request.createdAt);
      return isDateInRange(requestDate, dateRangeValue?.from, dateRangeValue?.to);
    });

    setPendingCount(filtered.filter(r => {
      const status = normalizeStatus(r.status);
      return status === 'pending';
    }).length);
    setApprovedCount(filtered.filter(r => normalizeStatus(r.status) === 'approved').length);
    setRejectedCount(filtered.filter(r => normalizeStatus(r.status) === 'rejected').length);
    setResubmittedCount(filtered.filter(r => normalizeStatus(r.status) === 'resubmitted').length);
  }, [effectiveRequests, dateRangeValue]);

  // Note: Data fetching is now handled by Convex queries automatically

  // Handle errors - Convex queries handle errors automatically
  // If needed, we can add error handling for specific cases here



  // Refresh function - Convex queries auto-refresh, so we just show a toast
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      // Convex queries automatically refresh, so we just simulate a refresh
      await new Promise(resolve => setTimeout(resolve, 500));
      toast({
        title: "Dashboard refreshed",
        description: "All data has been updated successfully.",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Refresh failed",
        description: "Failed to refresh dashboard data. Please try again.",
      });
    } finally {
      setIsRefreshing(false);
    }
  };

  // Handle approve request
  const handleApprove = async (requestId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await convexApproveRequest({ 
        requestId, 
        approvalReason: "Quick approval from dashboard",
        workosUserId: user?.id || ""
      });
      toast({
        title: "Request approved",
        description: "The cash request has been approved successfully.",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Approval failed",
        description: "Failed to approve the request. Please try again.",
      });
    }
  };

  // Handle reject request
  const handleReject = async (requestId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await convexRejectRequest({ 
        requestId, 
        rejectionReason: "Quick rejection from dashboard",
        workosUserId: user?.id || ""
      });
      toast({
        title: "Request rejected",
        description: "The cash request has been rejected.",
      });
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Rejection failed",
        description: "Failed to reject the request. Please try again.",
      });
    }
  };



  const showCreateRequestButton = user && canCreateRequests(userWithRoles?.roles?.map((role: any) => role.name) || []);

  // Show loading state if user is authenticated but role data is not loaded yet
  if (user && !userWithRoles) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    );
  }



  const handleDateRangeChange = (value: string) => {
    setDateRange(value as any);

    const today = new Date();
    let newFrom: Date | undefined;
    let newTo: Date | undefined;

    if (value === "all_time") {
      // All Time: No date filtering
      setDateRangeValue(undefined);
      return;
    } else if (value === "today") {
      // Set start of today (00:00:00)
      newFrom = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
      // Set end of today (23:59:59)
      newTo = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    } else if (value === "yesterday") {
      const yesterday = subDays(today, 1);
      // Set start of yesterday
      newFrom = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0);
      // Set end of yesterday
      newTo = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
    } else if (value === "week") {
      newFrom = subDays(today, 7);
      newTo = endOfDay(today);
    } else if (value === "last_week") {
      newFrom = subDays(today, 14);
      newTo = subDays(today, 7);
    } else if (value === "month") {
      // This Month: Start of current month to today
      newFrom = new Date(today.getFullYear(), today.getMonth(), 1, 0, 0, 0);
      newTo = endOfDay(today);
    } else if (value === "last_month") {
      // Last Month: Full previous month
      const lastMonth = subMonths(today, 1);
      newFrom = new Date(lastMonth.getFullYear(), lastMonth.getMonth(), 1, 0, 0, 0);
      newTo = new Date(today.getFullYear(), today.getMonth(), 0, 23, 59, 59); // Last day of previous month
    }

    if (newFrom) {
      setCalendarMonth(newFrom);
      setDateRangeValue({ from: newFrom, to: newTo });
    }
  };

  // Reset filter function
  const handleResetFilter = () => {
    setDateRange("all_time");
    setDateRangeValue(undefined);
    setTempDateRange(undefined);
    setIsCalendarOpen(false);
  };

  const formatDateRange = (range: DateRange | undefined) => {
    if (!range) return "Select date range";
    if (!range.from) return "Select date range";
    if (!range.to) return format(range.from, "MMM dd, yyyy");
    return `${format(range.from, "MMM dd, yyyy")} - ${format(range.to, "MMM dd, yyyy")}`;
  };



  // Show loading state for Convex queries
  if (!publicRequests && !publicStats && user?.id) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-muted-foreground" />
          <p className="text-muted-foreground">Loading dashboard data...</p>
        </div>
      </div>
    );
  }

  // Show error state if no data - removed since we want to show the dashboard even with 0 requests
  // The dashboard will show 0 counts which is the correct behavior

  return (
    <div className="animate-fade-in">
      <div className="pb-4 mb-6 relative before:content-[''] before:absolute before:bottom-0 before:left-0 before:right-0 before:h-px before:bg-gray-200 before:-ml-[100vw] before:w-[200vw]">
        <div className="px-6 flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
            <p className="text-muted-foreground mt-1">
              Overview of cash requests and approvals
            </p>
          </div>

          <div className="flex gap-2 flex-col sm:flex-row">
            <Select value={dateRange} onValueChange={handleDateRangeChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select period" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all_time">All Time</SelectItem>
                <SelectItem value="today">Today</SelectItem>
                <SelectItem value="yesterday">Yesterday</SelectItem>
                <SelectItem value="week">Last 7 Days</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="last_month">Last Month</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>

            {dateRange === "custom" && (
              <Popover open={isCalendarOpen} onOpenChange={setIsCalendarOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-[300px] pl-3 text-left font-normal",
                      !dateRangeValue && "text-muted-foreground"
                    )}
                    onClick={() => setIsCalendarOpen(true)}
                  >
                    {formatDateRange(dateRangeValue)}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <div className="p-0">
                    <Calendar
                      mode="range"
                      selected={tempDateRange || dateRangeValue}
                      onSelect={(selectedRange) => {
                        setTempDateRange(selectedRange);
                        if (selectedRange?.from) {
                          setCalendarMonth(selectedRange.from);
                        }
                      }}
                      defaultMonth={calendarMonth}
                      initialFocus
                      numberOfMonths={2}
                    />
                    <div className="flex items-center justify-end gap-2 p-3 border-t">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setTempDateRange(undefined);
                          setIsCalendarOpen(false);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        size="sm"
                        onClick={() => {
                          if (tempDateRange?.from) {
                            setDateRangeValue(tempDateRange);
                            setTempDateRange(undefined);
                            setIsCalendarOpen(false);
                          }
                        }}
                        disabled={!tempDateRange?.from}
                      >
                        Apply
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            )}

            {/* Reset Filter Button - Only show when not on "All Time" */}
            {dateRange !== "all_time" && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleResetFilter}
                className="flex items-center gap-2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4" />
                Reset Filter
              </Button>
            )}

            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              {isRefreshing ? 'Refreshing...' : 'Refresh'}
            </Button>

            {showCreateRequestButton && (
              <Button asChild className="self-start md:self-auto">
                <Link to="/create-request" className="flex items-center gap-2">
                  <PlusCircle className="h-4 w-4" />
                  <span>New Request</span>
                </Link>
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="px-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
          <Link
            to={{
              pathname: "/requests",
              search: "?status=pending"
            }}
            className="block"
          >
            <Card className="overflow-hidden transition-all duration-200 hover:shadow-md cursor-pointer">
              <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                <CardTitle className="text-sm font-medium text-muted-foreground">Pending</CardTitle>
                <MoreVertical className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="mr-4 rounded-full bg-[rgba(255,171,0,0.12)] p-2">
                    <Clock className="h-6 w-6 text-[#B76E00]" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{pendingCount}</div>
                    <p className="text-xs text-muted-foreground">Pending Requests</p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="p-0">
                <Progress
                  className="h-1 rounded-none bg-[#FFE5BF]"
                  indicatorClassName="bg-[#B76E00]"
                  value={pendingCount ? (pendingCount / (pendingCount + approvedCount + rejectedCount)) * 100 : 0}
                />
              </CardFooter>
            </Card>
          </Link>

          <Card
            className="overflow-hidden transition-all duration-200 hover:shadow-md cursor-pointer"
            onClick={() => navigate('/requests?status=approved')}
          >
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium text-muted-foreground">Approved</CardTitle>
              <MoreVertical className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="mr-4 rounded-full bg-[rgba(0,167,111,0.12)] p-2">
                  <CheckCircle className="h-6 w-6 text-[#229A16]" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{approvedCount}</div>
                  <p className="text-xs text-muted-foreground">Approved Requests</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="p-0">
              <Progress
                className="h-1 rounded-none bg-[#E8F5E8]"
                value={approvedCount ? (approvedCount / (pendingCount + approvedCount + rejectedCount)) * 100 : 0}
                indicatorClassName="bg-[#229A16]"
              />
            </CardFooter>
          </Card>

          <Card
            className="overflow-hidden transition-all duration-200 hover:shadow-md cursor-pointer"
            onClick={() => navigate('/requests?status=rejected')}
          >
            <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
              <CardTitle className="text-sm font-medium text-muted-foreground">Rejected</CardTitle>
              <MoreVertical className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="mr-4 rounded-full bg-[rgba(255,86,48,0.12)] p-2">
                  <AlertCircle className="h-6 w-6 text-[#B72136]" />
                </div>
                <div>
                  <div className="text-2xl font-bold">{rejectedCount}</div>
                  <p className="text-xs text-muted-foreground">Rejected Requests</p>
                </div>
              </div>
            </CardContent>
            <CardFooter className="p-0">
              <Progress
                className="h-1 rounded-none bg-gradient-to-r from-[#FFE5E5] to-[#B72136]"
                indicatorClassName="bg-gradient-to-r from-[#FFE5E5] to-[#B72136]"
                value={rejectedCount ? (rejectedCount / (pendingCount + approvedCount + rejectedCount)) * 100 : 0}
              />
            </CardFooter>
          </Card>

          <Link to="/requests?status=resubmitted">
            <Card className="overflow-hidden cursor-pointer hover:shadow-md transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between pb-2 space-y-0">
                <CardTitle className="text-sm font-medium text-muted-foreground">Resubmitted</CardTitle>
                <MoreVertical className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="mr-4 rounded-full bg-[rgba(59,130,246,0.12)] p-2">
                    <ClipboardPen className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="text-2xl font-bold">{resubmittedCount}</div>
                    <p className="text-xs text-muted-foreground">
                      Resubmitted requests
                    </p>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="p-0">
                <Progress
                  className="h-1 rounded-none bg-gradient-to-r from-[#3B82F6] to-[#1E40AF]"
                  value={effectiveStats.total > 0 ? ((effectiveStats.resubmitted || 0) / effectiveStats.total) * 100 : 0}
                />
              </CardFooter>
            </Card>
          </Link>
        </div>
        {/* Total Approval Section - Professional Single Card Design - Hidden for shop managers */}
        {user && (user.role || user.user_metadata?.role) !== 'shop_manager' && (
        <div className="mb-8">
          <Card className="overflow-hidden bg-gradient-to-r from-blue-600 to-blue-700 text-white relative">
            <CardContent className="p-6 pb-0">
              <div className="flex items-center justify-between mb-6 relative z-10">
                <div>
                  <div className="text-3xl font-bold mb-2">
                    {(() => {
                      // Calculate filtered total approved amount
                      if (!effectiveRequests || effectiveRequests.length === 0) return formatAmount(0);

                      const filtered = effectiveRequests.filter(request => {
                        const requestDate = parseRequestDate(request.createdAt);
                        return isDateInRange(requestDate, dateRangeValue?.from, dateRangeValue?.to);
                      });

                      // Enhanced debug logging to help diagnose the issue
                      console.log('🔍 Dashboard Filter Debug:', {
                        dateRange,
                        dateRangeValue: dateRangeValue ? {
                          from: dateRangeValue.from?.toISOString(),
                          to: dateRangeValue.to?.toISOString(),
                          fromLocal: dateRangeValue.from?.toLocaleDateString(),
                          toLocal: dateRangeValue.to?.toLocaleDateString()
                        } : null,
                        totalRequests: effectiveRequests.length,
                        filteredRequests: filtered.length,
                        approvedInFiltered: filtered.filter(r => normalizeStatus(r.status) === 'approved').length,
                        sampleFilteredDates: filtered.slice(0, 5).map(r => ({
                          id: r.id,
                          originalCreatedAt: r.createdAt,
                          parsedDate: parseRequestDate(r.createdAt).toISOString(),
                          parsedDateLocal: parseRequestDate(r.createdAt).toLocaleDateString(),
                          status: r.status,
                          normalizedStatus: normalizeStatus(r.status),
                          amount: r.amount,
                          inRange: isDateInRange(parseRequestDate(r.createdAt), dateRangeValue?.from, dateRangeValue?.to)
                        })),
                        allApprovedDates: effectiveRequests.filter(r => normalizeStatus(r.status) === 'approved').map(r => ({
                          id: r._id,
                          originalCreatedAt: r.createdAt,
                          parsedDate: parseRequestDate(r.createdAt).toISOString(),
                          parsedDateLocal: parseRequestDate(r.createdAt).toLocaleDateString(),
                          amount: r.amount,
                          inRange: isDateInRange(parseRequestDate(r.createdAt), dateRangeValue?.from, dateRangeValue?.to)
                        })),
                        februaryTest: {
                          feb1: new Date('2024-02-01').toISOString(),
                          feb28: new Date('2024-02-28').toISOString(),
                          testRange: dateRangeValue?.from && dateRangeValue?.to ?
                            isDateInRange(new Date('2024-02-15'), dateRangeValue.from, dateRangeValue.to) : 'no range'
                        }
                      });

                      const totalApprovedAmount = filtered
                        .filter(r => normalizeStatus(r.status) === 'approved')
                        .reduce((sum, r) => sum + r.amount, 0);

                      return formatAmount(totalApprovedAmount);
                    })()}
                  </div>
                  <div className="text-blue-100">Total Approval</div>
                </div>
                <div className="bg-white/20 rounded-lg px-3 py-1">
                  <span className="text-sm font-medium">
                    {(() => {
                      // Calculate filtered approval percentage
                      if (!effectiveRequests || effectiveRequests.length === 0) return '0';

                      const filtered = effectiveRequests.filter(request => {
                        const requestDate = parseRequestDate(request.createdAt);
                        return isDateInRange(requestDate, dateRangeValue?.from, dateRangeValue?.to);
                      });

                      const totalFiltered = filtered.length;
                      const approvedFiltered = filtered.filter(r => normalizeStatus(r.status) === 'approved').length;

                      return totalFiltered > 0 ? Math.round((approvedFiltered / totalFiltered) * 100) : 0;
                    })()}%
                  </span>
                </div>
              </div>

              {/* Wave decoration */}
              <div className="absolute inset-x-0 bottom-0 h-16 overflow-hidden">
                <svg
                  className="absolute bottom-0 w-full h-full"
                  viewBox="0 0 1200 120"
                  preserveAspectRatio="none"
                >
                  <path
                    d="M0,60 C300,120 600,0 900,60 C1050,90 1150,30 1200,60 L1200,120 L0,120 Z"
                    fill="rgba(255,255,255,0.1)"
                  />
                </svg>
              </div>
            </CardContent>

            {/* Integrated Area Breakdown - White Section */}
            <div className="bg-white text-gray-900 p-6">
              <div className="space-y-4">
                {(() => {
                  // Calculate area breakdown using filtered data
                  const areaStats = new Map();

                  if (!effectiveRequests || effectiveRequests.length === 0) {
                    return <div className="text-center text-gray-500">No area data available</div>;
                  }

                  // Filter requests by date range first
                  const filteredRequests = effectiveRequests.filter(request => {
                    const requestDate = parseRequestDate(request.createdAt);
                    return isDateInRange(requestDate, dateRangeValue?.from, dateRangeValue?.to);
                  });

                  filteredRequests.forEach(request => {
                    const area = request.areaName || 'Unknown Area';
                    if (!areaStats.has(area)) {
                      areaStats.set(area, {
                        name: area,
                        totalRequests: 0,
                        approvedRequests: 0,
                        rejectedRequests: 0,
                        pendingRequests: 0,
                        totalAmount: 0,
                        approvedAmount: 0
                      });
                    }

                    const areaData = areaStats.get(area);
                    const normalizedRequestStatus = normalizeStatus(request.status);

                    areaData.totalRequests++;
                    areaData.totalAmount += request.amount;

                    if (normalizedRequestStatus === 'approved') {
                      areaData.approvedRequests++;
                      areaData.approvedAmount += request.amount;
                    } else if (normalizedRequestStatus === 'rejected') {
                      areaData.rejectedRequests++;
                    } else if (normalizedRequestStatus === 'pending') {
                      areaData.pendingRequests++;
                    } else if (normalizedRequestStatus === 'resubmitted') {
                      // Don't count resubmitted as pending - they should be counted separately
                      // areaData.pendingRequests++; // Removed this line
                    }
                  });

                  const allAreas = Array.from(areaStats.values())
                    .sort((a, b) => b.approvedAmount - a.approvedAmount);

                  if (allAreas.length === 0) {
                    return (
                      <div className="text-center py-8 text-gray-500">
                        <div className="text-4xl mb-2">📊</div>
                        <div>No areas found for selected period</div>
                      </div>
                    );
                  }

                  // Calculate total approved amount for percentage calculation
                  const totalApprovedAmount = allAreas.reduce((sum, area) => sum + area.approvedAmount, 0);

                  return allAreas.map((area, index) => {
                    // Professional area icons
                    const getAreaIcon = (areaName: string) => {
                      if (areaName.toLowerCase().includes('shopify')) return '🛍️';
                      if (areaName.toLowerCase().includes('ios')) return '📱';
                      if (areaName.toLowerCase().includes('figma')) return '🎨';
                      return '📍';
                    };

                    const getAreaColor = (index: number) => {
                      const colors = ['text-gray-700', 'text-gray-700', 'text-gray-700'];
                      return colors[index] || 'text-gray-700';
                    };

                    return (
                      <div key={area.name}>
                        <div
                          className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors rounded-lg mx-2"
                          onClick={() => navigate(`/requests?area=${encodeURIComponent(area.name)}`)}
                        >
                          <div className="flex items-center gap-3">
                            <div className="text-2xl">{getAreaIcon(area.name)}</div>
                            <div>
                              <div className="font-medium text-gray-900">{area.name}</div>
                              <div className="text-sm text-gray-500">{area.totalRequests} Requests</div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className={`font-semibold ${getAreaColor(index)}`}>
                              {formatAmount(area.approvedAmount)}
                            </div>
                            <div className="text-xs text-gray-500">
                              {totalApprovedAmount > 0 ? Math.round((area.approvedAmount / totalApprovedAmount) * 100) : 0}%
                            </div>
                          </div>
                        </div>
                        {/* Dash divider between areas (except for the last one) */}
                        {index < allAreas.length - 1 && (
                          <div className="border-b border-dashed border-gray-300 mx-4"></div>
                        )}
                      </div>
                    );
                  });
                })()}
              </div>
            </div>
          </Card>
        </div>
        )}







        {/* Recent Requests Section - Professional Grid Layout - Hidden for shop managers */}
        {user && (user.role || user.user_metadata?.role) !== 'shop_manager' && (
        <div className="mb-8">
          <div className="flex flex-row items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-bold tracking-tight text-gray-900">Recent Requests</h2>
              <p className="text-gray-600 mt-1">
                Latest pending and resubmitted requests requiring attention
              </p>
            </div>
            <Button variant="outline" size="sm" asChild className="border-gray-300 text-gray-700 hover:bg-gray-50">
              <Link to="/requests" className="flex items-center gap-2">
                <span>View All Requests</span>
                <ArrowRight className="h-4 w-4" />
              </Link>
            </Button>
          </div>

          {(() => {
            // Filter for pending and resubmitted requests only, respecting date filter
            const actionableRequests = effectiveRequests
              ? effectiveRequests
                  .filter(r => {
                    const status = normalizeStatus(r.status);
                    const statusMatch = status === 'pending' || status === 'resubmitted';

                    // Apply date filter if active
                    if (statusMatch && dateRangeValue?.from && dateRangeValue?.to) {
                      const requestDate = parseRequestDate(r.createdAt);
                      return isDateInRange(requestDate, dateRangeValue.from, dateRangeValue.to);
                    }

                    return statusMatch;
                  })
                  .sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime())
                  .slice(0, 6) // Show top 6 recent actionable requests
              : [];



            if (actionableRequests.length === 0) {
              return (
                <Card className="border-gray-200">
                  <CardContent className="flex flex-col items-center justify-center py-16">
                    <div className="bg-green-100 rounded-full p-4 mb-4">
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                    <div className="text-xl font-semibold text-gray-900 mb-2">All caught up!</div>
                    <div className="text-gray-600 text-center max-w-md">
                      No pending or resubmitted requests requiring attention at the moment.
                    </div>
                  </CardContent>
                </Card>
              );
            }

            // Check if user can approve requests (accounts or shop_support roles)
            const currentUser = appUser || user;
            const userRole = currentUser?.role || currentUser?.user_metadata?.role;
            const canUserApprove = userRole === 'accounts' || userRole === 'shop_support';

            // Debug logging to help troubleshoot (remove in production)
            // console.log('🔍 Dashboard Role Debug:', { userRole, canUserApprove });

            return (
              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                {actionableRequests.map((request, index) => {
                  const isResubmitted = normalizeStatus(request.status) === 'resubmitted';
                  const requestDate = parseRequestDate(request.createdAt);

                  return (
                    <Card
                      key={request._id}
                      className="group hover:shadow-lg transition-all duration-300 cursor-pointer border border-gray-200 hover:border-primary/30 bg-white hover:bg-gray-50/50 animate-fade-in"
                      style={{ animationDelay: `${index * 100}ms` }}
                      onClick={() => navigate(`/requests/${request._id}`)}
                    >
                      <CardHeader className="pb-4 space-y-3">
                        {/* Status Badge and Amount */}
                        <div className="flex items-center justify-between">
                          <div className={`inline-flex items-center gap-1.5 px-2.5 py-1 rounded-full text-xs font-medium ${
                            isResubmitted
                              ? 'bg-[rgba(59,130,246,0.12)] text-blue-600'
                              : 'bg-[rgba(255,171,0,0.12)] text-[#B76E00]'
                          }`}>
                            {isResubmitted ? (
                              <ClipboardPen className="h-3.5 w-3.5" />
                            ) : (
                              <Clock className="h-3.5 w-3.5" />
                            )}
                            {isResubmitted ? 'Resubmitted' : 'Pending'}
                          </div>
                          <div className="text-right">
                            <div className="text-xl font-bold text-gray-900">
                              {formatAmount(request.amount)}
                            </div>
                          </div>
                        </div>

                        {/* Request Title */}
                        <div>
                          <CardTitle className="text-lg font-semibold text-gray-900 line-clamp-1 mb-2">
                            {request.title || `Request #${request._id.slice(-6)}`}
                          </CardTitle>

                          {/* Shop and Area Info */}
                          <div className="space-y-1">
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <User className="h-4 w-4 text-gray-400" />
                              <span className="truncate font-medium">{request.shopName}</span>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="h-4 w-4 text-gray-400" />
                              <span className="truncate">{request.areaName || 'Unknown Area'}</span>
                            </div>
                          </div>
                        </div>
                      </CardHeader>

                      <CardContent className="pt-0 pb-4">
                        {/* Request Details */}
                        <div className="space-y-3 mb-4">
                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Submitted</span>
                            <span className="text-gray-700 font-medium">
                              {format(requestDate, 'MMM dd, yyyy • HH:mm')}
                            </span>
                          </div>

                          <div className="flex items-center justify-between text-sm">
                            <span className="text-gray-500">Payment Method</span>
                            <span className="text-gray-700 font-medium capitalize">
                              {request.paymentMethod?.replace('_', ' ')}
                            </span>
                          </div>
                        </div>

                        {/* Action Buttons for Users who can approve */}
                        {canUserApprove && (
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              className="flex-1 h-9 text-sm font-medium bg-green-600 hover:bg-green-700 text-white"
                              onClick={(e) => handleApprove(request._id, e)}
                            >
                              <Check className="h-4 w-4 mr-1.5" />
                              Approve
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              className="flex-1 h-9 text-sm font-medium border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700 hover:border-red-300"
                              onClick={(e) => handleReject(request._id, e)}
                            >
                              <XCircle className="h-4 w-4 mr-1.5" />
                              Reject
                            </Button>
                          </div>
                        )}

                        {/* View Details Button for Non-Approvers */}
                        {!canUserApprove && (
                          <Button
                            size="sm"
                            variant="outline"
                            className="w-full h-9 text-sm font-medium border-gray-300 text-gray-700 hover:bg-gray-50 hover:text-gray-900"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/requests/${request._id}`);
                            }}
                          >
                            <Eye className="h-4 w-4 mr-1.5" />
                            View Details
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            );
          })()}


        </div>
        )}





        {/* Activity Timeline Section */}
        <div className="mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Activity Timeline</CardTitle>
                <CardDescription>Latest request activities and updates</CardDescription>
              </div>
              <Button variant="ghost" size="sm" asChild>
                <Link to="/requests" className="flex items-center gap-1">
                  <span>View More</span>
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </CardHeader>
            <CardContent>
              <ActivityTimeline
                requests={effectiveRequests}
                isLoading={false}
                itemsPerPage={4}
                showPagination={false}
                dateFilter={dateRangeValue}
              />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
});

export default Dashboard;
