import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { Id } from "./_generated/dataModel";
import { requireAuth, getCurrentUserWithPermissions } from "./permissions";

// Get user notifications
export const getUserNotifications = query({
  args: {
    limit: v.optional(v.number()),
    unreadOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    let query = ctx.db
      .query("notifications")
      .withIndex("by_user", (q) => q.eq("userId", userWithPermissions.user._id));

    if (args.unreadOnly) {
      query = query.filter((q) => q.eq(q.field("isRead"), false));
    }

    const notifications = await query
      .order("desc")
      .take(args.limit || 50);

    // Filter out expired notifications
    const now = Date.now();
    const validNotifications = notifications.filter(
      (notification) => !notification.expiresAt || notification.expiresAt > now
    );

    return validNotifications;
  },
});

// Get unread notification count
export const getUnreadCount = query({
  args: {},
  handler: async (ctx) => {
    const userWithPermissions = await requireAuth(ctx);

    const now = Date.now();
    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user_read", (q) => 
        q.eq("userId", userWithPermissions.user._id).eq("isRead", false)
      )
      .filter((q) => 
        q.or(
          q.eq(q.field("expiresAt"), undefined),
          q.gt(q.field("expiresAt"), now)
        )
      )
      .collect();

    return unreadNotifications.length;
  },
});

// Mark notification as read
export const markAsRead = mutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    if (notification.userId !== userWithPermissions.user._id) {
      throw new Error("Access denied");
    }

    if (!notification.isRead) {
      await ctx.db.patch(args.notificationId, {
        isRead: true,
        readAt: Date.now(),
      });
    }

    return args.notificationId;
  },
});

// Mark all notifications as read
export const markAllAsRead = mutation({
  args: {},
  handler: async (ctx) => {
    const userWithPermissions = await requireAuth(ctx);

    const unreadNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_user_read", (q) => 
        q.eq("userId", userWithPermissions.user._id).eq("isRead", false)
      )
      .collect();

    const now = Date.now();
    for (const notification of unreadNotifications) {
      await ctx.db.patch(notification._id, {
        isRead: true,
        readAt: now,
      });
    }

    return unreadNotifications.length;
  },
});

// Delete notification
export const deleteNotification = mutation({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      throw new Error("Notification not found");
    }

    if (notification.userId !== userWithPermissions.user._id) {
      throw new Error("Access denied");
    }

    await ctx.db.delete(args.notificationId);
    return args.notificationId;
  },
});

// Create system notification (admin only)
export const createSystemNotification = mutation({
  args: {
    title: v.string(),
    message: v.string(),
    targetUserIds: v.optional(v.array(v.id("users"))),
    targetRoles: v.optional(v.array(v.string())),
    expiresAt: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    // Check if user has admin permissions (accounts role)
    const hasAdminRole = userWithPermissions.roles.some(role => role.name === "accounts");
    if (!hasAdminRole) {
      throw new Error("Only accounts team can create system notifications");
    }

    let targetUsers: Id<"users">[] = [];

    if (args.targetUserIds) {
      targetUsers = args.targetUserIds;
    } else if (args.targetRoles) {
      // Get users with specified roles
      const roleIds = await Promise.all(
        args.targetRoles.map(async (roleName) => {
          const role = await ctx.db
            .query("roles")
            .withIndex("by_name", (q) => q.eq("name", roleName))
            .first();
          return role?._id;
        })
      );

      const validRoleIds = roleIds.filter(Boolean) as Id<"roles">[];
      
      for (const roleId of validRoleIds) {
        const userRoles = await ctx.db
          .query("user_roles")
          .withIndex("by_role_active", (q) => 
            q.eq("roleId", roleId).eq("isActive", true)
          )
          .collect();
        
        targetUsers.push(...userRoles.map(ur => ur.userId));
      }

      // Remove duplicates
      targetUsers = [...new Set(targetUsers)];
    } else {
      // Send to all active users
      const allUsers = await ctx.db
        .query("users")
        .withIndex("by_active", (q) => q.eq("isActive", true))
        .collect();
      targetUsers = allUsers.map(user => user._id);
    }

    // Create notifications for all target users
    const now = Date.now();
    const notificationIds: Id<"notifications">[] = [];

    for (const userId of targetUsers) {
      const notificationId = await ctx.db.insert("notifications", {
        type: "system_alert",
        title: args.title,
        message: args.message,
        userId,
        isRead: false,
        createdAt: now,
        expiresAt: args.expiresAt,
      });
      notificationIds.push(notificationId);
    }

    // Log the action
    await ctx.db.insert("audit_logs", {
      action: "system_notification_created",
      entityType: "notification",
      entityId: notificationIds[0] || "bulk",
      userId: userWithPermissions.user._id,
      newValues: {
        title: args.title,
        targetCount: targetUsers.length,
        targetRoles: args.targetRoles,
      },
      timestamp: now,
    });

    return notificationIds;
  },
});

// Clean up expired notifications (internal function)
export const cleanupExpiredNotifications = mutation({
  args: {},
  handler: async (ctx) => {
    const now = Date.now();
    
    const expiredNotifications = await ctx.db
      .query("notifications")
      .withIndex("by_expires_at", (q) => q.lt("expiresAt", now))
      .collect();

    for (const notification of expiredNotifications) {
      await ctx.db.delete(notification._id);
    }

    return expiredNotifications.length;
  },
});

// Get notification by ID
export const getNotification = query({
  args: { notificationId: v.id("notifications") },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const notification = await ctx.db.get(args.notificationId);
    if (!notification) {
      return null;
    }

    if (notification.userId !== userWithPermissions.user._id) {
      throw new Error("Access denied");
    }

    // Check if notification is expired
    const now = Date.now();
    if (notification.expiresAt && notification.expiresAt <= now) {
      return null;
    }

    return notification;
  },
});

// Get notifications for a specific entity
export const getEntityNotifications = query({
  args: {
    entityType: v.string(),
    entityId: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const userWithPermissions = await requireAuth(ctx);

    const notifications = await ctx.db
      .query("notifications")
      .withIndex("by_related_entity", (q) => 
        q.eq("relatedEntityType", args.entityType).eq("relatedEntityId", args.entityId)
      )
      .filter((q) => q.eq(q.field("userId"), userWithPermissions.user._id))
      .order("desc")
      .take(args.limit || 20);

    // Filter out expired notifications
    const now = Date.now();
    const validNotifications = notifications.filter(
      (notification) => !notification.expiresAt || notification.expiresAt > now
    );

    return validNotifications;
  },
});
