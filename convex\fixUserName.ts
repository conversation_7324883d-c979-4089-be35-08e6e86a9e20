import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Debug query to check user data
export const debugUserData = query({
  args: { email: v.string() },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (!user) {
      return { error: "User not found" };
    }
    
    return {
      user: {
        _id: user._id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        workosId: user.workosId,
      },
      avatarCalculation: {
        hasFirstName: !!user.firstName,
        firstName: user.firstName,
        lastName: user.lastName,
        combined: user.firstName ? (user.firstName + (user.lastName || '')) : 'no firstName',
        initials: user.firstName 
          ? (user.firstName + (user.lastName || '')).substring(0, 2).toUpperCase()
          : user.email?.substring(0, 2).toUpperCase()
      }
    };
  },
});

// Mutation to fix user name
export const fixUserName = mutation({
  args: {
    email: v.string(),
    firstName: v.string(),
    lastName: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", args.email))
      .unique();
    
    if (!user) {
      throw new Error("User not found");
    }
    
    await ctx.db.patch(user._id, {
      firstName: args.firstName,
      lastName: args.lastName || "",
      updatedAt: Date.now(),
    });
    
    return {
      success: true,
      message: `Updated user ${args.email} with firstName: ${args.firstName}, lastName: ${args.lastName || ''}`,
      newInitials: (args.firstName + (args.lastName || '')).substring(0, 2).toUpperCase()
    };
  },
});

// Get all users for debugging
export const getAllUsersDebug = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    
    return users.map(user => ({
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      workosId: user.workosId,
      initials: user.firstName 
        ? (user.firstName + (user.lastName || '')).substring(0, 2).toUpperCase()
        : user.email?.substring(0, 2).toUpperCase()
    }));
  },
});
