import React, { useState, useEffect } from 'react';
import Layout from '@/components/layout/Layout';
import { useRequireAuth } from '@/lib/auth';
import PageTransition from '@/components/common/PageTransition';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useQuery, useMutation } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { toast } from '@/components/ui/use-toast';
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Clock,
  Users,
  Building,
  Eye
} from 'lucide-react';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

const SyncManagement: React.FC = () => {
  useRequireAuth(['accounts']); // Only accounts users can access sync management

  const [selectedEntityType, setSelectedEntityType] = useState<string>('all');
  const [isRetrying, setIsRetrying] = useState<string | null>(null);

  // Fetch sync failures
  const syncFailures = useQuery(api.workosApi.getSyncStatus, {
    entityType: selectedEntityType === 'all' ? undefined : selectedEntityType,
    limit: 100,
  });

  // Retry mutation
  const retrySync = useMutation(api.workosApi.retryFailedSync);

  // Manual sync actions
  const syncUserToWorkOS = useMutation(api.workosApi.syncUserToWorkOS);
  const syncOrganizationToWorkOS = useMutation(api.workosApi.syncOrganizationToWorkOS);

  const handleRetrySync = async (auditLogId: string) => {
    setIsRetrying(auditLogId);
    try {
      await retrySync({ auditLogId });
      toast({
        title: "Sync Retry Successful",
        description: "The failed sync operation has been retried successfully.",
      });
    } catch (error) {
      toast({
        title: "Sync Retry Failed",
        description: error.message || "Failed to retry the sync operation.",
        variant: "destructive",
      });
    } finally {
      setIsRetrying(null);
    }
  };

  const getSyncStatusBadge = (failure: any) => {
    const timeDiff = Date.now() - failure.timestamp;
    const hoursAgo = Math.floor(timeDiff / (1000 * 60 * 60));

    if (hoursAgo < 1) {
      return <Badge variant="destructive">Recent Failure</Badge>;
    } else if (hoursAgo < 24) {
      return <Badge variant="secondary">Failed {hoursAgo}h ago</Badge>;
    } else {
      return <Badge variant="outline">Failed {Math.floor(hoursAgo / 24)}d ago</Badge>;
    }
  };

  const getEntityIcon = (entityType: string) => {
    switch (entityType) {
      case 'user':
        return <Users className="h-4 w-4" />;
      case 'area':
        return <Building className="h-4 w-4" />;
      default:
        return <RefreshCw className="h-4 w-4" />;
    }
  };

  return (
    <Layout>
      <PageTransition>
        <div className="container mx-auto p-6 space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold">WorkOS Sync Management</h1>
              <p className="text-muted-foreground">
                Monitor and manage synchronization between Convex and WorkOS
              </p>
            </div>
          </div>

          {/* Sync Status Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Sync Failures</CardTitle>
                <XCircle className="h-4 w-4 text-destructive" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-destructive">
                  {syncFailures?.length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Failed synchronization attempts
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Recent Failures</CardTitle>
                <Clock className="h-4 w-4 text-orange-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-500">
                  {syncFailures?.filter(f => Date.now() - f.timestamp < 24 * 60 * 60 * 1000).length || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Failed in last 24 hours
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Sync Status</CardTitle>
                <CheckCircle className="h-4 w-4 text-green-500" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-500">
                  {syncFailures?.length === 0 ? 'Healthy' : 'Issues'}
                </div>
                <p className="text-xs text-muted-foreground">
                  Overall sync health
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Sync Failures Table */}
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Sync Failures</CardTitle>
                  <CardDescription>
                    Failed synchronization attempts that need attention
                  </CardDescription>
                </div>
                <div className="flex gap-2">
                  <select
                    value={selectedEntityType}
                    onChange={(e) => setSelectedEntityType(e.target.value)}
                    className="px-3 py-2 border rounded-md text-sm"
                  >
                    <option value="all">All Types</option>
                    <option value="user">Users</option>
                    <option value="area">Areas</option>
                    <option value="user_membership">Memberships</option>
                  </select>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {syncFailures?.length === 0 ? (
                <Alert>
                  <CheckCircle className="h-4 w-4" />
                  <AlertTitle>No Sync Failures</AlertTitle>
                  <AlertDescription>
                    All synchronization operations are working correctly.
                  </AlertDescription>
                </Alert>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Entity</TableHead>
                      <TableHead>Type</TableHead>
                      <TableHead>Operation</TableHead>
                      <TableHead>Error</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Failed At</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {syncFailures?.map((failure) => (
                      <TableRow key={failure._id}>
                        <TableCell className="flex items-center gap-2">
                          {getEntityIcon(failure.entityType)}
                          <span className="font-mono text-sm">
                            {failure.entityId.slice(-8)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {failure.entityType}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {failure.metadata?.operation || 'unknown'}
                          </Badge>
                        </TableCell>
                        <TableCell className="max-w-xs">
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="h-auto p-0 text-left">
                                <span className="truncate">
                                  {failure.metadata?.error || 'Unknown error'}
                                </span>
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Sync Error Details</DialogTitle>
                                <DialogDescription>
                                  Full error message for failed sync operation
                                </DialogDescription>
                              </DialogHeader>
                              <div className="space-y-2">
                                <div>
                                  <strong>Entity:</strong> {failure.entityType} ({failure.entityId})
                                </div>
                                <div>
                                  <strong>Operation:</strong> {failure.metadata?.operation}
                                </div>
                                <div>
                                  <strong>Error:</strong>
                                  <pre className="mt-2 p-3 bg-muted rounded text-sm whitespace-pre-wrap">
                                    {failure.metadata?.error}
                                  </pre>
                                </div>
                              </div>
                            </DialogContent>
                          </Dialog>
                        </TableCell>
                        <TableCell>
                          {getSyncStatusBadge(failure)}
                        </TableCell>
                        <TableCell>
                          {format(new Date(failure.timestamp), 'MMM dd, yyyy HH:mm')}
                        </TableCell>
                        <TableCell>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleRetrySync(failure._id)}
                            disabled={isRetrying === failure._id}
                          >
                            {isRetrying === failure._id ? (
                              <RefreshCw className="h-4 w-4 animate-spin" />
                            ) : (
                              <RefreshCw className="h-4 w-4" />
                            )}
                            Retry
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Sync Health Tips */}
          <Card>
            <CardHeader>
              <CardTitle>Sync Health Tips</CardTitle>
              <CardDescription>
                Best practices for maintaining healthy WorkOS synchronization
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Environment Variables</AlertTitle>
                <AlertDescription>
                  Ensure WORKOS_API_KEY and WORKOS_WEBHOOK_SECRET are properly configured in your Convex environment.
                </AlertDescription>
              </Alert>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Webhook Configuration</AlertTitle>
                <AlertDescription>
                  Configure WorkOS webhooks to point to: <code>https://efficient-toucan-547.convex.cloud/http/workos-webhook</code>
                </AlertDescription>
              </Alert>

              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertTitle>Rate Limits</AlertTitle>
                <AlertDescription>
                  WorkOS API has rate limits. Failed syncs are automatically retried with exponential backoff.
                </AlertDescription>
              </Alert>
            </CardContent>
          </Card>
        </div>
      </PageTransition>
    </Layout>
  );
};

export default SyncManagement;