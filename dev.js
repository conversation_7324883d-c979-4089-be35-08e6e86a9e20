#!/usr/bin/env node

/**
 * Enhanced development script for Convex + Vite
 * 
 * This script provides a more robust development experience by:
 * 1. Deploying Convex functions once (no watch mode)
 * 2. Starting Vite development server
 * 3. Handling network errors gracefully
 * 4. Providing clear status messages
 */

import { spawn } from 'child_process';

// ANSI color codes for prettier output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// Helper to print colored messages
const print = {
  info: (msg) => console.log(`${colors.blue}${colors.bright}ℹ️ INFO:${colors.reset} ${msg}`),
  success: (msg) => console.log(`${colors.green}${colors.bright}✅ SUCCESS:${colors.reset} ${msg}`),
  warning: (msg) => console.log(`${colors.yellow}${colors.bright}⚠️ WARNING:${colors.reset} ${msg}`),
  error: (msg) => console.log(`${colors.red}${colors.bright}❌ ERROR:${colors.reset} ${msg}`),
  header: (msg) => console.log(`\n${colors.cyan}${colors.bright}=== ${msg} ===${colors.reset}\n`),
};

// Run a command and return a promise
function runCommand(command, args = [], options = {}) {
  return new Promise((resolve, reject) => {
    print.info(`Running: ${command} ${args.join(' ')}`);
    
    const proc = spawn(command, args, { 
      stdio: 'inherit',
      shell: true,
      ...options
    });
    
    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });
    
    proc.on('error', (err) => {
      reject(err);
    });
  });
}

// Main function
async function main() {
  print.header('Starting Development Environment');
  
  try {
    // Step 1: Deploy Convex functions once
    print.header('Deploying Convex Functions');
    await runCommand('npx', ['convex', 'dev', '--once']);
    print.success('Convex functions deployed successfully');
    
    // Step 2: Start Vite development server
    print.header('Starting Vite Development Server');
    await runCommand('npx', ['vite']);
    
  } catch (error) {
    if (error.message.includes('ETIMEDOUT') || error.message.includes('network')) {
      print.error('Network connection issue detected');
      print.warning('Unable to connect to Convex servers. This might be due to:');
      print.warning('- Temporary network issues');
      print.warning('- Firewall blocking connections to Convex');
      print.warning('- VPN interference');
      
      print.info('\nTrying alternative approach...');
      
      try {
        // Try running Vite without Convex
        print.header('Starting Vite Only (No Convex)');
        print.warning('Note: Backend functionality will not work until network issues are resolved');
        await runCommand('npx', ['vite']);
      } catch (viteError) {
        print.error('Failed to start Vite server');
        print.error(viteError.message);
        process.exit(1);
      }
    } else {
      print.error('Failed to start development environment');
      print.error(error.message);
      process.exit(1);
    }
  }
}

// Run the main function
main().catch(err => {
  print.error('Unhandled error in main process');
  print.error(err.message);
  process.exit(1);
});