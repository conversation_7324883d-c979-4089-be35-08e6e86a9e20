import { action } from "./_generated/server";
import { v } from "convex/values";

/**
 * Test WorkOS API integration without authentication
 */
export const testCreateUserInWorkOS = action({
  args: {
    email: v.string(),
    firstName: v.string(),
    lastName: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log("Testing WorkOS API user creation...");
      
      const userData = {
        email: args.email,
        first_name: args.firstName,
        last_name: args.lastName,
        email_verified: true,
      };

      const response = await fetch("https://api.workos.com/user_management/users", {
        method: "POST",
        headers: {
          "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
          "Content-Type": "application/json",
          "User-Agent": "MyBet-CashManagement/1.0",
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`WorkOS API error: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`WorkOS API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log("Created user in WorkOS:", result.id);
      return result;
      
    } catch (error) {
      console.error("Error creating user in WorkOS:", error);
      throw error;
    }
  },
});

// Test listing users from WorkOS
export const testListWorkOSUsers = action({
  args: {},
  handler: async (ctx) => {
    try {
      console.log("Testing WorkOS API user listing...");
      
      const response = await fetch("https://api.workos.com/user_management/users", {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`WorkOS API error: ${response.status} ${response.statusText}`, errorText);
        throw new Error(`WorkOS API error: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      console.log(`Found ${result.data.length} users in WorkOS`);
      return result;
      
    } catch (error) {
      console.error("Error listing WorkOS users:", error);
      throw error;
    }
  },
});