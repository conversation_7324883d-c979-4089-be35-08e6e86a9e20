import { ConvexReactClient } from "convex/react";
import { CONVEX_CONFIG, validateConfig } from "../config";

// Validate configuration on module load
try {
  validateConfig();
} catch (error) {
  console.error("Configuration validation failed:", error);
  throw error;
}

// Create and configure Convex client
export const convex = new ConvexReactClient(CONVEX_CONFIG.URL);

// Export the client as default for convenience
export default convex;

// Type helpers for Convex
export type { Id } from "../../convex/_generated/dataModel";
export { api } from "../../convex/_generated/api";

// Convex query and mutation hooks re-exports for convenience
export { useQuery, useMutation, useAction } from "convex/react";

// Custom hooks for common patterns
export const useConvexAuth = () => {
  // This will be implemented when we set up the auth system
  // For now, return a placeholder
  return {
    isLoading: false,
    isAuthenticated: false,
    user: null,
  };
};

// Error handling utilities
export const handleConvexError = (error: unknown): string => {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === "string") {
    return error;
  }
  return "An unexpected error occurred";
};

// Convex client utilities
export const convexUtils = {
  // Check if Convex is properly configured
  isConfigured: (): boolean => {
    return Boolean(CONVEX_CONFIG.URL);
  },

  // Get the Convex URL
  getUrl: (): string => {
    return CONVEX_CONFIG.URL;
  },

  // Check connection status
  isConnected: (): boolean => {
    return convex.connectionState().isWebSocketConnected;
  },

  // Get connection state
  getConnectionState: () => {
    return convex.connectionState();
  },
};

// Development utilities
if (import.meta.env.DEV) {
  // Add development-only utilities
  (window as any).convex = convex;
  (window as any).convexUtils = convexUtils;
}
