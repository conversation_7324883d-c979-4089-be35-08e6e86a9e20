import { mutation, query, action } from "./_generated/server";
import { v } from "convex/values";
import { api } from "./_generated/api";

/**
 * WorkOS API Integration for Bidirectional Sync
 * 
 * This module handles pushing changes from Convex to WorkOS
 * to maintain bidirectional synchronization.
 */

// WorkOS API client configuration
const WORKOS_API_BASE = "https://api.workos.com";

const getWorkOSHeaders = () => ({
  "Authorization": `Bearer ${process.env.WORKOS_API_KEY}`,
  "Content-Type": "application/json",
  "User-Agent": "MyBet-CashManagement/1.0",
});

// Helper function to make WorkOS API calls
const makeWorkOSRequest = async (
  endpoint: string, 
  method: string = "GET", 
  body?: any
) => {
  const url = `${WORKOS_API_BASE}${endpoint}`;
  const options: RequestInit = {
    method,
    headers: getWorkOSHeaders(),
  };

  if (body && (method === "POST" || method === "PUT" || method === "PATCH")) {
    options.body = JSON.stringify(body);
  }

  console.log(`Making WorkOS API request: ${method} ${url}`);
  
  const response = await fetch(url, options);
  
  if (!response.ok) {
    const errorText = await response.text();
    console.error(`WorkOS API error: ${response.status} ${response.statusText}`, errorText);
    throw new Error(`WorkOS API error: ${response.status} ${response.statusText}`);
  }

  if (response.status === 204) {
    return null; // No content
  }

  return await response.json();
};

// Sync user to WorkOS (create or update)
export const syncUserToWorkOS = action({
  args: {
    userId: v.id("users"),
    operation: v.union(v.literal("create"), v.literal("update"), v.literal("delete")),
  },
  handler: async (ctx, args) => {
    // Get user data from Convex
    const user = await ctx.runQuery(api.users.getUser, { userId: args.userId });
    
    if (!user) {
      throw new Error("User not found");
    }

    try {
      switch (args.operation) {
        case "create":
          return await createUserInWorkOS(user);
        case "update":
          return await updateUserInWorkOS(user);
        case "delete":
          return await deleteUserInWorkOS(user);
        default:
          throw new Error(`Unsupported operation: ${args.operation}`);
      }
    } catch (error) {
      console.error(`Failed to sync user to WorkOS:`, error);
      
      // Log the failed sync attempt
      await ctx.runMutation(api.workosApi.logSyncFailure, {
        entityType: "user",
        entityId: args.userId,
        operation: args.operation,
        error: error.message,
      });
      
      throw error;
    }
  },
});

// Create user in WorkOS
const createUserInWorkOS = async (user: any) => {
  const userData = {
    email: user.email,
    first_name: user.firstName,
    last_name: user.lastName,
    email_verified: true,
  };

  const result = await makeWorkOSRequest("/user_management/users", "POST", userData);
  console.log("Created user in WorkOS:", result.id);
  return result;
};

// Update user in WorkOS
const updateUserInWorkOS = async (user: any) => {
  if (!user.workosId) {
    console.log("User has no WorkOS ID, creating instead");
    return await createUserInWorkOS(user);
  }

  const userData = {
    first_name: user.firstName,
    last_name: user.lastName,
  };

  const result = await makeWorkOSRequest(
    `/user_management/users/${user.workosId}`, 
    "PUT", 
    userData
  );
  console.log("Updated user in WorkOS:", user.workosId);
  return result;
};

// Delete (deactivate) user in WorkOS
const deleteUserInWorkOS = async (user: any) => {
  if (!user.workosId) {
    console.log("User has no WorkOS ID, skipping deletion");
    return;
  }

  // WorkOS doesn't support user deletion, so we'll deactivate instead
  // This might require using the Directory Sync API or Organization Memberships
  console.log("User deactivation in WorkOS not implemented yet:", user.workosId);
  return null;
};

// Request password reset for user
export const requestPasswordReset = action({
  args: {
    email: v.string(),
    userId: v.id("users"),
  },
  handler: async (ctx, args) => {
    console.log(`Requesting password reset for user: ${args.email}`);

    try {
      // Make request to WorkOS password reset API
      const resetData = {
        email: args.email,
      };

      const passwordReset = await makeWorkOSRequest("/user_management/password_reset", "POST", resetData);
      console.log("Created WorkOS password reset:", passwordReset.id);

      // Log the password reset request
      await ctx.runMutation(api.workosApi.logSyncSuccess, {
        entityType: "password_reset",
        entityId: args.userId,
        operation: "password_reset_requested",
        workosId: passwordReset.id,
      });

      return {
        success: true,
        passwordResetId: passwordReset.id,
        passwordResetUrl: passwordReset.password_reset_url,
        message: "Password reset email sent successfully",
      };
    } catch (error) {
      console.error("Password reset request failed:", error);

      // Log the failed attempt
      await ctx.runMutation(api.workosApi.logSyncFailure, {
        entityType: "password_reset",
        entityId: args.userId,
        operation: "password_reset_requested",
        error: error.message,
      });

      throw new Error("Failed to send password reset email. Please try again.");
    }
  },
});

// Sync organization to WorkOS
export const syncOrganizationToWorkOS = action({
  args: {
    areaId: v.id("areas"),
    operation: v.union(v.literal("create"), v.literal("update"), v.literal("delete")),
  },
  handler: async (ctx, args) => {
    // Get area data from Convex (we're mapping areas to WorkOS organizations)
    const area = await ctx.runQuery(api.areas.getArea, { areaId: args.areaId });
    
    if (!area) {
      throw new Error("Area not found");
    }

    try {
      switch (args.operation) {
        case "create":
          return await createOrganizationInWorkOS(area);
        case "update":
          return await updateOrganizationInWorkOS(area);
        case "delete":
          return await deleteOrganizationInWorkOS(area);
        default:
          throw new Error(`Unsupported operation: ${args.operation}`);
      }
    } catch (error) {
      console.error(`Failed to sync organization to WorkOS:`, error);
      
      // Log the failed sync attempt
      await ctx.runMutation(api.workosApi.logSyncFailure, {
        entityType: "area",
        entityId: args.areaId,
        operation: args.operation,
        error: error.message,
      });
      
      throw error;
    }
  },
});

// Create organization in WorkOS
const createOrganizationInWorkOS = async (area: any) => {
  const orgData = {
    name: area.name,
    domains: [], // We might not have domains for areas
    allow_profiles_outside_organization: true,
  };

  const result = await makeWorkOSRequest("/organizations", "POST", orgData);
  console.log("Created organization in WorkOS:", result.id);
  return result;
};

// Update organization in WorkOS
const updateOrganizationInWorkOS = async (area: any) => {
  if (!area.workosOrganizationId) {
    console.log("Area has no WorkOS organization ID, creating instead");
    return await createOrganizationInWorkOS(area);
  }

  const orgData = {
    name: area.name,
  };

  const result = await makeWorkOSRequest(
    `/organizations/${area.workosOrganizationId}`, 
    "PUT", 
    orgData
  );
  console.log("Updated organization in WorkOS:", area.workosOrganizationId);
  return result;
};

// Delete organization in WorkOS
const deleteOrganizationInWorkOS = async (area: any) => {
  if (!area.workosOrganizationId) {
    console.log("Area has no WorkOS organization ID, skipping deletion");
    return;
  }

  await makeWorkOSRequest(`/organizations/${area.workosOrganizationId}`, "DELETE");
  console.log("Deleted organization in WorkOS:", area.workosOrganizationId);
  return null;
};

// Sync user role/organization membership to WorkOS
export const syncUserMembershipToWorkOS = action({
  args: {
    userId: v.id("users"),
    areaId: v.optional(v.id("areas")),
    operation: v.union(v.literal("create"), v.literal("update"), v.literal("delete")),
  },
  handler: async (ctx, args) => {
    const user = await ctx.runQuery(api.users.getUser, { userId: args.userId });
    
    if (!user || !user.workosId) {
      throw new Error("User not found or has no WorkOS ID");
    }

    try {
      switch (args.operation) {
        case "create":
          return await createMembershipInWorkOS(user, args.areaId);
        case "update":
          return await updateMembershipInWorkOS(user, args.areaId);
        case "delete":
          return await deleteMembershipInWorkOS(user, args.areaId);
        default:
          throw new Error(`Unsupported operation: ${args.operation}`);
      }
    } catch (error) {
      console.error(`Failed to sync membership to WorkOS:`, error);
      
      // Log the failed sync attempt
      await ctx.runMutation(api.workosApi.logSyncFailure, {
        entityType: "user_membership",
        entityId: args.userId,
        operation: args.operation,
        error: error.message,
      });
      
      throw error;
    }
  },
});

// Create organization membership in WorkOS
const createMembershipInWorkOS = async (user: any, areaId?: string) => {
  if (!areaId) {
    console.log("No area specified for membership creation");
    return null;
  }

  // We would need the WorkOS organization ID for the area
  // This requires extending the areas table to store WorkOS organization IDs
  console.log("Organization membership creation not fully implemented yet");
  return null;
};

// Update organization membership in WorkOS
const updateMembershipInWorkOS = async (user: any, areaId?: string) => {
  console.log("Organization membership update not fully implemented yet");
  return null;
};

// Delete organization membership in WorkOS
const deleteMembershipInWorkOS = async (user: any, areaId?: string) => {
  console.log("Organization membership deletion not fully implemented yet");
  return null;
};

// Log sync failures for monitoring and retry
export const logSyncFailure = mutation({
  args: {
    entityType: v.string(),
    entityId: v.string(),
    operation: v.string(),
    error: v.string(),
    userId: v.optional(v.id("users")), // Optional user ID for system operations
  },
  handler: async (ctx, args) => {
    // Use provided userId or default to the first admin user for system operations
    let systemUserId = args.userId;
    if (!systemUserId) {
      // Find an admin/accounts user to use as system user
      const adminUser = await ctx.db
        .query("user_roles")
        .withIndex("by_role_active", (q) => q.eq("roleId", "jx7c0hkvak4bzb1c6b27d9296d7jg714").eq("isActive", true))
        .first();
      
      if (adminUser) {
        systemUserId = adminUser.userId;
      } else {
        throw new Error("No admin user found for system operations");
      }
    }

    await ctx.db.insert("audit_logs", {
      action: "workos_sync_failed",
      entityType: args.entityType,
      entityId: args.entityId,
      userId: systemUserId,
      metadata: {
        operation: args.operation,
        error: args.error,
        timestamp: Date.now(),
      },
      timestamp: Date.now(),
    });
  },
});

// Get sync status and failures
export const getSyncStatus = query({
  args: {
    entityType: v.optional(v.string()),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db
      .query("audit_logs")
      .withIndex("by_action", (q) => q.eq("action", "workos_sync_failed"));

    if (args.entityType) {
      query = query.filter((q) => q.eq(q.field("entityType"), args.entityType));
    }

    const failures = await query
      .order("desc")
      .take(args.limit || 50);

    return failures;
  },
});

// Retry failed syncs
export const retryFailedSync = action({
  args: {
    auditLogId: v.id("audit_logs"),
  },
  handler: async (ctx, args) => {
    const auditLog = await ctx.runQuery(api.workosApi.getAuditLog, { 
      auditLogId: args.auditLogId 
    });

    if (!auditLog || auditLog.action !== "workos_sync_failed") {
      throw new Error("Invalid audit log for retry");
    }

    const { entityType, entityId, metadata } = auditLog;
    const operation = metadata.operation;

    try {
      switch (entityType) {
        case "user":
          await ctx.runAction(api.workosApi.syncUserToWorkOS, {
            userId: entityId as any,
            operation: operation as any,
          });
          break;
        case "area":
          await ctx.runAction(api.workosApi.syncOrganizationToWorkOS, {
            areaId: entityId as any,
            operation: operation as any,
          });
          break;
        default:
          throw new Error(`Unsupported entity type for retry: ${entityType}`);
      }

      console.log("Successfully retried sync for:", entityId);
    } catch (error) {
      console.error("Retry failed:", error);
      throw error;
    }
  },
});

// Create user invitation in WorkOS
export const createWorkOSInvitation = action({
  args: {
    email: v.string(),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    organizationId: v.optional(v.string()),
    roleSlug: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    try {
      console.log("Creating WorkOS invitation for:", args.email);
      
      // Strategy: Try to create user first, if it fails because user exists,
      // send password reset instead
      let user;
      let method = "invitation";
      
      try {
        // Try to create new user
        const userData = {
          email: args.email,
          first_name: args.firstName || "",
          last_name: args.lastName || "",
          email_verified: false,
        };

        user = await makeWorkOSRequest("/user_management/users", "POST", userData);
        console.log("Created new WorkOS user:", user.id);
        
        // Try to send invitation for new user
        const invitationData = {
          email: args.email,
          expires_in_days: 7,
        };

        const invitation = await makeWorkOSRequest("/user_management/invitations", "POST", invitationData);
        console.log("Created WorkOS invitation:", invitation.id);
        
        return {
          workosUserId: user.id,
          invitationId: invitation.id,
          invitationToken: invitation.token,
          invitationUrl: invitation.accept_invitation_url,
          method: "invitation",
        };
        
      } catch (createError) {
        console.log("User creation failed, trying password reset approach:", createError.message);
        method = "password_reset";
      }
      
      // If user creation failed (likely because user exists), send password reset
      console.log("Sending password reset for existing user");
      const resetData = {
        email: args.email,
      };

      const passwordReset = await makeWorkOSRequest("/user_management/password_reset", "POST", resetData);
      console.log("Created WorkOS password reset:", passwordReset.id);

      return {
        passwordResetId: passwordReset.id,
        passwordResetToken: passwordReset.password_reset_token,
        passwordResetUrl: passwordReset.password_reset_url,
        method: "password_reset",
      };
    } catch (error) {
      console.error("Failed to create WorkOS invitation:", error);
      throw new Error(`WorkOS invitation failed: ${error.message}`);
    }
  },
});

// Delete user from WorkOS
export const deleteWorkOSUser = action({
  args: {
    workosUserId: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log("Deleting WorkOS user:", args.workosUserId);
      
      // Delete user from WorkOS User Management
      await makeWorkOSRequest(`/user_management/users/${args.workosUserId}`, "DELETE");
      console.log("Successfully deleted WorkOS user:", args.workosUserId);

      return {
        success: true,
        workosUserId: args.workosUserId,
      };
    } catch (error) {
      console.error("Failed to delete WorkOS user:", error);
      throw new Error(`WorkOS user deletion failed: ${error.message}`);
    }
  },
});

// Create default organization membership for new users
export const createDefaultOrganizationMembership = action({
  args: {
    workosUserId: v.string(),
    userEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const DEFAULT_ORG_ID = "org_01JM086NJQP34JDKMG0Z3K2ATR";
    const DEFAULT_ROLE = "shop_manager"; // Default role for new users
    
    try {
      console.log(`Creating organization membership for user ${args.userEmail} in organization ${DEFAULT_ORG_ID}`);
      
      const membershipData = {
        user_id: args.workosUserId,
        organization_id: DEFAULT_ORG_ID,
        role_slug: DEFAULT_ROLE,
      };

      const membership = await makeWorkOSRequest(
        "/user_management/organization_memberships", 
        "POST", 
        membershipData
      );
      
      console.log("Successfully created organization membership:", membership.id);
      
      return {
        membershipId: membership.id,
        organizationId: DEFAULT_ORG_ID,
        roleSlug: DEFAULT_ROLE,
        userId: args.workosUserId,
      };
    } catch (error) {
      console.error("Failed to create default organization membership:", error);
      throw new Error(`Organization membership creation failed: ${error.message}`);
    }
  },
});

// Assign existing user to default organization (for fixing existing users)
export const assignExistingUserToDefaultOrg = action({
  args: {
    userEmail: v.string(),
  },
  handler: async (ctx, args) => {
    const DEFAULT_ORG_ID = "org_01JM086NJQP34JDKMG0Z3K2ATR";
    const DEFAULT_ROLE = "shop_manager";
    
    try {
      // First, find the user in WorkOS by email
      console.log(`Finding WorkOS user by email: ${args.userEmail}`);
      
      const usersResponse = await makeWorkOSRequest(
        `/user_management/users?email=${encodeURIComponent(args.userEmail)}`,
        "GET"
      );
      
      if (!usersResponse.data || usersResponse.data.length === 0) {
        throw new Error(`User not found in WorkOS: ${args.userEmail}`);
      }
      
      const workosUser = usersResponse.data[0];
      console.log(`Found WorkOS user: ${workosUser.id}`);
      
      // Check if user already has membership in the default organization
      const membershipsResponse = await makeWorkOSRequest(
        `/user_management/organization_memberships?user_id=${workosUser.id}&organization_id=${DEFAULT_ORG_ID}`,
        "GET"
      );
      
      if (membershipsResponse.data && membershipsResponse.data.length > 0) {
        console.log(`User ${args.userEmail} already has membership in default organization`);
        return {
          alreadyExists: true,
          membershipId: membershipsResponse.data[0].id,
          organizationId: DEFAULT_ORG_ID,
          userId: workosUser.id,
        };
      }
      
      // Create the organization membership
      const membershipData = {
        user_id: workosUser.id,
        organization_id: DEFAULT_ORG_ID,
        role_slug: DEFAULT_ROLE,
      };

      const membership = await makeWorkOSRequest(
        "/user_management/organization_memberships", 
        "POST", 
        membershipData
      );
      
      console.log(`Successfully assigned ${args.userEmail} to default organization:`, membership.id);
      
      return {
        alreadyExists: false,
        membershipId: membership.id,
        organizationId: DEFAULT_ORG_ID,
        roleSlug: DEFAULT_ROLE,
        userId: workosUser.id,
      };
    } catch (error) {
      console.error(`Failed to assign existing user to default organization:`, error);
      throw new Error(`Assignment failed: ${error.message}`);
    }
  },
});

// Helper query to get audit log
export const getAuditLog = query({
  args: { auditLogId: v.id("audit_logs") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.auditLogId);
  },
});