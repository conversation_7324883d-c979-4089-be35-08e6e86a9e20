# Convex MCP Setup Guide

## Overview
This project now has Convex Model Context Protocol (MCP) support enabled, allowing AI assistants to directly interact with your Convex backend.

## Available Tools
The Convex MCP server provides these tools:

### 🔍 **Data Tools**
- `data` - Query and inspect database tables
- `tables` - List all available tables
- `runOneoffQuery` - Execute custom queries

### ⚙️ **Function Tools**
- `run` - Execute Convex functions (queries, mutations, actions)
- `functionSpec` - Get function metadata and schemas

### 🌍 **Environment Tools**
- `envGet` - Get environment variables
- `envList` - List all environment variables
- `envSet` - Set environment variables
- `envRemove` - Remove environment variables

### 📊 **Status Tools**
- `status` - Get deployment status and health

## Quick Start

### 1. Start the MCP Server
```bash
npx convex mcp start --project-dir .
```

### 2. For Claude Desktop Integration
Add this to your Claude Desktop config file (`%APPDATA%\Claude\claude_desktop_config.json`):

```json
{
  "mcpServers": {
    "convex": {
      "command": "npx",
      "args": [
        "convex",
        "mcp",
        "start",
        "--project-dir",
        "C:\\Users\\<USER>\\Desktop\\retail-cash-manger"
      ]
    }
  }
}
```

### 3. For Other AI Tools
Use the configuration in `mcp-config.json` and adapt it to your AI tool's MCP configuration format.

## Example Usage

Once connected, you can ask AI assistants to:

### Query Data
- "Show me all users in the database"
- "Get the latest 10 requests"
- "Find user <NAME_EMAIL>"

### Execute Functions
- "Run the fixUserRole.assignRoleToUser mutation to give admin access"
- "Execute the debugAuth.getCurrentAuthState query"
- "Call the settings.getSettings function"

### Manage Environment
- "Show me the current environment variables"
- "Set a new environment variable for SMTP configuration"

### Debug Issues
- "Check the status of the deployment"
- "List all available functions"
- "Show me the schema for the users table"

## Security Notes

- The MCP server runs on your **development deployment** by default
- For production access, use `--prod` flag (use with caution)
- Environment variables are accessible through the MCP server
- Always review AI-generated mutations before execution

## Troubleshooting

### MCP Server Won't Start
1. Ensure you're in the project directory
2. Check that Convex is properly configured
3. Verify your deployment is accessible

### AI Tool Can't Connect
1. Check the MCP configuration file path
2. Ensure the command path is correct
3. Restart your AI tool after configuration changes

### Permission Issues
1. Verify your Convex authentication
2. Check deployment access permissions
3. Ensure environment variables are set correctly

## Available Functions in Your Project

### Authentication & Users
- `auth.getCurrentUser`
- `auth.getCurrentUserWithRoles`
- `users.getUsers`
- `fixUserRole.assignRoleToUser`

### Requests Management
- `requests.createRequest`
- `requests.approveRequest`
- `requests.rejectRequest`
- `publicQueries.getRequestsForUser`

### Settings & Configuration
- `settings.getSettings`
- `settings.updateSettings`

### Debug & Admin
- `debugAuth.getCurrentAuthState`
- `fixUserName.debugUserData`
- `quickFix.fixEbenezerName`

## Benefits

✅ **Direct Database Access** - Query and modify data without writing scripts  
✅ **Function Execution** - Run any Convex function directly  
✅ **Real-time Debugging** - Get instant insights into your backend  
✅ **Environment Management** - Manage configuration without dashboard  
✅ **Schema Exploration** - Understand your data structure quickly  

## Next Steps

1. **Configure your AI tool** with the MCP server
2. **Test basic queries** to ensure connection works
3. **Explore your data** using natural language queries
4. **Debug issues** more efficiently with AI assistance
5. **Automate common tasks** through AI-driven function calls

The MCP integration makes your Convex backend much more accessible and debuggable! 🚀
