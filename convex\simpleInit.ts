import { mutation, query } from "./_generated/server";
import { v } from "convex/values";

// Simple initialization without complex permissions
export const initializeDefaultSettings = mutation({
  args: {},
  handler: async (ctx) => {
    // Check if user is authenticated
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const now = Date.now();
    const createdSettings: string[] = [];

    // Default settings to create
    const defaultSettings = [
      {
        key: "mobile_money_approval_threshold",
        value: 5000,
        description: "Maximum amount for mobile money requests that can be approved by shop support",
        category: "approval_thresholds",
      },
      {
        key: "bank_transfer_approval_threshold", 
        value: 10000,
        description: "Maximum amount for bank transfer requests that can be approved by shop support",
        category: "approval_thresholds",
      },
      {
        key: "whitelisted_domains",
        value: ["kmkentertainment.com", "mybet.africa"],
        description: "Email domains allowed for user registration",
        category: "security",
      },
    ];

    // Create settings if they don't exist
    for (const settingData of defaultSettings) {
      const existingSetting = await ctx.db
        .query("settings")
        .withIndex("by_key", (q) => q.eq("key", settingData.key))
        .first();

      if (!existingSetting) {
        // Find or create a user record for this identity
        let user = await ctx.db
          .query("users")
          .withIndex("by_workos_id", (q) => q.eq("workosId", identity.subject))
          .first();

        if (!user) {
          // Create a temporary user record for system initialization
          const userId = await ctx.db.insert("users", {
            workosId: identity.subject,
            email: identity.email || "<EMAIL>",
            firstName: "System",
            lastName: "Admin",
            isActive: true,
            createdAt: now,
            updatedAt: now,
          });
          user = await ctx.db.get(userId);
        }

        await ctx.db.insert("settings", {
          ...settingData,
          isSystem: true,
          updatedBy: user!._id,
          updatedAt: now,
          createdAt: now,
        });
        createdSettings.push(settingData.key);
      }
    }

    return {
      success: true,
      createdSettings,
      message: `Created ${createdSettings.length} default settings`
    };
  },
});

// Simple settings getter without permissions
export const getBasicSettings = query({
  args: {},
  handler: async (ctx) => {
    // Check if user is authenticated
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Authentication required");
    }

    const settings = await ctx.db.query("settings").collect();
    
    // Convert to key-value object
    const settingsObject = settings.reduce((acc, setting) => {
      acc[setting.key] = setting.value;
      return acc;
    }, {} as Record<string, any>);

    // Add defaults if missing
    const defaultSettings = {
      mobile_money_approval_threshold: 5000,
      bank_transfer_approval_threshold: 10000,
      whitelisted_domains: ["kmkentertainment.com", "mybet.africa"],
      ...settingsObject,
    };

    return defaultSettings;
  },
});