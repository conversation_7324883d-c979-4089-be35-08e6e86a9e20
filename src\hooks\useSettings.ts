import { useQuery } from 'convex/react';
import { api } from '../../convex/_generated/api';
import { DEFAULT_THRESHOLDS, FILE_CONFIG } from '@/config';

/**
 * Custom hook for accessing application settings with proper fallbacks
 * 
 * This hook provides a consistent interface for accessing settings across the application.
 * It automatically handles fallbacks to default values when settings are not available.
 */
export const useSettings = () => {
  const settingsQuery = useQuery(api.settings.getPublicSettings);
  
  // Return settings with proper fallbacks
  return {
    // Loading state
    isLoading: settingsQuery === undefined,
    
    // Raw settings object
    settings: settingsQuery,
    
    // Approval thresholds
    mobileMoneyThreshold: settingsQuery?.mobile_money_approval_threshold || DEFAULT_THRESHOLDS.MOBILE_MONEY,
    bankTransferThreshold: settingsQuery?.bank_transfer_approval_threshold || DEFAULT_THRESHOLDS.BANK_TRANSFER,
    
    // File upload limits
    maxFileSize: settingsQuery?.max_file_size || FILE_CONFIG.MAX_FILE_SIZE,
    maxAvatarSize: settingsQuery?.max_avatar_size || FILE_CONFIG.MAX_AVATAR_SIZE,
    maxTicketImageSize: settingsQuery?.max_ticket_image_size || FILE_CONFIG.MAX_TICKET_IMAGE_SIZE,
    
    // Security settings
    whitelistedDomains: settingsQuery?.whitelisted_domains || ['kmkentertainment.com', 'mybet.africa'],
    sessionTimeoutMinutes: settingsQuery?.session_timeout_minutes || 480, // 8 hours
    autoLogoutWarningMinutes: settingsQuery?.auto_logout_warning_minutes || 15,
    
    // Notification settings
    notificationRetentionDays: settingsQuery?.notification_retention_days || 30,
    
    // Helper functions
    canApproveAmount: (paymentMethod: string, amount: number, userRoles: string[]) => {
      const threshold = paymentMethod === 'mobile_money' 
        ? (settingsQuery?.mobile_money_approval_threshold || DEFAULT_THRESHOLDS.MOBILE_MONEY)
        : (settingsQuery?.bank_transfer_approval_threshold || DEFAULT_THRESHOLDS.BANK_TRANSFER);
      
      // Accounts role can approve any amount
      if (userRoles.includes('accounts')) {
        return true;
      }
      
      // Shop support can only approve within thresholds
      if (userRoles.includes('shop_support')) {
        return paymentMethod === 'mobile_money' && amount <= threshold;
      }
      
      return false;
    },
    
    isValidFileSize: (fileSize: number, fileType: 'general' | 'avatar' | 'ticket') => {
      const maxSize = fileType === 'avatar' 
        ? (settingsQuery?.max_avatar_size || FILE_CONFIG.MAX_AVATAR_SIZE)
        : fileType === 'ticket'
        ? (settingsQuery?.max_ticket_image_size || FILE_CONFIG.MAX_TICKET_IMAGE_SIZE)
        : (settingsQuery?.max_file_size || FILE_CONFIG.MAX_FILE_SIZE);
      
      return fileSize <= maxSize;
    },
    
    isWhitelistedDomain: (email: string) => {
      const domains = settingsQuery?.whitelisted_domains || ['kmkentertainment.com', 'mybet.africa'];
      const emailDomain = email.split('@')[1]?.toLowerCase();
      return domains.some(domain => domain.toLowerCase() === emailDomain);
    }
  };
};

/**
 * Hook for accessing settings with metadata (admin interface)
 */
export const useSettingsWithMetadata = (category?: string, isSystem?: boolean) => {
  const settingsQuery = useQuery(api.settings.getSettingsWithMetadata, {
    category,
    isSystem
  });
  
  return {
    isLoading: settingsQuery === undefined,
    settings: settingsQuery || []
  };
};

/**
 * Legacy hook for backward compatibility
 * @deprecated Use useSettings() instead
 */
export const useThresholds = () => {
  const { mobileMoneyThreshold, bankTransferThreshold, isLoading } = useSettings();
  
  return {
    momoThreshold: mobileMoneyThreshold,
    bankThreshold: bankTransferThreshold,
    isLoading
  };
};
