// Script to test our WorkOS webhook endpoint with the correct URL
const fetch = require('node-fetch');
const crypto = require('crypto');

const WEBHOOK_URL = 'https://efficient-toucan-547.convex.cloud/http/workos-webhook';
const WEBHOOK_SECRET = 'test_secret_for_development'; // This should match your Convex env var

// Create a test webhook payload
const createTestPayload = (eventType = 'user.created') => {
  const timestamp = Date.now();
  const payload = {
    event: eventType,
    data: {
      id: 'user_test_' + timestamp,
      email: `test-${timestamp}@example.com`,
      first_name: 'Test',
      last_name: 'User',
      email_verified: true,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    },
    created_at: new Date().toISOString()
  };
  
  const payloadString = JSON.stringify(payload);
  
  // Create signature according to WorkOS format
  const signatureString = timestamp + '.' + payloadString;
  const signature = crypto
    .createHmac('sha256', WEBHOOK_SECRET)
    .update(signatureString, 'utf8')
    .digest('hex');
  
  console.log('Signature string:', signatureString);
  console.log('Computed signature:', signature);
  
  const workosSignature = `t=${timestamp},v1=${signature}`;
  
  return {
    payload: payloadString,
    signature: workosSignature,
    timestamp
  };
};

async function testWebhook() {
  try {
    console.log('Testing WorkOS webhook endpoint...');
    console.log('Webhook URL:', WEBHOOK_URL);
    
    const testData = createTestPayload('user.created');
    
    console.log('Sending test payload...');
    console.log('Signature:', testData.signature);
    
    const response = await fetch(WEBHOOK_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'WorkOS-Signature': testData.signature,
        'User-Agent': 'WorkOS-Webhook/1.0'
      },
      body: testData.payload
    });
    
    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const responseText = await response.text();
    console.log('Response body:', responseText);
    
    if (response.ok) {
      console.log('✅ Webhook test successful!');
    } else {
      console.log('❌ Webhook test failed');
    }
    
  } catch (error) {
    console.error('Error testing webhook:', error);
  }
}

testWebhook();