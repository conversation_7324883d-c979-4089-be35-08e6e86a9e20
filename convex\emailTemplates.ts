// Email templates for different notification types

export interface EmailTemplateData {
  requestNumber: string;
  amount: number;
  shopName: string;
  areaName: string;
  requesterName: string;
  paymentMethod: string;
  networkProvider?: string;
  rejectionReason?: string;
  approverName?: string;
  requestUrl: string;
  systemName: string;
}

export function getRequestSubmittedTemplate(data: EmailTemplateData): { subject: string; html: string; text: string } {
  const subject = `New Request Submitted - ${data.requestNumber}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #2563eb; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { background: #f8fafc; padding: 20px; border: 1px solid #e2e8f0; }
        .footer { background: #64748b; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 14px; }
        .details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #2563eb; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 15px 0; }
        .amount { font-size: 24px; font-weight: bold; color: #059669; }
        .label { font-weight: bold; color: #475569; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>📋 New Request Submitted</h1>
      </div>
      
      <div class="content">
        <p>A new payment request has been submitted and requires your attention.</p>
        
        <div class="details">
          <p><span class="label">Request Number:</span> ${data.requestNumber}</p>
          <p><span class="label">Amount:</span> <span class="amount">GHS ${data.amount.toLocaleString()}</span></p>
          <p><span class="label">Shop:</span> ${data.shopName}</p>
          <p><span class="label">Area:</span> ${data.areaName}</p>
          <p><span class="label">Requested By:</span> ${data.requesterName}</p>
          <p><span class="label">Payment Method:</span> ${data.paymentMethod}</p>
          ${data.networkProvider ? `<p><span class="label">Network:</span> ${data.networkProvider}</p>` : ''}
        </div>
        
        <p>Please review this request and take appropriate action.</p>
        
        <a href="${data.requestUrl}" class="button">Review Request</a>
      </div>
      
      <div class="footer">
        <p>${data.systemName} - Automated Email Notification</p>
      </div>
    </body>
    </html>
  `;
  
  const text = `
New Request Submitted - ${data.requestNumber}

A new payment request has been submitted and requires your attention.

Request Details:
- Request Number: ${data.requestNumber}
- Amount: GHS ${data.amount.toLocaleString()}
- Shop: ${data.shopName}
- Area: ${data.areaName}
- Requested By: ${data.requesterName}
- Payment Method: ${data.paymentMethod}
${data.networkProvider ? `- Network: ${data.networkProvider}` : ''}

Please review this request at: ${data.requestUrl}

${data.systemName} - Automated Email Notification
  `;
  
  return { subject, html, text };
}

export function getRequestApprovedTemplate(data: EmailTemplateData): { subject: string; html: string; text: string } {
  const subject = `Request Approved - ${data.requestNumber}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #059669; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { background: #f0fdf4; padding: 20px; border: 1px solid #bbf7d0; }
        .footer { background: #64748b; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 14px; }
        .details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #059669; }
        .button { display: inline-block; background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 15px 0; }
        .amount { font-size: 24px; font-weight: bold; color: #059669; }
        .label { font-weight: bold; color: #475569; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>✅ Request Approved</h1>
      </div>
      
      <div class="content">
        <p>Great news! Your payment request has been approved.</p>
        
        <div class="details">
          <p><span class="label">Request Number:</span> ${data.requestNumber}</p>
          <p><span class="label">Amount:</span> <span class="amount">GHS ${data.amount.toLocaleString()}</span></p>
          <p><span class="label">Payment Method:</span> ${data.paymentMethod}</p>
          ${data.networkProvider ? `<p><span class="label">Network:</span> ${data.networkProvider}</p>` : ''}
          ${data.approverName ? `<p><span class="label">Approved By:</span> ${data.approverName}</p>` : ''}
        </div>
        
        <p>Your request has been processed and payment will be made according to the standard processing times.</p>
        
        <a href="${data.requestUrl}" class="button">View Request Details</a>
      </div>
      
      <div class="footer">
        <p>${data.systemName} - Automated Email Notification</p>
      </div>
    </body>
    </html>
  `;
  
  const text = `
Request Approved - ${data.requestNumber}

Great news! Your payment request has been approved.

Request Details:
- Request Number: ${data.requestNumber}
- Amount: GHS ${data.amount.toLocaleString()}
- Payment Method: ${data.paymentMethod}
${data.networkProvider ? `- Network: ${data.networkProvider}` : ''}
${data.approverName ? `- Approved By: ${data.approverName}` : ''}

Your request has been processed and payment will be made according to the standard processing times.

View request details at: ${data.requestUrl}

${data.systemName} - Automated Email Notification
  `;
  
  return { subject, html, text };
}

export function getRequestRejectedTemplate(data: EmailTemplateData): { subject: string; html: string; text: string } {
  const subject = `Request Rejected - ${data.requestNumber}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc2626; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { background: #fef2f2; padding: 20px; border: 1px solid #fecaca; }
        .footer { background: #64748b; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 14px; }
        .details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #dc2626; }
        .button { display: inline-block; background: #dc2626; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 15px 0; }
        .amount { font-size: 24px; font-weight: bold; color: #dc2626; }
        .label { font-weight: bold; color: #475569; }
        .reason { background: #fee2e2; padding: 10px; border-radius: 4px; margin: 10px 0; border-left: 3px solid #dc2626; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>❌ Request Rejected</h1>
      </div>
      
      <div class="content">
        <p>Your payment request has been rejected and requires your attention.</p>
        
        <div class="details">
          <p><span class="label">Request Number:</span> ${data.requestNumber}</p>
          <p><span class="label">Amount:</span> <span class="amount">GHS ${data.amount.toLocaleString()}</span></p>
          <p><span class="label">Payment Method:</span> ${data.paymentMethod}</p>
          ${data.networkProvider ? `<p><span class="label">Network:</span> ${data.networkProvider}</p>` : ''}
        </div>
        
        ${data.rejectionReason ? `
        <div class="reason">
          <p><strong>Rejection Reason:</strong></p>
          <p>${data.rejectionReason}</p>
        </div>
        ` : ''}
        
        <p>You can review the request details and resubmit with corrections if needed.</p>
        
        <a href="${data.requestUrl}" class="button">Review & Resubmit</a>
      </div>
      
      <div class="footer">
        <p>${data.systemName} - Automated Email Notification</p>
      </div>
    </body>
    </html>
  `;
  
  const text = `
Request Rejected - ${data.requestNumber}

Your payment request has been rejected and requires your attention.

Request Details:
- Request Number: ${data.requestNumber}
- Amount: GHS ${data.amount.toLocaleString()}
- Payment Method: ${data.paymentMethod}
${data.networkProvider ? `- Network: ${data.networkProvider}` : ''}

${data.rejectionReason ? `Rejection Reason: ${data.rejectionReason}` : ''}

You can review the request details and resubmit with corrections if needed.

Review request at: ${data.requestUrl}

${data.systemName} - Automated Email Notification
  `;
  
  return { subject, html, text };
}

export function getRequestResubmittedTemplate(data: EmailTemplateData): { subject: string; html: string; text: string } {
  const subject = `Request Resubmitted - ${data.requestNumber}`;
  
  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${subject}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #7c3aed; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
        .content { background: #faf5ff; padding: 20px; border: 1px solid #e9d5ff; }
        .footer { background: #64748b; color: white; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 14px; }
        .details { background: white; padding: 15px; border-radius: 6px; margin: 15px 0; border-left: 4px solid #7c3aed; }
        .button { display: inline-block; background: #7c3aed; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 15px 0; }
        .amount { font-size: 24px; font-weight: bold; color: #7c3aed; }
        .label { font-weight: bold; color: #475569; }
      </style>
    </head>
    <body>
      <div class="header">
        <h1>🔄 Request Resubmitted</h1>
      </div>
      
      <div class="content">
        <p>A payment request has been resubmitted with updates and requires your review.</p>
        
        <div class="details">
          <p><span class="label">Request Number:</span> ${data.requestNumber}</p>
          <p><span class="label">Amount:</span> <span class="amount">GHS ${data.amount.toLocaleString()}</span></p>
          <p><span class="label">Shop:</span> ${data.shopName}</p>
          <p><span class="label">Area:</span> ${data.areaName}</p>
          <p><span class="label">Resubmitted By:</span> ${data.requesterName}</p>
          <p><span class="label">Payment Method:</span> ${data.paymentMethod}</p>
          ${data.networkProvider ? `<p><span class="label">Network:</span> ${data.networkProvider}</p>` : ''}
        </div>
        
        <p>Please review the updated request and take appropriate action.</p>
        
        <a href="${data.requestUrl}" class="button">Review Updated Request</a>
      </div>
      
      <div class="footer">
        <p>${data.systemName} - Automated Email Notification</p>
      </div>
    </body>
    </html>
  `;
  
  const text = `
Request Resubmitted - ${data.requestNumber}

A payment request has been resubmitted with updates and requires your review.

Request Details:
- Request Number: ${data.requestNumber}
- Amount: GHS ${data.amount.toLocaleString()}
- Shop: ${data.shopName}
- Area: ${data.areaName}
- Resubmitted By: ${data.requesterName}
- Payment Method: ${data.paymentMethod}
${data.networkProvider ? `- Network: ${data.networkProvider}` : ''}

Please review the updated request at: ${data.requestUrl}

${data.systemName} - Automated Email Notification
  `;
  
  return { subject, html, text };
}