// Script to set up WorkOS webhook endpoint
const fetch = require('node-fetch');

const WORKOS_API_KEY = process.env.WORKOS_API_KEY || 'sk_test_a2V5XzAxSk0wODZOQjk5WUQwMjkwMzFTSk1aWVo4LDFvTVcwbWNrdGVEVEZnZUdBNmVjZDBKcWs';
const WEBHOOK_ENDPOINT = process.env.WEBHOOK_ENDPOINT || 'https://efficient-toucan-547.convex.cloud/workos-webhook';

async function createWebhook() {
  try {
    console.log('Creating WorkOS webhook...');
    
    // First, check if we can list users to verify API key works
    const usersResponse = await fetch('https://api.workos.com/user_management/users', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${WORKOS_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    if (!usersResponse.ok) {
      const errorText = await usersResponse.text();
      console.error('Error accessing WorkOS API:', usersResponse.status, errorText);
      return;
    }
    
    const usersData = await usersResponse.json();
    console.log(`Successfully connected to WorkOS API. Found ${usersData.data.length} users.`);
    
    // Now try to create a webhook endpoint
    // Note: The exact API endpoint might vary based on WorkOS documentation
    // This is a best guess based on common API patterns
    const webhookResponse = await fetch('https://api.workos.com/webhooks/endpoints', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${WORKOS_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        endpoint_url: WEBHOOK_ENDPOINT,
        events: [
          'user.created',
          'user.updated',
          'user.deleted',
          'organization.created',
          'organization.updated',
          'organization.deleted',
          'organization_membership.created',
          'organization_membership.updated',
          'organization_membership.deleted'
        ]
      })
    });
    
    if (!webhookResponse.ok) {
      const errorText = await webhookResponse.text();
      console.error('Error creating webhook:', webhookResponse.status, errorText);
      
      // Try alternative endpoint
      console.log('Trying alternative endpoint...');
      const altWebhookResponse = await fetch('https://api.workos.com/webhooks', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${WORKOS_API_KEY}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          url: WEBHOOK_ENDPOINT,
          events: [
            'user.created',
            'user.updated',
            'user.deleted',
            'organization.created',
            'organization.updated',
            'organization.deleted',
            'organization_membership.created',
            'organization_membership.updated',
            'organization_membership.deleted'
          ]
        })
      });
      
      if (!altWebhookResponse.ok) {
        const altErrorText = await altWebhookResponse.text();
        console.error('Error with alternative endpoint:', altWebhookResponse.status, altErrorText);
        console.log('Please check the WorkOS documentation for the correct webhook creation endpoint.');
        return;
      }
      
      const webhookData = await altWebhookResponse.json();
      console.log('Successfully created webhook:', webhookData);
      return;
    }
    
    const webhookData = await webhookResponse.json();
    console.log('Successfully created webhook:', webhookData);
    
  } catch (error) {
    console.error('Error setting up webhook:', error);
  }
}

createWebhook();