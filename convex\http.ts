import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { auth } from "./auth.js";
import { workosWebhook } from "./webhooks.js";
import { webhook } from "./simpleWebhook.js";

const http = httpRouter();

// Add the auth HTTP routes
auth.addHttpRoutes(http);

// Simple test endpoint
http.route({
  path: "/test",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    return new Response("Test endpoint working!", { 
      status: 200,
      headers: { "Content-Type": "text/plain" }
    });
  }),
});

// Simple webhook endpoint for testing
http.route({
  path: "/simple-webhook",
  method: "POST",
  handler: webhook,
});

// Add WorkOS webhook endpoint
http.route({
  path: "/workos-webhook",
  method: "POST",
  handler: workosWebhook,
});

// Also add a GET route for testing
http.route({
  path: "/workos-webhook",
  method: "GET",
  handler: httpAction(async (ctx, request) => {
    return new Response("WorkOS Webhook Endpoint Ready", { 
      status: 200,
      headers: { "Content-Type": "text/plain" }
    });
  }),
});

export default http;