import { api } from '../../convex/_generated/api';
import { Id } from '../../convex/_generated/dataModel';
import { useQuery, useMutation } from 'convex/react';
import { convex } from '@/lib/convex';
import { Area } from '@/lib/types';

/**
 * Fetch all areas
 */
export async function getAreas(): Promise<Area[]> {
  try {
    const areas = await convex.query(api.areas.getAreas, {});
    return areas.map(mapConvexAreaToArea);
  } catch (error) {
    console.error('Error fetching areas:', error);
    throw error;
  }
}

/**
 * Fetch an area by ID
 */
export async function getAreaById(id: string): Promise<Area | null> {
  try {
    const area = await convex.query(api.areas.getArea, { 
      areaId: id as Id<"areas"> 
    });
    
    if (!area) {
      return null;
    }
    
    return mapConvexAreaToArea(area);
  } catch (error) {
    console.error('Error fetching area by ID:', error);
    throw error;
  }
}

/**
 * Create a new area
 */
export async function createArea(name: string): Promise<Area> {
  try {
    const areaId = await convex.mutation(api.areas.createArea, {
      name,
    });

    const createdArea = await convex.query(api.areas.getArea, { 
      areaId 
    });
    
    if (!createdArea) {
      throw new Error('Failed to retrieve created area');
    }
    
    return mapConvexAreaToArea(createdArea);
  } catch (error) {
    console.error('Error creating area:', error);
    throw error;
  }
}

/**
 * Update an area
 */
export async function updateArea(id: string, name: string): Promise<Area> {
  try {
    await convex.mutation(api.areas.updateArea, {
      areaId: id as Id<"areas">,
      name,
    });

    const updatedArea = await convex.query(api.areas.getArea, { 
      areaId: id as Id<"areas"> 
    });
    
    if (!updatedArea) {
      throw new Error('Failed to retrieve updated area');
    }
    
    return mapConvexAreaToArea(updatedArea);
  } catch (error) {
    console.error('Error updating area:', error);
    throw error;
  }
}

/**
 * Delete an area
 */
export async function deleteArea(id: string): Promise<void> {
  try {
    await convex.mutation(api.areas.deleteArea, {
      areaId: id as Id<"areas">,
    });
  } catch (error) {
    console.error('Error deleting area:', error);
    throw error;
  }
}

/**
 * Get users assigned to area
 */
export async function getAreaUsers(areaId: string): Promise<any[]> {
  try {
    return await convex.query(api.areas.getAreaUsers, { 
      areaId: areaId as Id<"areas"> 
    });
  } catch (error) {
    console.error('Error fetching area users:', error);
    throw error;
  }
}

/**
 * Get shops in area
 */
export async function getAreaShops(areaId: string): Promise<any[]> {
  try {
    return await convex.query(api.areas.getAreaShops, { 
      areaId: areaId as Id<"areas"> 
    });
  } catch (error) {
    console.error('Error fetching area shops:', error);
    throw error;
  }
}

/**
 * Get area statistics
 */
export async function getAreaStats(filters?: {
  areaId?: string;
  startDate?: Date;
  endDate?: Date;
}): Promise<any> {
  try {
    return await convex.query(api.areas.getAreaStats, {
      areaId: filters?.areaId as Id<"areas"> | undefined,
      startDate: filters?.startDate?.getTime(),
      endDate: filters?.endDate?.getTime(),
    });
  } catch (error) {
    console.error('Error fetching area stats:', error);
    throw error;
  }
}

/**
 * Get areas accessible to current user
 */
export async function getAccessibleAreas(): Promise<Area[]> {
  try {
    const areas = await convex.query(api.areas.getAccessibleAreas, {});
    return areas.map(mapConvexAreaToArea);
  } catch (error) {
    console.error('Error fetching accessible areas:', error);
    throw error;
  }
}

/**
 * Helper function to map a Convex area to an Area object
 */
function mapConvexAreaToArea(convexArea: any): Area {
  return {
    id: convexArea._id,
    name: convexArea.name,
  };
}

// React hooks for components
export const useAreas = (filters?: any) => {
  return useQuery(api.areas.getAreas, filters || {});
};

export const useArea = (areaId: string) => {
  return useQuery(api.areas.getArea, { 
    areaId: areaId as Id<"areas"> 
  });
};

export const useAccessibleAreas = () => {
  return useQuery(api.areas.getAccessibleAreas, {});
};

export const useAreaUsers = (areaId: string) => {
  return useQuery(api.areas.getAreaUsers, { 
    areaId: areaId as Id<"areas"> 
  });
};

export const useAreaShops = (areaId: string) => {
  return useQuery(api.areas.getAreaShops, { 
    areaId: areaId as Id<"areas"> 
  });
};

export const useCreateArea = () => {
  return useMutation(api.areas.createArea);
};

export const useUpdateArea = () => {
  return useMutation(api.areas.updateArea);
};

export const useDeleteArea = () => {
  return useMutation(api.areas.deleteArea);
};
