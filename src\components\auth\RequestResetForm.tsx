import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { toast } from '@/components/ui/use-toast';
import { Mail } from 'lucide-react';

export const RequestResetForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast({
        title: "Reset link sent",
        description: "Please check your email for password reset instructions.",
      });
      setEmail('');
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to send reset link. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Logo section within the card */}
      <div className="flex justify-center pb-6 border-b border-gray-100">
        <img 
          src="/lovable-uploads/d809f2fb-430a-4759-9377-9438f8aacc4f.png"
          alt="MyBet Africa" 
          className="h-8 w-auto"
        />
      </div>

      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-semibold tracking-tight text-gray-900">
          Reset Password
        </h1>
        <p className="text-sm text-gray-500">
          Enter your email to receive a password reset link
        </p>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="space-y-4">
          <div className="relative">
            <Mail className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              type="email"
              placeholder="Email address"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              disabled={isLoading}
              className="pl-10 h-12 text-base bg-gray-50 border-gray-200 focus:border-primary focus:ring-primary"
            />
          </div>
        </div>

        <Button
          type="submit"
          disabled={isLoading}
          className="w-full h-12 text-base font-medium bg-primary hover:bg-primary-dark transition-colors"
        >
          {isLoading ? (
            <span className="flex items-center justify-center gap-2">
              <span className="h-4 w-4 border-2 border-white/30 border-t-white animate-spin rounded-full" />
              Sending...
            </span>
          ) : (
            'Send Reset Link'
          )}
        </Button>

        <div className="text-center pt-2">
          <Link 
            to="/login" 
            className="text-sm font-medium text-primary hover:text-primary-dark transition-colors"
          >
            Back to Login
          </Link>
        </div>
      </form>
    </div>
  );
};
