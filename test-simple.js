import { ConvexHttpClient } from "convex/browser";

const client = new ConvexHttpClient("https://efficient-toucan-547.convex.cloud");

async function testBasic() {
  console.log("🧪 Testing basic Convex functionality...");
  
  try {
    // Test health check first
    const health = await client.query("health:healthCheck", {});
    console.log("✅ Health check successful:", health);
    
    // Test settings table check
    const settingsCheck = await client.query("health:checkSettingsTable", {});
    console.log("✅ Settings table check:", settingsCheck);
    
    return true;
  } catch (error) {
    console.log("❌ Error:", error.message);
    return false;
  }
}

testBasic();