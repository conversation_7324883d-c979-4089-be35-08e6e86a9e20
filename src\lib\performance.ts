/**
 * Performance Monitoring Utilities
 * 
 * This module provides utilities for monitoring and optimizing app performance
 */

// Performance metrics interface
interface PerformanceMetrics {
  name: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  metadata?: Record<string, any>;
}

// Store for performance metrics
const performanceMetrics: PerformanceMetrics[] = [];

/**
 * Start measuring performance for a specific operation
 */
export function startPerformanceMeasure(name: string, metadata?: Record<string, any>): string {
  const id = `${name}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  
  performanceMetrics.push({
    name,
    startTime: performance.now(),
    metadata
  });
  
  return id;
}

/**
 * End performance measurement
 */
export function endPerformanceMeasure(name: string): number | null {
  const metric = performanceMetrics.find(m => m.name === name && !m.endTime);
  
  if (!metric) {
    console.warn(`Performance measure '${name}' not found or already ended`);
    return null;
  }
  
  metric.endTime = performance.now();
  metric.duration = metric.endTime - metric.startTime;
  
  // Log slow operations (> 100ms)
  if (metric.duration > 100) {
    console.warn(`Slow operation detected: ${name} took ${metric.duration.toFixed(2)}ms`, metric.metadata);
  }
  
  return metric.duration;
}

/**
 * Measure async function performance
 */
export async function measureAsync<T>(
  name: string, 
  fn: () => Promise<T>, 
  metadata?: Record<string, any>
): Promise<T> {
  const startTime = performance.now();
  
  try {
    const result = await fn();
    const duration = performance.now() - startTime;
    
    // Log performance
    if (duration > 100) {
      console.warn(`Slow async operation: ${name} took ${duration.toFixed(2)}ms`, metadata);
    } else if (process.env.NODE_ENV === 'development') {
      console.log(`${name} completed in ${duration.toFixed(2)}ms`);
    }
    
    return result;
  } catch (error) {
    const duration = performance.now() - startTime;
    console.error(`Failed operation: ${name} failed after ${duration.toFixed(2)}ms`, error);
    throw error;
  }
}

/**
 * Debounce function to prevent excessive calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function to limit call frequency
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}

/**
 * Get performance metrics summary
 */
export function getPerformanceMetrics(): {
  totalMeasures: number;
  slowOperations: PerformanceMetrics[];
  averageDuration: number;
} {
  const completedMetrics = performanceMetrics.filter(m => m.duration !== undefined);
  const slowOperations = completedMetrics.filter(m => m.duration! > 100);
  const averageDuration = completedMetrics.length > 0 
    ? completedMetrics.reduce((sum, m) => sum + m.duration!, 0) / completedMetrics.length 
    : 0;
  
  return {
    totalMeasures: completedMetrics.length,
    slowOperations,
    averageDuration
  };
}

/**
 * Clear performance metrics (useful for testing)
 */
export function clearPerformanceMetrics(): void {
  performanceMetrics.length = 0;
}

/**
 * Monitor Core Web Vitals
 */
export function monitorWebVitals(): void {
  // Only run in browser
  if (typeof window === 'undefined') return;
  
  // Monitor Largest Contentful Paint (LCP)
  if ('PerformanceObserver' in window) {
    try {
      const observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        
        if (lastEntry.startTime > 2500) {
          console.warn(`Poor LCP: ${lastEntry.startTime.toFixed(2)}ms (should be < 2.5s)`);
        }
      });
      
      observer.observe({ entryTypes: ['largest-contentful-paint'] });
    } catch (error) {
      console.warn('Could not monitor LCP:', error);
    }
  }
  
  // Monitor First Input Delay (FID) - approximation
  let firstInputTime: number | null = null;
  
  const handleFirstInput = () => {
    if (firstInputTime === null) {
      firstInputTime = performance.now();
      
      // Remove listener after first input
      ['click', 'keydown', 'touchstart'].forEach(type => {
        document.removeEventListener(type, handleFirstInput, { capture: true });
      });
    }
  };
  
  ['click', 'keydown', 'touchstart'].forEach(type => {
    document.addEventListener(type, handleFirstInput, { capture: true });
  });
}

/**
 * Preload critical resources
 */
export function preloadCriticalResources(): void {
  if (typeof window === 'undefined') return;
  
  // Preload critical fonts
  const criticalFonts = [
    '/fonts/inter-var.woff2',
    // Add other critical fonts here
  ];
  
  criticalFonts.forEach(font => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.as = 'font';
    link.type = 'font/woff2';
    link.crossOrigin = 'anonymous';
    link.href = font;
    document.head.appendChild(link);
  });
}

/**
 * Optimize images with lazy loading
 */
export function optimizeImages(): void {
  if (typeof window === 'undefined') return;
  
  // Use Intersection Observer for lazy loading
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement;
          if (img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            imageObserver.unobserve(img);
          }
        }
      });
    });
    
    // Observe all images with data-src attribute
    document.querySelectorAll('img[data-src]').forEach(img => {
      imageObserver.observe(img);
    });
  }
}
